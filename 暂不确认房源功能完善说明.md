# 暂不确认房源功能完善说明

## 功能概述
根据参考项目 `/Users/<USER>/workspace/wyzgpt/wyzgpt-admin-h5/src/views/orderManagement/components/addForm.vue` 的实现逻辑，完善了BookingCreate.vue中"暂不确认房源"的交互逻辑和业务规则。

## 核心业务规则

### 1. 是否可退定金的自动设置
- **选择具体房源时**：自动设置为"否"（不可退）
- **勾选暂不确认房源时**：自动设置为"是"（可退）
- **可编辑性控制**：只有在暂不确认房源状态或未选择房源时才可编辑

### 2. 字段联动控制
- **勾选暂不确认房源时**：
  - 禁用意向业态选择器
  - 禁用意向房源选择器
  - 清空已选择的业态和房源数据
  - 自动设置为可退定金

- **取消勾选暂不确认房源时**：
  - 重新启用意向业态选择器
  - 重新启用意向房源选择器
  - 自动设置为不可退定金

### 3. 表单验证逻辑
- **暂不确认房源时**：跳过意向业态和意向房源的必填验证
- **正常状态时**：意向业态和意向房源为必填项

## 实现细节

### 1. 新增计算属性
```javascript
// 计算属性：是否可退字段是否可编辑
const isRefundableEditable = computed(() => {
    return formData.isUncertainRoom || !formData.roomId
})
```

### 2. 核心处理函数
```javascript
// 处理暂不确认房源变化
const handleUncertainRoomChange = (checked: boolean) => {
    if (checked) {
        // 勾选时：清空选择，设置可退
        formData.isRefundable = '是'
        formData.roomId = ''
        formData.roomName = ''
        formData.propertyTypeCode = ''
        formData.propertyTypeName = ''
        formData.propertyType = ''
        businessTypeValue.value = [0, 0]
    } else {
        // 取消勾选时：重新启用选择器，设置不可退
        formData.isRefundable = '否'
        initBusinessTypeColumns()
    }
}
```

### 3. 字段禁用控制
```vue
<!-- 意向业态 -->
<van-field 
    :required="!formData.isUncertainRoom"
    :disabled="formData.isUncertainRoom"
    :placeholder="formData.isUncertainRoom ? '暂不确认房源时无需选择' : '请选择'"
    @click="formData.isUncertainRoom ? null : (showBusinessTypePicker = true)">
</van-field>

<!-- 意向房源 -->
<van-field 
    :required="!formData.isUncertainRoom"
    :disabled="!formData.projectId || formData.isUncertainRoom"
    :placeholder="formData.isUncertainRoom ? '已勾选暂不确认房源' : (formData.projectId ? '请选择' : '请先选择项目')">
</van-field>

<!-- 是否退定金 -->
<van-field 
    :disabled="!isRefundableEditable"
    @click="isRefundableEditable ? (showRefundPicker = true) : null">
    <template #button>
        <van-tag v-if="!isRefundableEditable" color="#666">
            {{ formData.isUncertainRoom ? '暂不确认房源默认可退' : '选择房源后默认不可退' }}
        </van-tag>
    </template>
</van-field>
```

### 4. 表单验证更新
```javascript
const validateForm = () => {
    // 基础字段验证...
    
    // 暂不确认房源时，跳过业态和房源验证
    if (!formData.isUncertainRoom) {
        if (!formData.propertyTypeCode && !formData.propertyType) {
            showToast('请选择意向业态')
            return false
        }
        if (!formData.roomName) {
            showToast('请选择意向房源或勾选暂不确认房源')
            return false
        }
    }
    
    // 其他验证...
}
```

### 5. 数据提交处理
```javascript
const handleSave = async () => {
    const params = {
        ...formData,
        isSubmit: 0,
        isRefundable: formData.isRefundable === '是' ? 1 : 0,
        // 暂不确认房源时的特殊处理
        roomName: formData.isUncertainRoom ? '暂不确认房源' : formData.roomName,
        roomId: formData.isUncertainRoom ? '' : formData.roomId
    }
    // 提交逻辑...
}
```

## 用户交互流程

### 正常选择房源流程
1. 选择项目 → 选择意向业态 → 选择意向房源
2. 选择房源后自动设置为"不可退定金"
3. 是否退定金字段不可编辑

### 暂不确认房源流程
1. 选择项目 → 勾选"暂不确认房源"
2. 意向业态和意向房源字段自动禁用
3. 自动设置为"可退定金"
4. 提交时roomName设置为"暂不确认房源"

### 状态切换流程
- **从正常状态切换到暂不确认**：清空已选数据，禁用相关字段
- **从暂不确认切换到正常状态**：重新启用字段，用户重新选择

## 界面提示优化

### 1. 占位符文本
- 暂不确认房源时显示："暂不确认房源时无需选择"
- 正常状态显示："请选择"

### 2. 状态标签
- 可退状态：显示"暂不确认房源默认可退"
- 不可退状态：显示"选择房源后默认不可退"

### 3. 交互反馈
- 勾选/取消暂不确认房源时显示相应的Toast提示
- 点击已禁用的房源字段时显示提示信息

## 数据兼容性

### 1. 接口字段映射
- `roomName`: 正常情况下为具体房源名称，暂不确认时为"暂不确认房源"
- `roomId`: 正常情况下为具体房源ID，暂不确认时为空字符串
- `isRefundable`: 转换为数字格式（1-是，0-否）

### 2. 后端识别
- 通过`roomName === '暂不确认房源'`来识别暂不确认房源的定单
- 暂不确认房源的定单`roomId`为空

## 测试要点

### 功能测试
1. ✅ 勾选暂不确认房源后，业态和房源字段正确禁用
2. ✅ 取消勾选后，字段正确重新启用
3. ✅ 选择具体房源后，自动设置为不可退
4. ✅ 暂不确认房源时，自动设置为可退
5. ✅ 表单验证在不同状态下正确工作

### 边界测试
1. ✅ 已选择房源后勾选暂不确认房源，数据正确清空
2. ✅ 暂不确认房源状态下点击房源字段，显示适当提示
3. ✅ 数据提交时字段值正确设置

### 兼容性测试
1. ✅ TypeScript类型检查通过
2. ✅ 构建成功无错误
3. ✅ 与现有API接口兼容

## 参考实现
本功能完全参考了管理端项目的实现逻辑：
- 文件路径：`/Users/<USER>/workspace/wyzgpt/wyzgpt-admin-h5/src/views/orderManagement/components/addForm.vue`
- 核心逻辑：`handleUnknownSourceChange`、`isRefundableEditable`计算属性
- 验证规则：条件验证逻辑
- 数据提交：特殊标识处理

确保了H5端与管理端的业务逻辑保持一致。 
# 房源数据结构适配更新说明

## 更新背景

根据您提供的实际接口返回数据，我们发现房源接口 `/business-rent-admin/room/roomOptions` 返回的是树形结构数据，而非之前预期的平铺列表。因此对代码进行了相应的适配更新。

## 实际数据结构分析

### 数据层级结构
```
Level 1: 项目根节点 (北滘-众创)
    ├─ Level 2: 地块 (A地块)
        ├─ Level 3: 建筑 (22栋, 23栋, 24栋, 26栋)
            └─ Level 4: 房源 (具体房间)
```

### 关键字段说明
- **level**: 标识数据层级 (1=项目, 2=地块, 3=建筑, 4=房源)
- **roomId**: 实际房源ID (仅在level=4时有值)
- **roomName**: 房源名称 (如"1001", "101"等)
- **buildingName**: 建筑名称 (如"26栋", "22栋")
- **propertyType**: 业态类型 (如"20"表示厂房)
- **rentStatus**: 租赁状态 ("0"=可租, "1"=已租)

## 代码更新内容

### 1. 接口定义更新 (`src/api/room.ts`)

#### 原接口定义
```typescript
export interface RoomOption {
    id: string
    code: string
    name: string
    buildingName?: string
    // ...
}
```

#### 更新后接口定义
```typescript
export interface RoomOption {
    id: string | null
    name: string | null
    parentId: string | null
    roomId: string | null          // 房源ID
    roomName: string | null        // 房源名称
    projectId: string | null
    projectName: string | null
    buildingId: string | null
    buildingName: string | null    // 建筑名称
    floorId: string | null
    floorName: string | null       // 楼层名称
    propertyType: string | null    // 业态类型
    rentStatus: string | null      // 租赁状态
    rentArea: number | null        // 租赁面积
    price: number | null           // 价格
    level: number                  // 层级
    children: RoomOption[] | null  // 子节点
    // ... 其他字段
}
```

### 2. 数据提取工具方法

新增 `extractRoomsFromTree` 方法，用于从树形结构中提取实际房源：

```typescript
export const extractRoomsFromTree = (treeData: RoomOption[]): RoomOption[] => {
    const rooms: RoomOption[] = []
    
    const traverse = (nodes: RoomOption[]) => {
        for (const node of nodes) {
            // level: 4 表示实际的房源
            if (node.level === 4 && node.roomId && node.roomName) {
                rooms.push(node)
            }
            
            // 递归处理子节点
            if (node.children && node.children.length > 0) {
                traverse(node.children)
            }
        }
    }
    
    traverse(treeData)
    return rooms
}
```

### 3. 组件逻辑更新 (`src/views/BookingCreate.vue`)

#### 数据处理更新
```typescript
// 原逻辑
if (res.code === 200 && Array.isArray(res.data?.list)) {
    roomList.value = res.data.list
    roomColumns.value = res.data.list.map(room => ({
        text: room.name || `${room.buildingName || ''}-${room.code || room.id}`,
        value: room.id
    }))
}

// 更新后逻辑
if (res.code === 200 && Array.isArray(res.data)) {
    // 从树形结构中提取实际房源（level: 4）
    const extractedRooms = extractRoomsFromTree(res.data)
    roomList.value = extractedRooms
    roomColumns.value = extractedRooms.map((room: RoomOption) => ({
        text: `${room.buildingName || ''}-${room.roomName || ''}` || room.roomId || '',
        value: room.roomId || ''
    }))
}
```

#### 房源确认逻辑更新
```typescript
// 原逻辑
const selectedRoom = roomList.value.find(room => room.id === selectedRoomId)

// 更新后逻辑
const selectedRoom = roomList.value.find(room => room.roomId === selectedRoomId)
```

## 功能特性

### 1. 房源显示格式
- 显示格式：`建筑名称-房源名称` (如"26栋-1001")
- 存储值：使用 `roomId` 作为唯一标识

### 2. 业态类型筛选
根据实际数据，支持按 `propertyType` 字段筛选房源：
- "20": 厂房类型
- 其他业态类型根据实际业务需求

### 3. 房源状态过滤
可以根据 `rentStatus` 筛选房源：
- "0": 可租房源
- "1": 已租房源

### 4. 详细信息展示
选择房源时可获取完整信息：
```typescript
{
    roomId: "21564",
    roomName: "1001",
    buildingName: "26栋",
    floorName: "10层",
    rentArea: 934.6,
    price: 11,
    rentStatus: "0"
}
```

## 使用示例

### 接口调用
```typescript
const params = {
    projectId: "306627ad-3f55-46cb-b51e-4970c50559da",
    businessType: "20", // 可选：业态类型筛选
    pageSize: 1000
}

const response = await getRoomOptions(params)
const rooms = extractRoomsFromTree(response.data)
```

### 房源选择器显示
```
26栋-1001
26栋-101
26栋-1101
22栋-101
22栋-201
23栋-1001
...
```

## 注意事项

1. **接口方法**: 使用 `POST` 方法调用 `/business-rent-admin/room/roomOptions`
2. **数据层级**: 只有 `level: 4` 的节点才是实际房源
3. **必要字段**: 房源必须同时具有 `roomId` 和 `roomName`
4. **业态筛选**: `businessType` 参数对应 `propertyType` 字段
5. **状态筛选**: 可根据需要添加 `rentStatus` 筛选逻辑

## 扩展功能建议

1. **状态筛选**: 只显示可租房源 (`rentStatus: "0"`)
2. **面积显示**: 在房源名称中显示面积信息
3. **价格显示**: 显示房源租金价格
4. **楼层分组**: 按楼层对房源进行分组显示
5. **缓存优化**: 缓存已获取的房源树形数据

此次更新确保了代码与实际接口数据结构完全匹配，提供了更准确的房源选择功能。 
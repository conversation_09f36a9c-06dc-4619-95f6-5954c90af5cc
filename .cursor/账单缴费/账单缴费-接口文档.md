---
title: 万洋
language_tabs:
  - shell: Shell
  - http: HTTP
  - javascript: JavaScript
  - ruby: Ruby
  - python: Python
  - php: PHP
  - java: Java
  - go: Go
toc_footers: []
includes: []
search: true
code_clipboard: true
highlight_theme: darkula
headingLevel: 2
generator: "@tarslib/widdershins v4.0.30"

---

# 万洋

Base URLs:

# Authentication

# 租赁中心前端/C端合同接口

<a id="opIdpay"></a>

## POST 合同支付接口

POST /contract/pay

根据合同ID和支付明细生成支付链接

> Body 请求参数

```json
{
  "contractId": "string",
  "amount": 0,
  "costList": [
    {
      "costId": "string",
      "payAmount": 0
    }
  ]
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[ContractPaymentDto](#schemacontractpaymentdto)| 否 |none|

> 返回示例

> 200 Response

```
"string"
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|default response|string|

<a id="opIddetail"></a>

## GET 合同详情接口

GET /contract/detail

根据合同ID查询合同基础信息和未收齐账单信息

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|id|query|string| 是 |合同ID|
|Authorization|header|string| 否 |none|

> 返回示例

> 200 Response

```
{"contract":{"id":"string","projectId":"string","projectName":"string","contractNo":"string","unionId":"string","version":"string","isCurrent":true,"isLatest":true,"status":0,"statusTwo":0,"approveStatus":0,"operateType":0,"contractType":0,"ourSigningParty":"string","customerName":"string","unenterNum":0,"signWay":0,"signType":0,"originId":"string","changeFromId":"string","landUsage":"string","signerId":"string","signerName":"string","ownerId":"string","ownerName":"string","contractMode":0,"paperContractNo":"string","signDate":"2019-08-24T14:15:22Z","handoverDate":"2019-08-24T14:15:22Z","contractPurpose":0,"dealChannel":0,"assistantId":"string","assistantName":"string","rentYear":0,"rentMonth":0,"rentDay":0,"startDate":"2019-08-24T14:15:22Z","endDate":"2019-08-24T14:15:22Z","effectDate":"2019-08-24T14:15:22Z","bookingRelType":0,"bondReceivableDate":"string","bondReceivableType":0,"bondPriceType":0,"bondPrice":0,"chargeWay":0,"rentReceivableDate":"string","rentReceivableType":0,"rentTicketPeriod":0,"rentPayPeriod":0,"increaseGap":0,"increaseRate":0,"increaseRule":"string","estimateRevenue":0,"percentageType":0,"fixedPercentage":0,"stepPercentage":"string","revenueType":0,"isIncludePm":true,"pmUnitPrice":0,"pmMonthlyPrice":0,"totalPrice":0,"bizTypeId":"string","bizTypeName":"string","lesseeBrand":"string","businessCategory":"string","openDate":"2019-08-24T14:15:22Z","fireRiskCategory":0,"sprinklerSystem":0,"factoryEngaged":"string","deliverDate":"2019-08-24T14:15:22Z","parkingSpaceType":0,"hasParkingFee":true,"parkingFeeAmount":0,"otherInfo":"string","contractAttachments":"string","signAttachments":"string","attachmentsPlan":"string","isUploadSignature":true,"changeType":"string","processId":"string","changeDate":"2019-08-24T14:15:22Z","changeAttachments":"string","isSignatureConfirm":true,"isPaperConfirm":true,"isFinish":true,"createByName":"string","createTime":"2019-08-24T14:15:22Z","updateByName":"string","updateTime":"2019-08-24T14:15:22Z","isDel":true,"roomName":"string","terminateDate":"2019-08-24T14:15:22Z","customer":{"id":"string","contractId":"string","customerId":"string","customerType":0,"customerName":"string","address":"string","phone":"string","idType":"string","idNumber":"string","isEmployee":true,"creditCode":"string","contactName":"string","contactPhone":"string","contactIdNumber":"string","legalName":"string","paymentAccount":"string","guarantorName":"string","guarantorPhone":"string","guarantorIdType":"string","guarantorIdNumber":"string","guarantorAddress":"string","guarantorIdFront":"string","guarantorIdBack":"string","invoiceTitle":"string","invoiceTaxNumber":"string","invoiceAddress":"string","invoicePhone":"string","invoiceBankName":"string","invoiceAccountNumber":"string","createByName":"string","updateByName":"string","isDel":true},"bookings":[{"id":"string","contractId":"string","bookingId":"string","bookedRoom":"string","bookerName":"string","bookingReceivedAmount":0,"bookingPaymentDate":"2019-08-24T14:15:22Z","transferBondAmount":0,"transferRentAmount":0,"createByName":"string","updateByName":"string","isDel":true}],"fees":[{"id":"string","contractId":"string","feeType":0,"freeType":0,"freeRentMonth":0,"freeRentDay":0,"startDate":"2019-08-24T14:15:22Z","endDate":"2019-08-24T14:15:22Z","isCharge":true,"remark":"string","createByName":"string","updateByName":"string","isDel":true}],"costs":[{"id":"string","contractId":"string","costType":0,"startDate":"2019-08-24T14:15:22Z","endDate":"2019-08-24T14:15:22Z","period":0,"customerId":"string","customerName":"string","roomId":"string","roomName":"string","area":0,"subjectId":"string","subjectName":"string","receivableDate":"2019-08-24T14:15:22Z","unitPrice":0,"priceUnit":0,"totalAmount":0,"discountAmount":0,"actualReceivable":0,"receivedAmount":0,"isRevenue":true,"isDiscount":true,"percentageType":0,"fixedPercentage":0,"stepPercentage":"string","createByName":"string","updateByName":"string","isDel":true}],"rooms":[{"id":"string","contractId":"string","roomId":"string","roomName":"string","buildingName":"string","floorName":"string","parcelName":"string","stageName":"string","area":0,"standardUnitPrice":0,"bottomPrice":0,"priceUnit":0,"discount":0,"signedUnitPrice":0,"signedMonthlyPrice":0,"startDate":"2019-08-24T14:15:22Z","endDate":"2019-08-24T14:15:22Z","bondPriceType":0,"bondPrice":0,"createByName":"string","updateByName":"string","isDel":true}]},"unpaidBills":[{"id":"string","projectId":"string","projectName":"string","chargeType":0,"bizId":"string","refundId":"string","bizNo":"string","customerId":"string","customerName":"string","costType":0,"period":0,"subjectId":"string","subjectName":"string","startDate":"2019-08-24T14:15:22Z","endDate":"2019-08-24T14:15:22Z","receivableDate":"2019-08-24T14:15:22Z","status":0,"canPay":true,"isRevenueBill":true,"isRevenueGenerated":true,"totalAmount":0,"discountAmount":0,"actualReceivable":0,"receivedAmount":0,"carryoverAmount":0,"confirmStatus":0,"createByName":"string","updateByName":"string","isDel":true,"contractType":0,"roomName":"string","contractStatus":0,"contractStatusTwo":0}]}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|default response|[ContractDetailVo](#schemacontractdetailvo)|

# 数据模型

<h2 id="tocS_ContractPaymentDto">ContractPaymentDto</h2>

<a id="schemacontractpaymentdto"></a>
<a id="schema_ContractPaymentDto"></a>
<a id="tocScontractpaymentdto"></a>
<a id="tocscontractpaymentdto"></a>

```json
{
  "contractId": "string",
  "amount": 0,
  "costList": [
    {
      "costId": "string",
      "payAmount": 0
    }
  ]
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|contractId|string|true|none|合同ID|合同ID|
|amount|number|true|none|支付金额|支付金额|
|costList|[[ContractPaymentItemDto](#schemacontractpaymentitemdto)]|false|none|账单支付明细列表|账单支付明细列表|

<h2 id="tocS_ContractPaymentItemDto">ContractPaymentItemDto</h2>

<a id="schemacontractpaymentitemdto"></a>
<a id="schema_ContractPaymentItemDto"></a>
<a id="tocScontractpaymentitemdto"></a>
<a id="tocscontractpaymentitemdto"></a>

```json
{
  "costId": "string",
  "payAmount": 0
}

```

账单支付明细列表

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|costId|string|true|none|账单ID|账单ID|
|payAmount|number|true|none|支付金额|支付金额|

<h2 id="tocS_ContractDetailVo">ContractDetailVo</h2>

<a id="schemacontractdetailvo"></a>
<a id="schema_ContractDetailVo"></a>
<a id="tocScontractdetailvo"></a>
<a id="tocscontractdetailvo"></a>

```json
{
  "contract": {
    "id": "string",
    "projectId": "string",
    "projectName": "string",
    "contractNo": "string",
    "unionId": "string",
    "version": "string",
    "isCurrent": true,
    "isLatest": true,
    "status": 0,
    "statusTwo": 0,
    "approveStatus": 0,
    "operateType": 0,
    "contractType": 0,
    "ourSigningParty": "string",
    "customerName": "string",
    "unenterNum": 0,
    "signWay": 0,
    "signType": 0,
    "originId": "string",
    "changeFromId": "string",
    "landUsage": "string",
    "signerId": "string",
    "signerName": "string",
    "ownerId": "string",
    "ownerName": "string",
    "contractMode": 0,
    "paperContractNo": "string",
    "signDate": "2019-08-24T14:15:22Z",
    "handoverDate": "2019-08-24T14:15:22Z",
    "contractPurpose": 0,
    "dealChannel": 0,
    "assistantId": "string",
    "assistantName": "string",
    "rentYear": 0,
    "rentMonth": 0,
    "rentDay": 0,
    "startDate": "2019-08-24T14:15:22Z",
    "endDate": "2019-08-24T14:15:22Z",
    "effectDate": "2019-08-24T14:15:22Z",
    "bookingRelType": 0,
    "bondReceivableDate": "string",
    "bondReceivableType": 0,
    "bondPriceType": 0,
    "bondPrice": 0,
    "chargeWay": 0,
    "rentReceivableDate": "string",
    "rentReceivableType": 0,
    "rentTicketPeriod": 0,
    "rentPayPeriod": 0,
    "increaseGap": 0,
    "increaseRate": 0,
    "increaseRule": "string",
    "estimateRevenue": 0,
    "percentageType": 0,
    "fixedPercentage": 0,
    "stepPercentage": "string",
    "revenueType": 0,
    "isIncludePm": true,
    "pmUnitPrice": 0,
    "pmMonthlyPrice": 0,
    "totalPrice": 0,
    "bizTypeId": "string",
    "bizTypeName": "string",
    "lesseeBrand": "string",
    "businessCategory": "string",
    "openDate": "2019-08-24T14:15:22Z",
    "fireRiskCategory": 0,
    "sprinklerSystem": 0,
    "factoryEngaged": "string",
    "deliverDate": "2019-08-24T14:15:22Z",
    "parkingSpaceType": 0,
    "hasParkingFee": true,
    "parkingFeeAmount": 0,
    "otherInfo": "string",
    "contractAttachments": "string",
    "signAttachments": "string",
    "attachmentsPlan": "string",
    "isUploadSignature": true,
    "changeType": "string",
    "processId": "string",
    "changeDate": "2019-08-24T14:15:22Z",
    "changeAttachments": "string",
    "isSignatureConfirm": true,
    "isPaperConfirm": true,
    "isFinish": true,
    "createByName": "string",
    "createTime": "2019-08-24T14:15:22Z",
    "updateByName": "string",
    "updateTime": "2019-08-24T14:15:22Z",
    "isDel": true,
    "roomName": "string",
    "terminateDate": "2019-08-24T14:15:22Z",
    "customer": {
      "id": "string",
      "contractId": "string",
      "customerId": "string",
      "customerType": 0,
      "customerName": "string",
      "address": "string",
      "phone": "string",
      "idType": "string",
      "idNumber": "string",
      "isEmployee": true,
      "creditCode": "string",
      "contactName": "string",
      "contactPhone": "string",
      "contactIdNumber": "string",
      "legalName": "string",
      "paymentAccount": "string",
      "guarantorName": "string",
      "guarantorPhone": "string",
      "guarantorIdType": "string",
      "guarantorIdNumber": "string",
      "guarantorAddress": "string",
      "guarantorIdFront": "string",
      "guarantorIdBack": "string",
      "invoiceTitle": "string",
      "invoiceTaxNumber": "string",
      "invoiceAddress": "string",
      "invoicePhone": "string",
      "invoiceBankName": "string",
      "invoiceAccountNumber": "string",
      "createByName": "string",
      "updateByName": "string",
      "isDel": true
    },
    "bookings": [
      {
        "id": "string",
        "contractId": "string",
        "bookingId": "string",
        "bookedRoom": "string",
        "bookerName": "string",
        "bookingReceivedAmount": 0,
        "bookingPaymentDate": "2019-08-24T14:15:22Z",
        "transferBondAmount": 0,
        "transferRentAmount": 0,
        "createByName": "string",
        "updateByName": "string",
        "isDel": true
      }
    ],
    "fees": [
      {
        "id": "string",
        "contractId": "string",
        "feeType": 0,
        "freeType": 0,
        "freeRentMonth": 0,
        "freeRentDay": 0,
        "startDate": "2019-08-24T14:15:22Z",
        "endDate": "2019-08-24T14:15:22Z",
        "isCharge": true,
        "remark": "string",
        "createByName": "string",
        "updateByName": "string",
        "isDel": true
      }
    ],
    "costs": [
      {
        "id": "string",
        "contractId": "string",
        "costType": 0,
        "startDate": "2019-08-24T14:15:22Z",
        "endDate": "2019-08-24T14:15:22Z",
        "period": 0,
        "customerId": "string",
        "customerName": "string",
        "roomId": "string",
        "roomName": "string",
        "area": 0,
        "subjectId": "string",
        "subjectName": "string",
        "receivableDate": "2019-08-24T14:15:22Z",
        "unitPrice": 0,
        "priceUnit": 0,
        "totalAmount": 0,
        "discountAmount": 0,
        "actualReceivable": 0,
        "receivedAmount": 0,
        "isRevenue": true,
        "isDiscount": true,
        "percentageType": 0,
        "fixedPercentage": 0,
        "stepPercentage": "string",
        "createByName": "string",
        "updateByName": "string",
        "isDel": true
      }
    ],
    "rooms": [
      {
        "id": "string",
        "contractId": "string",
        "roomId": "string",
        "roomName": "string",
        "buildingName": "string",
        "floorName": "string",
        "parcelName": "string",
        "stageName": "string",
        "area": 0,
        "standardUnitPrice": 0,
        "bottomPrice": 0,
        "priceUnit": 0,
        "discount": 0,
        "signedUnitPrice": 0,
        "signedMonthlyPrice": 0,
        "startDate": "2019-08-24T14:15:22Z",
        "endDate": "2019-08-24T14:15:22Z",
        "bondPriceType": 0,
        "bondPrice": 0,
        "createByName": "string",
        "updateByName": "string",
        "isDel": true
      }
    ]
  },
  "unpaidBills": [
    {
      "id": "string",
      "projectId": "string",
      "projectName": "string",
      "chargeType": 0,
      "bizId": "string",
      "refundId": "string",
      "bizNo": "string",
      "customerId": "string",
      "customerName": "string",
      "costType": 0,
      "period": 0,
      "subjectId": "string",
      "subjectName": "string",
      "startDate": "2019-08-24T14:15:22Z",
      "endDate": "2019-08-24T14:15:22Z",
      "receivableDate": "2019-08-24T14:15:22Z",
      "status": 0,
      "canPay": true,
      "isRevenueBill": true,
      "isRevenueGenerated": true,
      "totalAmount": 0,
      "discountAmount": 0,
      "actualReceivable": 0,
      "receivedAmount": 0,
      "carryoverAmount": 0,
      "confirmStatus": 0,
      "createByName": "string",
      "updateByName": "string",
      "isDel": true,
      "contractType": 0,
      "roomName": "string",
      "contractStatus": 0,
      "contractStatusTwo": 0
    }
  ]
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|contract|[ContractVo](#schemacontractvo)|false|none||合同基础信息|
|unpaidBills|[[CostVo](#schemacostvo)]|false|none|未收齐账单列表|未收齐的账单列表，按costType和receivableDate正序排列|

<h2 id="tocS_CostVo">CostVo</h2>

<a id="schemacostvo"></a>
<a id="schema_CostVo"></a>
<a id="tocScostvo"></a>
<a id="tocscostvo"></a>

```json
{
  "id": "string",
  "projectId": "string",
  "projectName": "string",
  "chargeType": 0,
  "bizId": "string",
  "refundId": "string",
  "bizNo": "string",
  "customerId": "string",
  "customerName": "string",
  "costType": 0,
  "period": 0,
  "subjectId": "string",
  "subjectName": "string",
  "startDate": "2019-08-24T14:15:22Z",
  "endDate": "2019-08-24T14:15:22Z",
  "receivableDate": "2019-08-24T14:15:22Z",
  "status": 0,
  "canPay": true,
  "isRevenueBill": true,
  "isRevenueGenerated": true,
  "totalAmount": 0,
  "discountAmount": 0,
  "actualReceivable": 0,
  "receivedAmount": 0,
  "carryoverAmount": 0,
  "confirmStatus": 0,
  "createByName": "string",
  "updateByName": "string",
  "isDel": true,
  "contractType": 0,
  "roomName": "string",
  "contractStatus": 0,
  "contractStatusTwo": 0
}

```

账单信息

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|string|false|none|主键ID|none|
|projectId|string|false|none|项目id|项目id|
|projectName|string|false|none|项目名称|项目名称|
|chargeType|integer(int32)|false|none|收费类别: 0-定单, 1-合同|收费类别: 0-定单, 1-合同|
|bizId|string|false|none|业务id, 定单类别对应定单id, 合同类别对应合同id|业务id, 定单类别对应定单id, 合同类别对应合同id|
|refundId|string|false|none|退款单id|退款单id|
|bizNo|string|false|none|业务单号|业务单号|
|customerId|string|false|none|承租人id|承租人id|
|customerName|string|false|none|承租人名称|承租人名称|
|costType|integer(int32)|false|none|账单类型: 1-保证金,2-租金,3-其他费用|账单类型: 1-保证金,2-租金,3-其他费用|
|period|integer(int32)|false|none|账单期数|账单期数|
|subjectId|string|false|none|收款用途id|收款用途id|
|subjectName|string|false|none|收款用途名称|收款用途名称|
|startDate|string(date-time)|false|none|开始日期|开始日期|
|endDate|string(date-time)|false|none|结束日期|结束日期|
|receivableDate|string(date-time)|false|none|应收日期|应收日期|
|status|integer(int32)|false|none|账单状态: 0-待收、1-待付、2-已收、3-已付|账单状态: 0-待收、1-待付、2-已收、3-已付|
|canPay|boolean|false|none|是否可收/付 0-否,1-是|是否可收/付 0-否,1-是|
|isRevenueBill|boolean|false|none|是否是营收抽点账单 0-否,1-是|是否是营收抽点账单 0-否,1-是|
|isRevenueGenerated|boolean|false|none|营收抽点金额是否已生成 0-否,1-是|营收抽点金额是否已生成 0-否,1-是|
|totalAmount|number|false|none|账单总额|账单总额|
|discountAmount|number|false|none|优惠金额|优惠金额|
|actualReceivable|number|false|none|实际应收金额|实际应收金额|
|receivedAmount|number|false|none|已收金额|已收金额|
|carryoverAmount|number|false|none|结转金额|结转金额|
|confirmStatus|integer(int32)|false|none|确认状态: 0-待确认, 1-已确认|确认状态: 0-待确认, 1-已确认|
|createByName|string|false|none|创建人姓名|创建人姓名|
|updateByName|string|false|none|更新人姓名|更新人姓名|
|isDel|boolean|false|none|0-否,1-是|0-否,1-是|
|contractType|integer(int32)|false|none|合同类型,0-非宿舍,1-宿舍,2-多经,3-日租房|合同类型,0-非宿舍,1-宿舍,2-多经,3-日租房|
|roomName|string|false|none|租赁资源|租赁资源|
|contractStatus|integer(int32)|false|none|合同一级状态|合同一级状态|
|contractStatusTwo|integer(int32)|false|none|合同二级状态|合同二级状态|

<h2 id="tocS_ContractVo">ContractVo</h2>

<a id="schemacontractvo"></a>
<a id="schema_ContractVo"></a>
<a id="tocScontractvo"></a>
<a id="tocscontractvo"></a>

```json
{
  "id": "string",
  "projectId": "string",
  "projectName": "string",
  "contractNo": "string",
  "unionId": "string",
  "version": "string",
  "isCurrent": true,
  "isLatest": true,
  "status": 0,
  "statusTwo": 0,
  "approveStatus": 0,
  "operateType": 0,
  "contractType": 0,
  "ourSigningParty": "string",
  "customerName": "string",
  "unenterNum": 0,
  "signWay": 0,
  "signType": 0,
  "originId": "string",
  "changeFromId": "string",
  "landUsage": "string",
  "signerId": "string",
  "signerName": "string",
  "ownerId": "string",
  "ownerName": "string",
  "contractMode": 0,
  "paperContractNo": "string",
  "signDate": "2019-08-24T14:15:22Z",
  "handoverDate": "2019-08-24T14:15:22Z",
  "contractPurpose": 0,
  "dealChannel": 0,
  "assistantId": "string",
  "assistantName": "string",
  "rentYear": 0,
  "rentMonth": 0,
  "rentDay": 0,
  "startDate": "2019-08-24T14:15:22Z",
  "endDate": "2019-08-24T14:15:22Z",
  "effectDate": "2019-08-24T14:15:22Z",
  "bookingRelType": 0,
  "bondReceivableDate": "string",
  "bondReceivableType": 0,
  "bondPriceType": 0,
  "bondPrice": 0,
  "chargeWay": 0,
  "rentReceivableDate": "string",
  "rentReceivableType": 0,
  "rentTicketPeriod": 0,
  "rentPayPeriod": 0,
  "increaseGap": 0,
  "increaseRate": 0,
  "increaseRule": "string",
  "estimateRevenue": 0,
  "percentageType": 0,
  "fixedPercentage": 0,
  "stepPercentage": "string",
  "revenueType": 0,
  "isIncludePm": true,
  "pmUnitPrice": 0,
  "pmMonthlyPrice": 0,
  "totalPrice": 0,
  "bizTypeId": "string",
  "bizTypeName": "string",
  "lesseeBrand": "string",
  "businessCategory": "string",
  "openDate": "2019-08-24T14:15:22Z",
  "fireRiskCategory": 0,
  "sprinklerSystem": 0,
  "factoryEngaged": "string",
  "deliverDate": "2019-08-24T14:15:22Z",
  "parkingSpaceType": 0,
  "hasParkingFee": true,
  "parkingFeeAmount": 0,
  "otherInfo": "string",
  "contractAttachments": "string",
  "signAttachments": "string",
  "attachmentsPlan": "string",
  "isUploadSignature": true,
  "changeType": "string",
  "processId": "string",
  "changeDate": "2019-08-24T14:15:22Z",
  "changeAttachments": "string",
  "isSignatureConfirm": true,
  "isPaperConfirm": true,
  "isFinish": true,
  "createByName": "string",
  "createTime": "2019-08-24T14:15:22Z",
  "updateByName": "string",
  "updateTime": "2019-08-24T14:15:22Z",
  "isDel": true,
  "roomName": "string",
  "terminateDate": "2019-08-24T14:15:22Z",
  "customer": {
    "id": "string",
    "contractId": "string",
    "customerId": "string",
    "customerType": 0,
    "customerName": "string",
    "address": "string",
    "phone": "string",
    "idType": "string",
    "idNumber": "string",
    "isEmployee": true,
    "creditCode": "string",
    "contactName": "string",
    "contactPhone": "string",
    "contactIdNumber": "string",
    "legalName": "string",
    "paymentAccount": "string",
    "guarantorName": "string",
    "guarantorPhone": "string",
    "guarantorIdType": "string",
    "guarantorIdNumber": "string",
    "guarantorAddress": "string",
    "guarantorIdFront": "string",
    "guarantorIdBack": "string",
    "invoiceTitle": "string",
    "invoiceTaxNumber": "string",
    "invoiceAddress": "string",
    "invoicePhone": "string",
    "invoiceBankName": "string",
    "invoiceAccountNumber": "string",
    "createByName": "string",
    "updateByName": "string",
    "isDel": true
  },
  "bookings": [
    {
      "id": "string",
      "contractId": "string",
      "bookingId": "string",
      "bookedRoom": "string",
      "bookerName": "string",
      "bookingReceivedAmount": 0,
      "bookingPaymentDate": "2019-08-24T14:15:22Z",
      "transferBondAmount": 0,
      "transferRentAmount": 0,
      "createByName": "string",
      "updateByName": "string",
      "isDel": true
    }
  ],
  "fees": [
    {
      "id": "string",
      "contractId": "string",
      "feeType": 0,
      "freeType": 0,
      "freeRentMonth": 0,
      "freeRentDay": 0,
      "startDate": "2019-08-24T14:15:22Z",
      "endDate": "2019-08-24T14:15:22Z",
      "isCharge": true,
      "remark": "string",
      "createByName": "string",
      "updateByName": "string",
      "isDel": true
    }
  ],
  "costs": [
    {
      "id": "string",
      "contractId": "string",
      "costType": 0,
      "startDate": "2019-08-24T14:15:22Z",
      "endDate": "2019-08-24T14:15:22Z",
      "period": 0,
      "customerId": "string",
      "customerName": "string",
      "roomId": "string",
      "roomName": "string",
      "area": 0,
      "subjectId": "string",
      "subjectName": "string",
      "receivableDate": "2019-08-24T14:15:22Z",
      "unitPrice": 0,
      "priceUnit": 0,
      "totalAmount": 0,
      "discountAmount": 0,
      "actualReceivable": 0,
      "receivedAmount": 0,
      "isRevenue": true,
      "isDiscount": true,
      "percentageType": 0,
      "fixedPercentage": 0,
      "stepPercentage": "string",
      "createByName": "string",
      "updateByName": "string",
      "isDel": true
    }
  ],
  "rooms": [
    {
      "id": "string",
      "contractId": "string",
      "roomId": "string",
      "roomName": "string",
      "buildingName": "string",
      "floorName": "string",
      "parcelName": "string",
      "stageName": "string",
      "area": 0,
      "standardUnitPrice": 0,
      "bottomPrice": 0,
      "priceUnit": 0,
      "discount": 0,
      "signedUnitPrice": 0,
      "signedMonthlyPrice": 0,
      "startDate": "2019-08-24T14:15:22Z",
      "endDate": "2019-08-24T14:15:22Z",
      "bondPriceType": 0,
      "bondPrice": 0,
      "createByName": "string",
      "updateByName": "string",
      "isDel": true
    }
  ]
}

```

合同基础信息

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|string|false|none|主键ID|none|
|projectId|string|false|none|项目id|项目id|
|projectName|string|false|none|项目名称|项目名称|
|contractNo|string|false|none|合同号|合同号|
|unionId|string|false|none|统一id|统一id|
|version|string|false|none|版本号v00x|版本号v00x|
|isCurrent|boolean|false|none|是否当前生效版本 0-否,1-是|是否当前生效版本 0-否,1-是|
|isLatest|boolean|false|none|是否最新版本 0-否,1-是|是否最新版本 0-否,1-是|
|status|integer(int32)|false|none|一级状态,10-草稿,20-待生效,20-生效中,30-失效,40-作废|一级状态,10-草稿,20-待生效,20-生效中,30-失效,40-作废|
|statusTwo|integer(int32)|false|none|二级状态|二级状态|
|approveStatus|integer(int32)|false|none|审核状态,0-待审核,1-审核中,2-审核通过,3-审核拒绝|审核状态,0-待审核,1-审核中,2-审核通过,3-审核拒绝|
|operateType|integer(int32)|false|none|最新操作类型,0-新签,1-变更条款,2-退租|最新操作类型,0-新签,1-变更条款,2-退租|
|contractType|integer(int32)|false|none|合同类型,0-非宿舍,1-宿舍,2-多经,3-日租房|合同类型,0-非宿舍,1-宿舍,2-多经,3-日租房|
|ourSigningParty|string|false|none|我方签约主体信息|我方签约主体信息|
|customerName|string|false|none|客户名/公司名|客户名/公司名|
|unenterNum|integer(int32)|false|none|未进场房源数|未进场房源数|
|signWay|integer(int32)|false|none|签约方式,0-电子合同,1-纸质合同（甲方电子章,2-纸质合同（双方实体章）|签约方式,0-电子合同,1-纸质合同（甲方电子章,2-纸质合同（双方实体章）|
|signType|integer(int32)|false|none|签约类型,0-新签,1-续签|签约类型,0-新签,1-续签|
|originId|string|false|none|续签源合同ID|续签源合同ID|
|changeFromId|string|false|none|变更源合同ID|变更源合同ID|
|landUsage|string|false|none|用地性质|用地性质|
|signerId|string|false|none|合同签约人id|合同签约人id|
|signerName|string|false|none|签约人姓名|签约人姓名|
|ownerId|string|false|none|责任人id|责任人id|
|ownerName|string|false|none|责任人姓名|责任人姓名|
|contractMode|integer(int32)|false|none|合同模式,0-标准合同,1-非标合同|合同模式,0-标准合同,1-非标合同|
|paperContractNo|string|false|none|纸质合同号|纸质合同号|
|signDate|string(date-time)|false|none|签订日期|签订日期|
|handoverDate|string(date-time)|false|none|交房日期|交房日期|
|contractPurpose|integer(int32)|false|none|合同用途,字典:|合同用途,字典:|
|dealChannel|integer(int32)|false|none|成交渠道,0-中介推荐,1-自拓客户,2-招商代理,3-全员招商|成交渠道,0-中介推荐,1-自拓客户,2-招商代理,3-全员招商|
|assistantId|string|false|none|协助人id|协助人id|
|assistantName|string|false|none|协助人姓名|协助人姓名|
|rentYear|integer(int32)|false|none|租赁期限-年|租赁期限-年|
|rentMonth|integer(int32)|false|none|租赁期限-月|租赁期限-月|
|rentDay|integer(int32)|false|none|租赁期限-日|租赁期限-日|
|startDate|string(date-time)|false|none|开始日期|开始日期|
|endDate|string(date-time)|false|none|结束日期|结束日期|
|effectDate|string(date-time)|false|none|实际生效日期|实际生效日期|
|bookingRelType|integer(int32)|false|none|定单关联方式，0-房间有效定单，1-不关联定单，2-其他定单|定单关联方式，0-房间有效定单，1-不关联定单，2-其他定单|
|bondReceivableDate|string|false|none|保证金应收日期(天数/具体日期)|保证金应收日期(天数/具体日期)|
|bondReceivableType|integer(int32)|false|none|保证金应收日期类型,0-合同签订后, 1-指定日期|保证金应收日期类型,0-合同签订后, 1-指定日期|
|bondPriceType|integer(int32)|false|none|保证金金额类型,0-自定义,1-压1个月, 2-压2个月...,12-压12个月,30-房源保证金|保证金金额类型,0-自定义,1-压1个月, 2-压2个月...,12-压12个月,30-房源保证金|
|bondPrice|number|false|none|保证金金额|保证金金额|
|chargeWay|integer(int32)|false|none|收费方式,0-固定租金,1-递增租金,2-营收抽成|收费方式,0-固定租金,1-递增租金,2-营收抽成|
|rentReceivableDate|string|false|none|租金应收日期(天数/具体日期)|租金应收日期(天数/具体日期)|
|rentReceivableType|integer(int32)|false|none|租金应收日期类型,1-租期开始前,2-租期开始后|租金应收日期类型,1-租期开始前,2-租期开始后|
|rentTicketPeriod|integer(int32)|false|none|租金账单周期: 1-租赁月, 2-自然月|租金账单周期: 1-租赁月, 2-自然月|
|rentPayPeriod|integer(int32)|false|none|租金支付周期|租金支付周期|
|increaseGap|integer(int32)|false|none|递增间隔(年)|递增间隔(年)|
|increaseRate|number|false|none|递增率|递增率|
|increaseRule|string|false|none|租金递增规则(非标)|租金递增规则(非标)|
|estimateRevenue|number|false|none|预估营收额|预估营收额|
|percentageType|integer(int32)|false|none|提成类型: 1-固定提成, 2-阶梯提成|提成类型: 1-固定提成, 2-阶梯提成|
|fixedPercentage|number|false|none|固定提成比例|固定提成比例|
|stepPercentage|string|false|none|阶梯提成比例json信息|阶梯提成比例json信息|
|revenueType|integer(int32)|false|none|抽点类型: 1-按月营业额, 2-按年营业额|抽点类型: 1-按月营业额, 2-按年营业额|
|isIncludePm|boolean|false|none|是否包含物业费|是否包含物业费|
|pmUnitPrice|number|false|none|月物业费单价|月物业费单价|
|pmMonthlyPrice|number|false|none|月物业费总价|月物业费总价|
|totalPrice|number|false|none|合同总价|合同总价|
|bizTypeId|string|false|none|业态id|业态id|
|bizTypeName|string|false|none|业态|业态|
|lesseeBrand|string|false|none|承租方品牌|承租方品牌|
|businessCategory|string|false|none|经营品类|经营品类|
|openDate|string(date-time)|false|none|开业日期|开业日期|
|fireRiskCategory|integer(int32)|false|none|生产火灾危险性类别,0-丙类,1-丁类,2-戊类|生产火灾危险性类别,0-丙类,1-丁类,2-戊类|
|sprinklerSystem|integer(int32)|false|none|喷淋系统,0-安装,1-未安装|喷淋系统,0-安装,1-未安装|
|factoryEngaged|string|false|none|厂房从事|厂房从事|
|deliverDate|string(date-time)|false|none|交接日期|交接日期|
|parkingSpaceType|integer(int32)|false|none|车位类型,0-人防,1-非人防|车位类型,0-人防,1-非人防|
|hasParkingFee|boolean|false|none|是否包含车位管理费|是否包含车位管理费|
|parkingFeeAmount|number|false|none|车位管理费金额|车位管理费金额|
|otherInfo|string|false|none|补充条款|补充条款|
|contractAttachments|string|false|none|合同附件|合同附件|
|signAttachments|string|false|none|签署附件|签署附件[{"fileName": "", "fileUrl":"", "isConfirm":1or0}]|
|attachmentsPlan|string|false|none|附件-平面图|附件-平面图|
|isUploadSignature|boolean|false|none|是否上传签署文件 0-否,1-是|是否上传签署文件 0-否,1-是|
|changeType|string|false|none|变更类型逗号拼接,1-主体变更, 2-条款变更, 3-费用条款&价格变更|变更类型逗号拼接,1-主体变更, 2-条款变更, 3-费用条款&价格变更|
|processId|string|false|none|流程实例t_oa_process->id|流程实例t_oa_process->id|
|changeDate|string(date-time)|false|none|变更日期|变更日期|
|changeAttachments|string|false|none|变更附件json数组|变更附件json数组|
|isSignatureConfirm|boolean|false|none|是否确认签署 0-否,1-是|是否确认签署 0-否,1-是|
|isPaperConfirm|boolean|false|none|是否确认纸质文件 0-否,1-是|是否确认纸质文件 0-否,1-是|
|isFinish|boolean|false|none|合同是否完全结束 0-否,1-是|合同是否完全结束 0-否,1-是|
|createByName|string|false|none|创建人姓名|创建人姓名|
|createTime|string(date-time)|false|none|创建日期|创建日期|
|updateByName|string|false|none|更新人姓名|更新人姓名|
|updateTime|string(date-time)|false|none|更新日期|更新日期|
|isDel|boolean|false|none|0-否,1-是|0-否,1-是|
|roomName|string|false|none|租赁资源|租赁资源|
|terminateDate|string(date-time)|false|none|退租日期|退租日期|
|customer|[ContractCustomerVo](#schemacontractcustomervo)|false|none||合同客户信息|
|bookings|[[ContractBookingVo](#schemacontractbookingvo)]|false|none|合同定单列表|合同定单列表|
|fees|[[ContractFeeVo](#schemacontractfeevo)]|false|none|合同费用列表|合同费用列表|
|costs|[[ContractCostVo](#schemacontractcostvo)]|false|none|合同应收列表|合同应收列表|
|rooms|[[ContractRoomVo](#schemacontractroomvo)]|false|none|合同房源列表|合同房源列表|

<h2 id="tocS_ContractRoomVo">ContractRoomVo</h2>

<a id="schemacontractroomvo"></a>
<a id="schema_ContractRoomVo"></a>
<a id="tocScontractroomvo"></a>
<a id="tocscontractroomvo"></a>

```json
{
  "id": "string",
  "contractId": "string",
  "roomId": "string",
  "roomName": "string",
  "buildingName": "string",
  "floorName": "string",
  "parcelName": "string",
  "stageName": "string",
  "area": 0,
  "standardUnitPrice": 0,
  "bottomPrice": 0,
  "priceUnit": 0,
  "discount": 0,
  "signedUnitPrice": 0,
  "signedMonthlyPrice": 0,
  "startDate": "2019-08-24T14:15:22Z",
  "endDate": "2019-08-24T14:15:22Z",
  "bondPriceType": 0,
  "bondPrice": 0,
  "createByName": "string",
  "updateByName": "string",
  "isDel": true
}

```

合同房源列表

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|string|false|none|主键ID|none|
|contractId|string|false|none|合同id|合同id|
|roomId|string|false|none|房源id|房源id|
|roomName|string|false|none|房间名称|房间名称|
|buildingName|string|false|none|楼栋名称|楼栋名称|
|floorName|string|false|none|楼层名称|楼层名称|
|parcelName|string|false|none|地块名称|地块名称|
|stageName|string|false|none|分期名称|分期名称|
|area|number|false|none|面积（㎡）|面积（㎡）|
|standardUnitPrice|number|false|none|标准租金（单价）|标准租金（单价）|
|bottomPrice|number|false|none|底价|底价|
|priceUnit|integer(int32)|false|none|单价单位, 使用统一字典, 待定|单价单位, 使用统一字典, 待定|
|discount|number|false|none|折扣|折扣|
|signedUnitPrice|number|false|none|签约单价|签约单价|
|signedMonthlyPrice|number|false|none|签约月总价（元/月）|签约月总价（元/月）|
|startDate|string(date-time)|false|none|实际开始日期|实际开始日期|
|endDate|string(date-time)|false|none|实际结束日期|实际结束日期|
|bondPriceType|integer(int32)|false|none|立项定价保证金金额类型|立项定价保证金金额类型,0-自定义,1-压1个月, 2-压2个月...,12-压12个月|
|bondPrice|number|false|none|立项定价保证金金额|立项定价保证金金额|
|createByName|string|false|none|创建人姓名|创建人姓名|
|updateByName|string|false|none|更新人姓名|更新人姓名|
|isDel|boolean|false|none|0-否,1-是|0-否,1-是|

<h2 id="tocS_ContractCostVo">ContractCostVo</h2>

<a id="schemacontractcostvo"></a>
<a id="schema_ContractCostVo"></a>
<a id="tocScontractcostvo"></a>
<a id="tocscontractcostvo"></a>

```json
{
  "id": "string",
  "contractId": "string",
  "costType": 0,
  "startDate": "2019-08-24T14:15:22Z",
  "endDate": "2019-08-24T14:15:22Z",
  "period": 0,
  "customerId": "string",
  "customerName": "string",
  "roomId": "string",
  "roomName": "string",
  "area": 0,
  "subjectId": "string",
  "subjectName": "string",
  "receivableDate": "2019-08-24T14:15:22Z",
  "unitPrice": 0,
  "priceUnit": 0,
  "totalAmount": 0,
  "discountAmount": 0,
  "actualReceivable": 0,
  "receivedAmount": 0,
  "isRevenue": true,
  "isDiscount": true,
  "percentageType": 0,
  "fixedPercentage": 0,
  "stepPercentage": "string",
  "createByName": "string",
  "updateByName": "string",
  "isDel": true
}

```

合同应收列表

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|string|false|none|主键ID|none|
|contractId|string|false|none|合同id|合同id|
|costType|integer(int32)|false|none|账单类型,1-保证金,2-租金,3-其他费用|账单类型,1-保证金,2-租金,3-其他费用|
|startDate|string(date-time)|false|none|开始日期|开始日期|
|endDate|string(date-time)|false|none|结束日期|结束日期|
|period|integer(int32)|false|none|账单期数|账单期数|
|customerId|string|false|none|商户id|商户id|
|customerName|string|false|none|商户名|商户名|
|roomId|string|false|none|商铺id|商铺id|
|roomName|string|false|none|商铺名|商铺名|
|area|number|false|none|商铺面积|商铺面积|
|subjectId|string|false|none|收款用途id|收款用途id|
|subjectName|string|false|none|收款用途|收款用途|
|receivableDate|string(date-time)|false|none|应收日期|应收日期|
|unitPrice|number|false|none|单价|单价|
|priceUnit|integer(int32)|false|none|单价单位|单价单位|
|totalAmount|number|false|none|账单总额（元）|账单总额（元）|
|discountAmount|number|false|none|优惠金额（元）|优惠金额（元）|
|actualReceivable|number|false|none|账单实际应收金额（元）|账单实际应收金额（元）|
|receivedAmount|number|false|none|账单已收金额（元）|账单已收金额（元）|
|isRevenue|boolean|false|none|是否营收提成,0-否,1-是|是否营收提成,0-否,1-是|
|isDiscount|boolean|false|none|是否优惠,0-否,1-是|是否优惠,0-否,1-是|
|percentageType|integer(int32)|false|none|提成类型: 1-固定提成, 2-阶梯提成|提成类型: 1-固定提成, 2-阶梯提成|
|fixedPercentage|number|false|none|固定提成比例|固定提成比例|
|stepPercentage|string|false|none|阶梯提成比例json信息|阶梯提成比例json信息|
|createByName|string|false|none|创建人姓名|创建人姓名|
|updateByName|string|false|none|更新人姓名|更新人姓名|
|isDel|boolean|false|none|0-否,1-是|0-否,1-是|

<h2 id="tocS_ContractFeeVo">ContractFeeVo</h2>

<a id="schemacontractfeevo"></a>
<a id="schema_ContractFeeVo"></a>
<a id="tocScontractfeevo"></a>
<a id="tocscontractfeevo"></a>

```json
{
  "id": "string",
  "contractId": "string",
  "feeType": 0,
  "freeType": 0,
  "freeRentMonth": 0,
  "freeRentDay": 0,
  "startDate": "2019-08-24T14:15:22Z",
  "endDate": "2019-08-24T14:15:22Z",
  "isCharge": true,
  "remark": "string",
  "createByName": "string",
  "updateByName": "string",
  "isDel": true
}

```

合同费用列表

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|string|false|none|主键ID|none|
|contractId|string|false|none|合同id|合同id|
|feeType|integer(int32)|false|none|费用类型,1-免租期|费用类型,1-免租期|
|freeType|integer(int32)|false|none|免租类型,0-装修免租,1-经营免租,2-合同免租|免租类型,0-装修免租,1-经营免租,2-合同免租|
|freeRentMonth|integer(int32)|false|none|免租月数|免租月数|
|freeRentDay|integer(int32)|false|none|免租天数|免租天数|
|startDate|string(date-time)|false|none|开始日期|开始日期|
|endDate|string(date-time)|false|none|结束日期|结束日期|
|isCharge|boolean|false|none|免租是否收费:0-否,1-是(退租时使用)|免租是否收费:0-否,1-是(退租时使用)|
|remark|string|false|none|备注|备注|
|createByName|string|false|none|创建人姓名|创建人姓名|
|updateByName|string|false|none|更新人姓名|更新人姓名|
|isDel|boolean|false|none|0-否,1-是|0-否,1-是|

<h2 id="tocS_ContractBookingVo">ContractBookingVo</h2>

<a id="schemacontractbookingvo"></a>
<a id="schema_ContractBookingVo"></a>
<a id="tocScontractbookingvo"></a>
<a id="tocscontractbookingvo"></a>

```json
{
  "id": "string",
  "contractId": "string",
  "bookingId": "string",
  "bookedRoom": "string",
  "bookerName": "string",
  "bookingReceivedAmount": 0,
  "bookingPaymentDate": "2019-08-24T14:15:22Z",
  "transferBondAmount": 0,
  "transferRentAmount": 0,
  "createByName": "string",
  "updateByName": "string",
  "isDel": true
}

```

合同定单列表

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|string|false|none|主键ID|none|
|contractId|string|false|none|合同id|合同id|
|bookingId|string|false|none|定单id|定单id|
|bookedRoom|string|false|none|预定房源|预定房源|
|bookerName|string|false|none|预定人姓名|预定人姓名|
|bookingReceivedAmount|number|false|none|定单已收金额|定单已收金额|
|bookingPaymentDate|string(date-time)|false|none|定单收款(生效)日期|定单收款(生效)日期|
|transferBondAmount|number|false|none|转保证金金额|转保证金金额|
|transferRentAmount|number|false|none|转租金金额|转租金金额|
|createByName|string|false|none|创建人姓名|创建人姓名|
|updateByName|string|false|none|更新人姓名|更新人姓名|
|isDel|boolean|false|none|0-否,1-是|0-否,1-是|

<h2 id="tocS_ContractCustomerVo">ContractCustomerVo</h2>

<a id="schemacontractcustomervo"></a>
<a id="schema_ContractCustomerVo"></a>
<a id="tocScontractcustomervo"></a>
<a id="tocscontractcustomervo"></a>

```json
{
  "id": "string",
  "contractId": "string",
  "customerId": "string",
  "customerType": 0,
  "customerName": "string",
  "address": "string",
  "phone": "string",
  "idType": "string",
  "idNumber": "string",
  "isEmployee": true,
  "creditCode": "string",
  "contactName": "string",
  "contactPhone": "string",
  "contactIdNumber": "string",
  "legalName": "string",
  "paymentAccount": "string",
  "guarantorName": "string",
  "guarantorPhone": "string",
  "guarantorIdType": "string",
  "guarantorIdNumber": "string",
  "guarantorAddress": "string",
  "guarantorIdFront": "string",
  "guarantorIdBack": "string",
  "invoiceTitle": "string",
  "invoiceTaxNumber": "string",
  "invoiceAddress": "string",
  "invoicePhone": "string",
  "invoiceBankName": "string",
  "invoiceAccountNumber": "string",
  "createByName": "string",
  "updateByName": "string",
  "isDel": true
}

```

合同客户信息

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|string|false|none|主键ID|none|
|contractId|string|false|none|合同id|合同id|
|customerId|string|false|none|客户id|客户id|
|customerType|integer(int32)|false|none|客户类型:1-个人1,2-企业|客户类型:1-个人1,2-企业|
|customerName|string|false|none|客户名/公司名|客户名/公司名|
|address|string|false|none|地址|地址|
|phone|string|false|none|手机号(个人/法人)|手机号(个人/法人)|
|idType|string|false|none|证件类型(个人/法人)|证件类型(个人/法人)|
|idNumber|string|false|none|证件号码(个人/法人)|证件号码(个人/法人)|
|isEmployee|boolean|false|none|是否是员工:0-否,1-是|是否是员工:0-否,1-是|
|creditCode|string|false|none|统一社会信用代码|统一社会信用代码|
|contactName|string|false|none|联系人|联系人|
|contactPhone|string|false|none|联系人手机号|联系人手机号|
|contactIdNumber|string|false|none|联系人身份证号|联系人身份证号|
|legalName|string|false|none|法人名称|法人名称|
|paymentAccount|string|false|none|付款银行账号|付款银行账号|
|guarantorName|string|false|none|担保人姓名|担保人姓名|
|guarantorPhone|string|false|none|担保人手机号|担保人手机号|
|guarantorIdType|string|false|none|担保人证件类型|担保人证件类型|
|guarantorIdNumber|string|false|none|担保人证件号码|担保人证件号码|
|guarantorAddress|string|false|none|担保人通讯地址|担保人通讯地址|
|guarantorIdFront|string|false|none|担保人身份证正面地址|担保人身份证正面地址|
|guarantorIdBack|string|false|none|担保人身份证反面地址|担保人身份证反面地址|
|invoiceTitle|string|false|none|开票名称|开票名称|
|invoiceTaxNumber|string|false|none|开票税号|开票税号|
|invoiceAddress|string|false|none|开票单位地址|开票单位地址|
|invoicePhone|string|false|none|开票电话号码|开票电话号码|
|invoiceBankName|string|false|none|开票开户银行|开票开户银行|
|invoiceAccountNumber|string|false|none|开票银行账户|开票银行账户|
|createByName|string|false|none|创建人姓名|创建人姓名|
|updateByName|string|false|none|更新人姓名|更新人姓名|
|isDel|boolean|false|none|0-否,1-是|0-否,1-是|


---
title: 万洋
language_tabs:
  - shell: Shell
  - http: HTTP
  - javascript: JavaScript
  - ruby: Ruby
  - python: Python
  - php: PHP
  - java: Java
  - go: Go
toc_footers: []
includes: []
search: true
code_clipboard: true
highlight_theme: darkula
headingLevel: 2
generator: "@tarslib/widdershins v4.0.30"

---

# 万洋

Base URLs:

# Authentication

# 租赁中心前端/C端催缴接口

<a id="opIdpay_1"></a>

## POST 催缴支付接口

POST /contract/bill/pay

根据催缴ID和支付明细生成支付链接

> Body 请求参数

```json
{
  "billId": "string",
  "amount": 0,
  "paymentList": [
    {
      "costId": "string",
      "payAmount": 0
    }
  ]
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[ContractBillPaymentDto](#schemacontractbillpaymentdto)| 否 |none|

> 返回示例

> 200 Response

```
"string"
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|default response|string|

<a id="opIddetail_3"></a>

## GET 获取催缴详细信息

GET /contract/bill/detail

根据催缴ID获取催缴详细信息，包含催缴金额列表

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|id|query|string| 是 |催缴ID|
|Authorization|header|string| 否 |none|

> 返回示例

> 200 Response

```
{"id":"string","projectId":"string","projectName":"string","customerId":"string","customerName":"string","contractId":"string","contractNo":"string","roomCount":0,"roomName":"string","type":0,"startDate":"2019-08-24T14:15:22Z","endDate":"2019-08-24T14:15:22Z","receivableDate":"2019-08-24T14:15:22Z","totalMoney":0,"lastReceivedMoney":0,"collectFlag":0,"collectTime":"2019-08-24T14:15:22Z","sendTime":"2019-08-24T14:15:22Z","receivePerson":"string","receivePhone":"string","viewStatus":0,"viewTime":"2019-08-24T14:15:22Z","previewUrl":"string","createBy":"string","createByName":"string","createTime":"2019-08-24T14:15:22Z","updateBy":"string","updateByName":"string","updateTime":"2019-08-24T14:15:22Z","moneyList":[{"id":"string","billId":"string","costId":"string","costType":0,"subjectName":"string","period":0,"startDate":"2019-08-24T14:15:22Z","endDate":"2019-08-24T14:15:22Z","receivableDate":"2019-08-24T14:15:22Z","totalAmount":0,"discountAmount":0,"reductionAmount":0,"actualReceivable":0,"receivedAmount":0,"carryoverAmount":0,"unreceivedAmount":0,"createBy":"string","createByName":"string","createTime":"2019-08-24T14:15:22Z","updateBy":"string","updateByName":"string","updateTime":"2019-08-24T14:15:22Z"}]}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|default response|[ContractBillDetailVo](#schemacontractbilldetailvo)|

# 数据模型

<h2 id="tocS_ContractBillPaymentDto">ContractBillPaymentDto</h2>

<a id="schemacontractbillpaymentdto"></a>
<a id="schema_ContractBillPaymentDto"></a>
<a id="tocScontractbillpaymentdto"></a>
<a id="tocscontractbillpaymentdto"></a>

```json
{
  "billId": "string",
  "amount": 0,
  "paymentList": [
    {
      "costId": "string",
      "payAmount": 0
    }
  ]
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|billId|string|true|none|催缴ID|催缴ID|
|amount|number|true|none|支付金额|支付金额|
|paymentList|[[ContractBillPaymentItemDto](#schemacontractbillpaymentitemdto)]|false|none|支付催缴金额详情列表|支付催缴金额详情列表|

<h2 id="tocS_ContractBillPaymentItemDto">ContractBillPaymentItemDto</h2>

<a id="schemacontractbillpaymentitemdto"></a>
<a id="schema_ContractBillPaymentItemDto"></a>
<a id="tocScontractbillpaymentitemdto"></a>
<a id="tocscontractbillpaymentitemdto"></a>

```json
{
  "costId": "string",
  "payAmount": 0
}

```

支付催缴金额详情列表

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|costId|string|true|none|账单ID|账单ID|
|payAmount|number|true|none|支付金额|支付金额|

<h2 id="tocS_ContractBillDetailVo">ContractBillDetailVo</h2>

<a id="schemacontractbilldetailvo"></a>
<a id="schema_ContractBillDetailVo"></a>
<a id="tocScontractbilldetailvo"></a>
<a id="tocscontractbilldetailvo"></a>

```json
{
  "id": "string",
  "projectId": "string",
  "projectName": "string",
  "customerId": "string",
  "customerName": "string",
  "contractId": "string",
  "contractNo": "string",
  "roomCount": 0,
  "roomName": "string",
  "type": 0,
  "startDate": "2019-08-24T14:15:22Z",
  "endDate": "2019-08-24T14:15:22Z",
  "receivableDate": "2019-08-24T14:15:22Z",
  "totalMoney": 0,
  "lastReceivedMoney": 0,
  "collectFlag": 0,
  "collectTime": "2019-08-24T14:15:22Z",
  "sendTime": "2019-08-24T14:15:22Z",
  "receivePerson": "string",
  "receivePhone": "string",
  "viewStatus": 0,
  "viewTime": "2019-08-24T14:15:22Z",
  "previewUrl": "string",
  "createBy": "string",
  "createByName": "string",
  "createTime": "2019-08-24T14:15:22Z",
  "updateBy": "string",
  "updateByName": "string",
  "updateTime": "2019-08-24T14:15:22Z",
  "moneyList": [
    {
      "id": "string",
      "billId": "string",
      "costId": "string",
      "costType": 0,
      "subjectName": "string",
      "period": 0,
      "startDate": "2019-08-24T14:15:22Z",
      "endDate": "2019-08-24T14:15:22Z",
      "receivableDate": "2019-08-24T14:15:22Z",
      "totalAmount": 0,
      "discountAmount": 0,
      "reductionAmount": 0,
      "actualReceivable": 0,
      "receivedAmount": 0,
      "carryoverAmount": 0,
      "unreceivedAmount": 0,
      "createBy": "string",
      "createByName": "string",
      "createTime": "2019-08-24T14:15:22Z",
      "updateBy": "string",
      "updateByName": "string",
      "updateTime": "2019-08-24T14:15:22Z"
    }
  ]
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|string|false|none|主键ID|none|
|projectId|string|false|none|项目ID|项目ID|
|projectName|string|false|none|项目名称|项目名称|
|customerId|string|false|none|客户ID|客户ID|
|customerName|string|false|none|客户名称|客户名称|
|contractId|string|false|none|合同ID|合同ID|
|contractNo|string|false|none|合同编号|合同编号|
|roomCount|integer(int32)|false|none|租赁房源数|租赁房源数|
|roomName|string|false|none|房间名称，多个按照逗号分开|房间名称，多个按照逗号分开|
|type|integer(int32)|false|none|催缴类型(1催缴函 2催缴通知单)|催缴类型(1催缴函 2催缴通知单)|
|startDate|string(date-time)|false|none|合同开始日期|合同开始日期|
|endDate|string(date-time)|false|none|合同结束日期|合同结束日期|
|receivableDate|string(date-time)|false|none|应收日期|应收日期|
|totalMoney|number|false|none|催缴金额|催缴金额|
|lastReceivedMoney|number|false|none|账单生成时，除保证金外的已收金额|账单生成时，除保证金外的已收金额|
|collectFlag|integer(int32)|false|none|缴费状态(0未缴 1部分缴 2已缴 3缴费中)|缴费状态(0未缴 1部分缴 2已缴 3缴费中)|
|collectTime|string(date-time)|false|none|收齐时间|收齐时间|
|sendTime|string(date-time)|false|none|发送时间|发送时间|
|receivePerson|string|false|none|接收人|接收人|
|receivePhone|string|false|none|接收人手机号|接收人手机号|
|viewStatus|integer(int32)|false|none|查看状态(0未查看 1已查看)|查看状态(0未查看 1已查看)|
|viewTime|string(date-time)|false|none|查看时间|查看时间|
|previewUrl|string|false|none|预览地址|预览地址|
|createBy|string|false|none|创建人账号|创建人账号|
|createByName|string|false|none|创建人姓名|创建人姓名|
|createTime|string(date-time)|false|none|创建时间|创建时间|
|updateBy|string|false|none|更新人账号|更新人账号|
|updateByName|string|false|none|更新人姓名|更新人姓名|
|updateTime|string(date-time)|false|none|更新时间|更新时间|
|moneyList|[[ContractBillMoneyVo](#schemacontractbillmoneyvo)]|false|none|催缴金额列表|催缴金额列表|

<h2 id="tocS_ContractBillMoneyVo">ContractBillMoneyVo</h2>

<a id="schemacontractbillmoneyvo"></a>
<a id="schema_ContractBillMoneyVo"></a>
<a id="tocScontractbillmoneyvo"></a>
<a id="tocscontractbillmoneyvo"></a>

```json
{
  "id": "string",
  "billId": "string",
  "costId": "string",
  "costType": 0,
  "subjectName": "string",
  "period": 0,
  "startDate": "2019-08-24T14:15:22Z",
  "endDate": "2019-08-24T14:15:22Z",
  "receivableDate": "2019-08-24T14:15:22Z",
  "totalAmount": 0,
  "discountAmount": 0,
  "reductionAmount": 0,
  "actualReceivable": 0,
  "receivedAmount": 0,
  "carryoverAmount": 0,
  "unreceivedAmount": 0,
  "createBy": "string",
  "createByName": "string",
  "createTime": "2019-08-24T14:15:22Z",
  "updateBy": "string",
  "updateByName": "string",
  "updateTime": "2019-08-24T14:15:22Z"
}

```

催缴金额列表

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|string|false|none|主键ID|none|
|billId|string|false|none|催收id|催收id|
|costId|string|false|none|账单id|账单id|
|costType|integer(int32)|false|none|账单类型|账单类型|
|subjectName|string|false|none|收款用途名称|收款用途名称|
|period|integer(int32)|false|none|账单期数|账单期数|
|startDate|string(date-time)|false|none|开始日期|开始日期|
|endDate|string(date-time)|false|none|结束日期|结束日期|
|receivableDate|string(date-time)|false|none|应收日期|应收日期|
|totalAmount|number|false|none|账单总额|账单总额|
|discountAmount|number|false|none|优惠金额|优惠金额|
|reductionAmount|number|false|none|减免金额|减免金额|
|actualReceivable|number|false|none|实际应收金额|实际应收金额|
|receivedAmount|number|false|none|已收金额|已收金额|
|carryoverAmount|number|false|none|结转金额|结转金额|
|unreceivedAmount|number|false|none|未收金额|未收金额|
|createBy|string|false|none|创建人账号|创建人账号|
|createByName|string|false|none|创建人姓名|创建人姓名|
|createTime|string(date-time)|false|none|创建时间|创建时间|
|updateBy|string|false|none|更新人账号|更新人账号|
|updateByName|string|false|none|更新人姓名|更新人姓名|
|updateTime|string(date-time)|false|none|更新时间|更新时间|


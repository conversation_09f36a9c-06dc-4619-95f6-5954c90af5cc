---
title: 万洋
language_tabs:
  - shell: Shell
  - http: HTTP
  - javascript: JavaScript
  - ruby: Ruby
  - python: Python
  - php: PHP
  - java: Java
  - go: Go
toc_footers: []
includes: []
search: true
code_clipboard: true
highlight_theme: darkula
headingLevel: 2
generator: "@tarslib/widdershins v4.0.30"

---

# 万洋

Base URLs:

# Authentication

# 租赁中心前端/入场记录

<a id="opIdgetUnenteredRooms"></a>

## POST 根据合同id查询未进场房源列表

POST /enter/unenteredRooms

根据合同id查询未进场房源列表

> Body 请求参数

```json
{
  "params": {
    "property1": {},
    "property2": {}
  },
  "pageNum": 1,
  "pageSize": 1,
  "id": "string",
  "projectId": "string",
  "contractId": "string",
  "contractUnionId": "string",
  "enteredRoomCount": 0,
  "isNotify": true,
  "createBy": "string",
  "createByName": "string",
  "createTime": "2019-08-24T14:15:22Z",
  "updateBy": "string",
  "updateByName": "string",
  "updateTime": "2019-08-24T14:15:22Z",
  "isDel": true,
  "type": "string",
  "tenantName": "string",
  "roomName": "string",
  "rentStartDateBegin": "string",
  "rentStartDateEnd": "string",
  "roomAndCustomerName": "string",
  "nearDays": 0
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[EnterQueryDTO](#schemaenterquerydto)| 否 |none|

> 返回示例

> 200 Response

```
{"error":true,"success":true,"warn":true,"empty":true,"property1":{},"property2":{}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[AjaxResult](#schemaajaxresult)|

<a id="opIdsave"></a>

## POST 进场单保存

POST /enter/save

进场单保存

> Body 请求参数

```json
{
  "id": "string",
  "projectId": "string",
  "contractId": "string",
  "contractUnionId": "string",
  "enteredRoomCount": 0,
  "isNotify": true,
  "isDel": true,
  "roomList": [
    {
      "id": "string",
      "enterId": "string",
      "roomId": "string",
      "roomName": "string",
      "propertyType": 0,
      "parcelName": "string",
      "buildingName": "string",
      "enterDate": "2019-08-24T14:15:22Z",
      "elecMeterReading": 0,
      "coldWaterReading": 0,
      "hotWaterReading": 0,
      "remark": "string",
      "assetList": [
        {
          "id": "string",
          "enterRoomId": "string",
          "category": 0,
          "name": "string",
          "specification": "string",
          "count": 0,
          "isMissing": true,
          "isAdd": true,
          "isDel": true
        }
      ],
      "isDel": true
    }
  ]
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[EnterAddDTO](#schemaenteradddto)| 否 |none|

> 返回示例

> 200 Response

```
{"error":true,"success":true,"warn":true,"empty":true,"property1":{},"property2":{}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[AjaxResult](#schemaajaxresult)|

<a id="opIdlist_1"></a>

## POST 查询入场记录列表

POST /enter/list

查询入场记录列表，支持按项目id, 类型(待办理/已办理)，承租方（模糊匹配），楼栋/房源（模糊搜索），租期开始日期，租期结束日期进行查询

> Body 请求参数

```json
{
  "params": {
    "property1": {},
    "property2": {}
  },
  "pageNum": 1,
  "pageSize": 1,
  "id": "string",
  "projectId": "string",
  "contractId": "string",
  "contractUnionId": "string",
  "enteredRoomCount": 0,
  "isNotify": true,
  "createBy": "string",
  "createByName": "string",
  "createTime": "2019-08-24T14:15:22Z",
  "updateBy": "string",
  "updateByName": "string",
  "updateTime": "2019-08-24T14:15:22Z",
  "isDel": true,
  "type": "string",
  "tenantName": "string",
  "roomName": "string",
  "rentStartDateBegin": "string",
  "rentStartDateEnd": "string",
  "roomAndCustomerName": "string",
  "nearDays": 0
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[EnterQueryDTO](#schemaenterquerydto)| 否 |none|

> 返回示例

> 200 Response

```
{"id":"string","projectId":"string","contractId":"string","contractUnionId":"string","enteredRoomCount":0,"isNotify":true,"createByName":"string","updateByName":"string","isDel":true,"projectName":"string","contractNo":"string","contractPurpose":0,"tenantName":"string","roomName":"string","rentStartDate":"2019-08-24T14:15:22Z","rentEndDate":"2019-08-24T14:15:22Z","unenterNum":0,"createTime":"2019-08-24T14:15:22Z"}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|default response|[EnterVo](#schemaentervo)|

<a id="opIdinit"></a>

## POST 进场单初始化

POST /enter/init

进场单初始化

> Body 请求参数

```json
{
  "contractId": "string",
  "roomIds": [
    "string"
  ]
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[EnterInitDTO](#schemaenterinitdto)| 否 |none|

> 返回示例

> 200 Response

```
{"error":true,"success":true,"warn":true,"empty":true,"property1":{},"property2":{}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[AjaxResult](#schemaajaxresult)|

<a id="opIdnotifyCustomer"></a>

## GET 通知客户

GET /enter/notifyCustomer

根据进场单id发送进场通知给客户

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|enterId|query|string| 是 |none|
|Authorization|header|string| 否 |none|

> 返回示例

> 200 Response

```
{"error":true,"success":true,"warn":true,"empty":true,"property1":{},"property2":{}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[AjaxResult](#schemaajaxresult)|

<a id="opIdenterDetail"></a>

## GET 进场单详情（包含房间和资产信息）

GET /enter/enterDetail

根据进场单id查询进场单详情信息，包括合同信息、房间列表和资产列表

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|enterId|query|string| 是 |none|
|Authorization|header|string| 否 |none|

> 返回示例

> 200 Response

```
{"error":true,"success":true,"warn":true,"empty":true,"property1":{},"property2":{}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[AjaxResult](#schemaajaxresult)|

<a id="opIddetail_1"></a>

## GET C端进场通知单详情

GET /enter/detail

根据合同id查询合同信息、房间列表和已进场房间数

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|contractId|query|string| 是 |none|
|Authorization|header|string| 否 |none|

> 返回示例

> 200 Response

```
{"error":true,"success":true,"warn":true,"empty":true,"property1":{},"property2":{}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[AjaxResult](#schemaajaxresult)|

# 数据模型

<h2 id="tocS_AjaxResult">AjaxResult</h2>

<a id="schemaajaxresult"></a>
<a id="schema_AjaxResult"></a>
<a id="tocSajaxresult"></a>
<a id="tocsajaxresult"></a>

```json
{
  "error": true,
  "success": true,
  "warn": true,
  "empty": true,
  "property1": {},
  "property2": {}
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|**additionalProperties**|object|false|none||none|
|error|boolean|false|none||none|
|success|boolean|false|none||none|
|warn|boolean|false|none||none|
|empty|boolean|false|none||none|

<h2 id="tocS_EnterAddDTO">EnterAddDTO</h2>

<a id="schemaenteradddto"></a>
<a id="schema_EnterAddDTO"></a>
<a id="tocSenteradddto"></a>
<a id="tocsenteradddto"></a>

```json
{
  "id": "string",
  "projectId": "string",
  "contractId": "string",
  "contractUnionId": "string",
  "enteredRoomCount": 0,
  "isNotify": true,
  "isDel": true,
  "roomList": [
    {
      "id": "string",
      "enterId": "string",
      "roomId": "string",
      "roomName": "string",
      "propertyType": 0,
      "parcelName": "string",
      "buildingName": "string",
      "enterDate": "2019-08-24T14:15:22Z",
      "elecMeterReading": 0,
      "coldWaterReading": 0,
      "hotWaterReading": 0,
      "remark": "string",
      "assetList": [
        {
          "id": "string",
          "enterRoomId": "string",
          "category": 0,
          "name": "string",
          "specification": "string",
          "count": 0,
          "isMissing": true,
          "isAdd": true,
          "isDel": true
        }
      ],
      "isDel": true
    }
  ]
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|string|false|none|主键ID|主键ID|
|projectId|string|false|none|项目id|项目id|
|contractId|string|false|none|合同id|合同id|
|contractUnionId|string|false|none|合同统一id|合同统一id|
|enteredRoomCount|integer(int32)|false|none|已进场房源数|已进场房源数|
|isNotify|boolean|false|none|是否通知承租方|是否通知承租方|
|isDel|boolean|false|none|0-否,1-是|0-否,1-是|
|roomList|[[EnterRoomAddDTO](#schemaenterroomadddto)]|false|none|房间列表|房间列表|

<h2 id="tocS_EnterQueryDTO">EnterQueryDTO</h2>

<a id="schemaenterquerydto"></a>
<a id="schema_EnterQueryDTO"></a>
<a id="tocSenterquerydto"></a>
<a id="tocsenterquerydto"></a>

```json
{
  "params": {
    "property1": {},
    "property2": {}
  },
  "pageNum": 1,
  "pageSize": 1,
  "id": "string",
  "projectId": "string",
  "contractId": "string",
  "contractUnionId": "string",
  "enteredRoomCount": 0,
  "isNotify": true,
  "createBy": "string",
  "createByName": "string",
  "createTime": "2019-08-24T14:15:22Z",
  "updateBy": "string",
  "updateByName": "string",
  "updateTime": "2019-08-24T14:15:22Z",
  "isDel": true,
  "type": "string",
  "tenantName": "string",
  "roomName": "string",
  "rentStartDateBegin": "string",
  "rentStartDateEnd": "string",
  "roomAndCustomerName": "string",
  "nearDays": 0
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|params|object|false|none||none|
|» **additionalProperties**|object|false|none||none|
|pageNum|integer(int32)|true|none||none|
|pageSize|integer(int32)|true|none||none|
|id|string|false|none|主键ID|主键ID|
|projectId|string|false|none|项目id|项目id|
|contractId|string|false|none|合同id|合同id|
|contractUnionId|string|false|none|合同统一id|合同统一id|
|enteredRoomCount|integer(int32)|false|none|已进场房源数|已进场房源数|
|isNotify|boolean|false|none|是否通知承租方|是否通知承租方|
|createBy|string|false|none|创建人账号|创建人账号|
|createByName|string|false|none|创建人姓名|创建人姓名|
|createTime|string(date-time)|false|none|创建时间|创建时间|
|updateBy|string|false|none|更新人账号|更新人账号|
|updateByName|string|false|none|更新人姓名|更新人姓名|
|updateTime|string(date-time)|false|none|更新时间|更新时间|
|isDel|boolean|false|none|0-否,1-是|0-否,1-是|
|type|string|false|none|类型: 0-待办理,1-已办理|0-待办理,1-已办理|
|tenantName|string|false|none|承租方|承租方名称，模糊匹配|
|roomName|string|false|none|楼栋/房源|楼栋或者房源名称，模糊搜索|
|rentStartDateBegin|string|false|none|租期开始日期|租期开始日期|
|rentStartDateEnd|string|false|none|租期结束日期|租期结束日期|
|roomAndCustomerName|string|false|none|楼栋/房源/承租方|楼栋/房源/承租方|
|nearDays|integer(int32)|false|none|合同开始近3/7/15天|合同开始近3/7/15天|

<h2 id="tocS_EnterRoomAddDTO">EnterRoomAddDTO</h2>

<a id="schemaenterroomadddto"></a>
<a id="schema_EnterRoomAddDTO"></a>
<a id="tocSenterroomadddto"></a>
<a id="tocsenterroomadddto"></a>

```json
{
  "id": "string",
  "enterId": "string",
  "roomId": "string",
  "roomName": "string",
  "propertyType": 0,
  "parcelName": "string",
  "buildingName": "string",
  "enterDate": "2019-08-24T14:15:22Z",
  "elecMeterReading": 0,
  "coldWaterReading": 0,
  "hotWaterReading": 0,
  "remark": "string",
  "assetList": [
    {
      "id": "string",
      "enterRoomId": "string",
      "category": 0,
      "name": "string",
      "specification": "string",
      "count": 0,
      "isMissing": true,
      "isAdd": true,
      "isDel": true
    }
  ],
  "isDel": true
}

```

房间列表

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|string|false|none|主键ID|主键ID|
|enterId|string|false|none|入场单id|入场单id|
|roomId|string|false|none|房间id|房间id|
|roomName|string|false|none|房间名称|房间名称|
|propertyType|integer(int32)|false|none|物业类型字典|物业类型字典|
|parcelName|string|false|none|所属地块|所属地块|
|buildingName|string|false|none|所属楼栋|所属楼栋|
|enterDate|string(date-time)|false|none|入场日期|入场日期|
|elecMeterReading|number|false|none|电表读数|电表读数|
|coldWaterReading|number|false|none|冷水表读数|冷水表读数|
|hotWaterReading|number|false|none|热水表读数|热水表读数|
|remark|string|false|none|备注|备注|
|assetList|[[EnterRoomAssetsAddDTO](#schemaenterroomassetsadddto)]|false|none|资产列表|资产列表|
|isDel|boolean|false|none|0-否,1-是|0-否,1-是|

<h2 id="tocS_EnterRoomAssetsAddDTO">EnterRoomAssetsAddDTO</h2>

<a id="schemaenterroomassetsadddto"></a>
<a id="schema_EnterRoomAssetsAddDTO"></a>
<a id="tocSenterroomassetsadddto"></a>
<a id="tocsenterroomassetsadddto"></a>

```json
{
  "id": "string",
  "enterRoomId": "string",
  "category": 0,
  "name": "string",
  "specification": "string",
  "count": 0,
  "isMissing": true,
  "isAdd": true,
  "isDel": true
}

```

资产列表

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|string|false|none|主键ID|主键ID|
|enterRoomId|string|false|none|入场记录-房间id|入场记录-房间id|
|category|integer(int32)|false|none|种类|种类|
|name|string|false|none|物品名称|物品名称|
|specification|string|false|none|规格|规格|
|count|integer(int32)|false|none|数量|数量|
|isMissing|boolean|false|none|是否缺失|是否缺失|
|isAdd|boolean|false|none|是否手动添加的:0-否,1-是|是否手动添加的:0-否,1-是|
|isDel|boolean|false|none|0-否,1-是|0-否,1-是|

<h2 id="tocS_EnterInitDTO">EnterInitDTO</h2>

<a id="schemaenterinitdto"></a>
<a id="schema_EnterInitDTO"></a>
<a id="tocSenterinitdto"></a>
<a id="tocsenterinitdto"></a>

```json
{
  "contractId": "string",
  "roomIds": [
    "string"
  ]
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|contractId|string|false|none|合同ID|合同ID|
|roomIds|[string]|false|none|房间ID集合|房间ID集合|
|» 房间ID集合|string|false|none|房间ID集合|房间ID集合|

<h2 id="tocS_EnterVo">EnterVo</h2>

<a id="schemaentervo"></a>
<a id="schema_EnterVo"></a>
<a id="tocSentervo"></a>
<a id="tocsentervo"></a>

```json
{
  "id": "string",
  "projectId": "string",
  "contractId": "string",
  "contractUnionId": "string",
  "enteredRoomCount": 0,
  "isNotify": true,
  "createByName": "string",
  "updateByName": "string",
  "isDel": true,
  "projectName": "string",
  "contractNo": "string",
  "contractPurpose": 0,
  "tenantName": "string",
  "roomName": "string",
  "rentStartDate": "2019-08-24T14:15:22Z",
  "rentEndDate": "2019-08-24T14:15:22Z",
  "unenterNum": 0,
  "createTime": "2019-08-24T14:15:22Z"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|string|false|none|主键ID|none|
|projectId|string|false|none|项目id|项目id|
|contractId|string|false|none|合同id|合同id|
|contractUnionId|string|false|none|合同统一id|合同统一id|
|enteredRoomCount|integer(int32)|false|none|已进场房源数|已进场房源数|
|isNotify|boolean|false|none|是否通知承租方|是否通知承租方|
|createByName|string|false|none|创建人姓名|创建人姓名|
|updateByName|string|false|none|更新人姓名|更新人姓名|
|isDel|boolean|false|none|0-否,1-是|0-否,1-是|
|projectName|string|false|none|项目名称|项目名称|
|contractNo|string|false|none|合同编号|合同编号|
|contractPurpose|integer(int32)|false|none|合同用途|合同用途,字典|
|tenantName|string|false|none|承租方名称|承租方名称|
|roomName|string|false|none|房源名称|房源名称|
|rentStartDate|string(date-time)|false|none|租期开始日期|租期开始日期|
|rentEndDate|string(date-time)|false|none|租期结束日期|租期结束日期|
|unenterNum|integer(int32)|false|none|未进场房源数|未进场房源数|
|createTime|string(date-time)|false|none|创建时间|创建时间|


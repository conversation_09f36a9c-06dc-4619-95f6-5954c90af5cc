---
title: 万洋
language_tabs:
  - shell: Shell
  - http: HTTP
  - javascript: JavaScript
  - ruby: Ruby
  - python: Python
  - php: PHP
  - java: Java
  - go: Go
toc_footers: []
includes: []
search: true
code_clipboard: true
highlight_theme: darkula
headingLevel: 2
generator: "@tarslib/widdershins v4.0.30"

---

# 万洋

Base URLs:

# Authentication

# 租赁中心前端/组织项目

<a id="opIdtree"></a>

## POST 获取组织树

POST /org/tree

> Body 请求参数

```json
{
  "name": "string",
  "relProjectFlag": 0,
  "projectIds": [
    "string"
  ],
  "merchantId": "string"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[SysOrgTreeDTO](#schemasysorgtreedto)| 否 |none|

> 返回示例

> 200 Response

```
[{"id":"string","code":"string","fullName":"string","name":"string","orgType":0,"level":0,"parentId":"string","children":[{"id":"string","code":"string","fullName":"string","name":"string","orgType":0,"level":0,"parentId":"string","children":[{"id":"string","code":"string","fullName":"string","name":"string","orgType":0,"level":0,"parentId":"string","children":[null]}]}]}]
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|default response|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|*anonymous*|[[SysOrgTreeVo](#schemasysorgtreevo)]|false|none||[组织树形结构对象]|
|» id|string|false|none||组织ID|
|» code|string|false|none||组织编码|
|» fullName|string|false|none||组织全称|
|» name|string|false|none||组织简称|
|» orgType|integer(int32)|false|none||组织类型; 1:组织  3：项目|
|» level|integer(int32)|false|none||组织等级 1：集团 2：区域事业部 3：片区 4：项目|
|» parentId|string|false|none||上级组织ID|
|» children|[[SysOrgTreeVo](#schemasysorgtreevo)]|false|none||子组织列表|
|»» id|string|false|none||组织ID|
|»» code|string|false|none||组织编码|
|»» fullName|string|false|none||组织全称|
|»» name|string|false|none||组织简称|
|»» orgType|integer(int32)|false|none||组织类型; 1:组织  3：项目|
|»» level|integer(int32)|false|none||组织等级 1：集团 2：区域事业部 3：片区 4：项目|
|»» parentId|string|false|none||上级组织ID|
|»» children|[[SysOrgTreeVo](#schemasysorgtreevo)]|false|none||子组织列表|

# 数据模型

<h2 id="tocS_SysOrgTreeDTO">SysOrgTreeDTO</h2>

<a id="schemasysorgtreedto"></a>
<a id="schema_SysOrgTreeDTO"></a>
<a id="tocSsysorgtreedto"></a>
<a id="tocssysorgtreedto"></a>

```json
{
  "name": "string",
  "relProjectFlag": 0,
  "projectIds": [
    "string"
  ],
  "merchantId": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|name|string|false|none|组织简称|组织简称|
|relProjectFlag|integer(int32)|false|none|未关联项目标识|未关联项目标识; 1:未关联  2：已关联|
|projectIds|[string]|false|none||none|
|merchantId|string|false|none|商业公司id|商业公司id|

<h2 id="tocS_SysOrgTreeVo">SysOrgTreeVo</h2>

<a id="schemasysorgtreevo"></a>
<a id="schema_SysOrgTreeVo"></a>
<a id="tocSsysorgtreevo"></a>
<a id="tocssysorgtreevo"></a>

```json
{
  "id": "string",
  "code": "string",
  "fullName": "string",
  "name": "string",
  "orgType": 0,
  "level": 0,
  "parentId": "string",
  "children": [
    {
      "id": "string",
      "code": "string",
      "fullName": "string",
      "name": "string",
      "orgType": 0,
      "level": 0,
      "parentId": "string",
      "children": [
        {
          "id": "string",
          "code": "string",
          "fullName": "string",
          "name": "string",
          "orgType": 0,
          "level": 0,
          "parentId": "string",
          "children": [
            {}
          ]
        }
      ]
    }
  ]
}

```

组织树形结构对象

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|string|false|none||组织ID|
|code|string|false|none||组织编码|
|fullName|string|false|none||组织全称|
|name|string|false|none||组织简称|
|orgType|integer(int32)|false|none||组织类型; 1:组织  3：项目|
|level|integer(int32)|false|none||组织等级 1：集团 2：区域事业部 3：片区 4：项目|
|parentId|string|false|none||上级组织ID|
|children|[[SysOrgTreeVo](#schemasysorgtreevo)]|false|none||子组织列表|


# 定单列表页面功能实现说明

## 概述
定单列表页面用于管理和查看所有定单的状态，包括待生效、已生效、已转签、已作废等不同状态的定单管理。

## 页面功能

### 1. 基础功能
- **顶部导航**: 标题显示"定单"，支持返回上一页
- **搜索功能**: 支持按客户姓名、房号搜索
- **状态筛选**: 四个状态标签（待生效 2、已生效 1、已转签 3、已作废 1）
- **排序筛选**: 下拉选择最新实收、最新创建、金额最高、金额最低
- **更多筛选**: 扩展筛选功能入口

### 2. 列表展示
每个定单项目显示：
- **房源标题**: 如 首开地块-工业D03#楼-2-02区间
- **状态标签**: 草稿、待收费等状态显示
- **基本信息**:
  - **客户姓名**: 张斌
  - **应收定金**: 1000元
  - **应收日期**: 2025-07-10
  - **创建日期**: 2025-07-09 23:00
  - **创建人**: 张晓丽

### 3. 操作功能
- **退定按钮**: 处理定单退定操作
- **收款码**: 对于待收费状态的定单显示二维码图标
- **状态切换**: 点击不同状态标签切换列表内容
- **动态下划线**: 激活状态下有蓝色下划线跟随

## 技术实现

### 1. 组件结构
```
BookingList.vue
├── 顶部导航 (van-nav-bar)
├── 搜索区域 (van-search)
├── 状态标签区域
│   ├── 状态选项卡 (自定义)
│   └── 激活下划线 (动态位置)
├── 筛选栏
│   ├── 排序下拉 (van-dropdown-menu)
│   └── 更多筛选入口
└── 列表区域
    ├── 加载状态 (van-loading)
    ├── 空状态 (van-empty)
    └── 定单列表 (自定义列表项)
```

### 2. 数据管理
- **状态管理**: 四种定单状态和对应数量
- **响应式数据**: 使用 Vue 3 Composition API
- **计算属性**: filteredList 实现搜索和状态筛选
- **动态样式**: 状态标签颜色和下划线位置

### 3. 样式设计
- **状态标签**: 不同状态使用不同颜色区分
  - 草稿: 灰色 (#f0f0f0)
  - 待收费: 蓝色 (#e6f7ff)
  - 已生效: 绿色 (#f6ffed)
- **动态下划线**: 跟随激活状态移动
- **收款码图标**: 垂直布局的二维码和文字

## 状态说明

### 1. 定单状态
- **待生效**: 新创建的定单，等待生效
- **已生效**: 已确认生效的定单
- **已转签**: 已转为正式合同的定单
- **已作废**: 取消或作废的定单

### 2. 状态标签
- **草稿**: 刚创建尚未提交的定单
- **待收费**: 等待收取费用的定单

## 交互特性

### 1. 搜索筛选
- 实时搜索客户姓名和房源信息
- 支持中文模糊匹配

### 2. 状态切换
- 点击状态标签切换列表内容
- 下划线动画跟随切换
- 激活状态高亮显示

### 3. 操作反馈
- 点击退定按钮显示确认提示
- 点击收款码显示二维码弹窗
- 各种操作都有Toast提示

## 后续扩展

### 1. API 集成
- 获取定单列表数据接口
- 搜索和筛选接口
- 退定操作接口
- 状态变更接口

### 2. 功能增强
- 批量操作功能
- 详情页面跳转
- 状态变更通知
- 导出功能

### 3. 性能优化
- 虚拟滚动支持
- 分页加载
- 数据缓存机制

## 文件位置
- 页面组件: `src/views/BookingList.vue`
- 路由配置: `src/router/index.ts` (已添加 /booking-list 路由)
- 首页入口: `src/views/Home.vue` (已添加菜单项)

## 开发规范遵循
- ✅ 使用 Vue 3 Composition API
- ✅ 使用 Vant UI 组件库
- ✅ 使用 TypeScript 类型系统
- ✅ 小驼峰命名法
- ✅ 4个空格缩进
- ✅ 响应式设计适配移动端 
.page {
  background-color: rgba(224, 224, 224, 1);
  position: relative;
  width: 100vw;
  height: 216.54vw;
  overflow: hidden;
}

.group_1 {
  background-color: rgba(241, 241, 241, 1);
  position: relative;
}

.section_1 {
  background-color: rgba(255, 255, 255, 1);
  padding: 4.13vw 3.73vw 5.33vw 4.53vw;
}

.box_1 {
  margin-left: 1.07vw;
}

.text-wrapper_1 {
  width: 14.4vw;
  height: 4.54vw;
  overflow-wrap: break-word;
  font-size: 0;
  letter-spacing: -0.5600000023841858px;
  font-family: Helvetica;
  font-weight: NaN;
  text-align: right;
  white-space: nowrap;
  line-height: 4.54vw;
}

.text_1 {
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 3.73vw;
  font-family: Helvetica;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 4.54vw;
}

.text_2 {
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 3.73vw;
  font-family: Helvetica;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 4.54vw;
}

.label_1 {
  width: 4.54vw;
  height: 2.8vw;
  margin: 0.53vw 0 1.2vw 58.26vw;
}

.label_2 {
  width: 4.14vw;
  height: 2.94vw;
  margin: 0.53vw 0 1.06vw 1.33vw;
}

.image_1 {
  width: 6.67vw;
  height: 3.2vw;
  margin: 0.4vw 0 0.93vw 1.33vw;
}

.box_2 {
  width: 48.4vw;
  margin: 4.26vw 43.33vw 0 0;
}

.label_3 {
  width: 4.94vw;
  height: 4.94vw;
  margin-bottom: 0.27vw;
}

.text_3 {
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 4.8vw;
  font-family: PingFangSC-Medium;
  font-weight: 500;
  text-align: right;
  white-space: nowrap;
  line-height: 4.8vw;
  margin-top: 0.4vw;
}

.section_2 {
  padding-bottom: 4.14vw;
}

.block_1 {
  background-color: rgba(255, 255, 255, 1);
  padding: 2.66vw 3.86vw 3.2vw 4.13vw;
}

.box_3 {
  background-color: rgba(243, 243, 243, 1);
  border-radius: 33px;
  padding: 2vw 47.86vw 1.86vw 3.2vw;
}

.image-text_1 {
  width: 40.94vw;
}

.label_4 {
  width: 3.74vw;
  height: 3.74vw;
  margin: 0.26vw 0 0.93vw 0;
}

.text-group_1 {
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 3.46vw;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 4.94vw;
}

.block_2 {
  background-color: rgba(255, 255, 255, 1);
  padding: 3.86vw 4vw 0 4.13vw;
}

.text-wrapper_2 {
  width: 91.87vw;
}

.text_4 {
  overflow-wrap: break-word;
  color: rgba(53, 131, 255, 1);
  font-size: 4vw;
  font-family: PingFangSC-Medium;
  font-weight: 500;
  text-align: right;
  white-space: nowrap;
  line-height: 5.6vw;
}

.text_5 {
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 4vw;
  font-family: PingFangSC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 5.6vw;
}

.text_6 {
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 4vw;
  font-family: PingFangSC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 5.6vw;
}

.text_7 {
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 4vw;
  font-family: PingFangSC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 5.6vw;
}

.box_4 {
  margin: 3.06vw 76.53vw 0 0.4vw;
}

.box_5 {
  background-color: rgba(53, 131, 255, 1);
  width: 14.94vw;
  height: 0.8vw;
}

.block_3 {
  width: 92vw;
  align-self: center;
  margin-top: 4vw;
}

.box_6 {
  background-color: rgba(255, 255, 255, 1);
  border-radius: 30px;
  padding: 1.33vw 4.13vw 1.73vw 3.86vw;
}

.image-text_2 {
  width: 17.07vw;
}

.text-group_2 {
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 3.46vw;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 4.94vw;
}

.thumbnail_1 {
  width: 1.6vw;
  height: 0.94vw;
  margin: 2vw 0 2vw 0;
}

.box_7 {
  background-color: rgba(255, 255, 255, 1);
  border-radius: 30px;
  padding: 1.33vw 3.6vw 1.73vw 3.2vw;
}

.image-text_3 {
  width: 18.27vw;
}

.text-group_3 {
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 3.46vw;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 4.94vw;
}

.image_2 {
  width: 3.2vw;
  height: 2.67vw;
  margin: 1.2vw 0 1.06vw 0;
}

.section_3 {
  background-color: rgba(255, 255, 255, 1);
  border-radius: 20px;
  width: 92vw;
  align-self: center;
  margin-top: -0.13vw;
  padding-bottom: 4vw;
}

.box_8 {
  width: 92vw;
  background: url(./img/SketchPnga7accd1592e0e0ea9873f644c038a9901b4a027e9d37549a351c44616a9e2526.png)
    100% no-repeat;
  background-size: 100% 100%;
  padding: 2.53vw 4vw 2.53vw 3.86vw;
}

.text_8 {
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 4vw;
  font-family: PingFangSC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 5.6vw;
}

.text-wrapper_3 {
  background-color: rgba(190, 189, 188, 1);
  border-radius: 4px;
  margin: 0.53vw 0 0.4vw 0;
  padding: 0.13vw 2.26vw 0.13vw 2.26vw;
}

.text_9 {
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 3.2vw;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 4.4vw;
}

.text-wrapper_4 {
  width: 22.4vw;
  height: 4.4vw;
  overflow-wrap: break-word;
  font-size: 0;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 4.4vw;
  margin: 2.66vw 65.6vw 0 4vw;
}

.text_10 {
  overflow-wrap: break-word;
  color: rgba(145, 145, 153, 1);
  font-size: 3.2vw;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 4.4vw;
}

.text_11 {
  overflow-wrap: break-word;
  color: rgba(51, 51, 51, 1);
  font-size: 3.2vw;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 4.4vw;
}

.text-wrapper_5 {
  width: 26.27vw;
  height: 4.4vw;
  overflow-wrap: break-word;
  font-size: 0;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 4.4vw;
  margin: 1.33vw 61.73vw 0 4vw;
}

.text_12 {
  overflow-wrap: break-word;
  color: rgba(145, 145, 153, 1);
  font-size: 3.2vw;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 4.4vw;
}

.text_13 {
  overflow-wrap: break-word;
  color: rgba(51, 51, 51, 1);
  font-size: 3.2vw;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 4.4vw;
}

.text-wrapper_6 {
  width: 34.54vw;
  height: 4.4vw;
  overflow-wrap: break-word;
  font-size: 0;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 4.4vw;
  margin: 1.33vw 53.46vw 0 4vw;
}

.text_14 {
  overflow-wrap: break-word;
  color: rgba(145, 145, 153, 1);
  font-size: 3.2vw;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 4.4vw;
}

.text_15 {
  overflow-wrap: break-word;
  color: rgba(51, 51, 51, 1);
  font-size: 3.2vw;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 4.4vw;
}

.text-wrapper_7 {
  width: 44.67vw;
  height: 4.4vw;
  overflow-wrap: break-word;
  font-size: 0;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 4.4vw;
  margin: 1.33vw 43.33vw 0 4vw;
}

.text_16 {
  overflow-wrap: break-word;
  color: rgba(145, 145, 153, 1);
  font-size: 3.2vw;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 4.4vw;
}

.text_17 {
  overflow-wrap: break-word;
  color: rgba(51, 51, 51, 1);
  font-size: 3.2vw;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 4.4vw;
}

.text-wrapper_8 {
  width: 22.4vw;
  height: 4.4vw;
  overflow-wrap: break-word;
  font-size: 0;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 4.4vw;
  margin: 1.33vw 65.6vw 0 4vw;
}

.text_18 {
  overflow-wrap: break-word;
  color: rgba(145, 145, 153, 1);
  font-size: 3.2vw;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 4.4vw;
}

.text_19 {
  overflow-wrap: break-word;
  color: rgba(51, 51, 51, 1);
  font-size: 3.2vw;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 4.4vw;
}

.text-wrapper_9 {
  background-color: rgba(53, 131, 255, 1);
  border-radius: 31px;
  margin: 0 4vw 0 67.33vw;
  padding: 1.2vw 6.8vw 1.06vw 6.93vw;
}

.text_20 {
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 3.46vw;
  font-family: PingFangSC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 4.94vw;
}

.section_4 {
  background-color: rgba(255, 255, 255, 1);
  border-radius: 20px;
  width: 92vw;
  align-self: center;
  margin-top: 3.2vw;
  padding-bottom: 4vw;
}

.box_9 {
  width: 92vw;
  background: url(./img/SketchPnga7accd1592e0e0ea9873f644c038a9901b4a027e9d37549a351c44616a9e2526.png)
    100% no-repeat;
  background-size: 100% 100%;
  padding: 2.53vw 4vw 2.53vw 4vw;
}

.text_21 {
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 4vw;
  font-family: PingFangSC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 5.6vw;
}

.text-wrapper_10 {
  background-color: rgba(58, 200, 212, 1);
  border-radius: 4px;
  margin: 0.53vw 0 0.4vw 0;
  padding: 0.13vw 1.73vw 0.13vw 1.86vw;
}

.text_22 {
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 3.2vw;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 4.4vw;
}

.box_10 {
  width: 84vw;
  align-self: center;
  margin-top: 3.07vw;
}

.group_2 {
}

.text-wrapper_11 {
  width: 22.4vw;
  height: 4.4vw;
  overflow-wrap: break-word;
  font-size: 0;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 4.4vw;
  margin-right: 22.27vw;
}

.text_23 {
  overflow-wrap: break-word;
  color: rgba(145, 145, 153, 1);
  font-size: 3.2vw;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 4.4vw;
}

.text_24 {
  overflow-wrap: break-word;
  color: rgba(51, 51, 51, 1);
  font-size: 3.2vw;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 4.4vw;
}

.text-wrapper_12 {
  width: 26.27vw;
  height: 4.4vw;
  overflow-wrap: break-word;
  font-size: 0;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 4.4vw;
  margin: 1.33vw 18.4vw 0 0;
}

.text_25 {
  overflow-wrap: break-word;
  color: rgba(145, 145, 153, 1);
  font-size: 3.2vw;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 4.4vw;
}

.text_26 {
  overflow-wrap: break-word;
  color: rgba(51, 51, 51, 1);
  font-size: 3.2vw;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 4.4vw;
}

.text-wrapper_13 {
  width: 34.54vw;
  height: 4.4vw;
  overflow-wrap: break-word;
  font-size: 0;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 4.4vw;
  margin: 1.33vw 10.13vw 0 0;
}

.text_27 {
  overflow-wrap: break-word;
  color: rgba(145, 145, 153, 1);
  font-size: 3.2vw;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 4.4vw;
}

.text_28 {
  overflow-wrap: break-word;
  color: rgba(51, 51, 51, 1);
  font-size: 3.2vw;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 4.4vw;
}

.text-wrapper_14 {
  width: 44.67vw;
  height: 4.4vw;
  overflow-wrap: break-word;
  font-size: 0;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 4.4vw;
  margin-top: 1.34vw;
}

.text_29 {
  overflow-wrap: break-word;
  color: rgba(145, 145, 153, 1);
  font-size: 3.2vw;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 4.4vw;
}

.text_30 {
  overflow-wrap: break-word;
  color: rgba(51, 51, 51, 1);
  font-size: 3.2vw;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 4.4vw;
}

.group_3 {
  border-radius: 11px;
  border: 1px solid rgba(160, 170, 185, 0.4);
  padding-top: 2.27vw;
  margin: 4vw 0 3.46vw 0;
}

.label_5 {
  width: 5.34vw;
  height: 5.34vw;
  align-self: center;
}

.text-wrapper_15 {
  background-color: rgba(160, 170, 185, 1);
  border-radius: 0px 0px 11px 11px;
  margin-top: 2.14vw;
  padding: 0.13vw 3.06vw 0.4vw 3.06vw;
}

.text_31 {
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 2.66vw;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 3.74vw;
}

.text-wrapper_16 {
  width: 22.4vw;
  height: 4.4vw;
  overflow-wrap: break-word;
  font-size: 0;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 4.4vw;
  margin: 1.33vw 65.6vw 0 4vw;
}

.text_32 {
  overflow-wrap: break-word;
  color: rgba(145, 145, 153, 1);
  font-size: 3.2vw;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 4.4vw;
}

.text_33 {
  overflow-wrap: break-word;
  color: rgba(51, 51, 51, 1);
  font-size: 3.2vw;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 4.4vw;
}

.text-wrapper_17 {
  background-color: rgba(53, 131, 255, 1);
  border-radius: 31px;
  margin: 0 4vw 0 64.66vw;
  padding: 1.6vw 8.13vw 1.6vw 8.26vw;
}

.text_34 {
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 3.46vw;
  font-family: PingFangSC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 4.94vw;
}

.section_5 {
  background-color: rgba(255, 255, 255, 1);
  border-radius: 20px;
  align-self: center;
  margin-top: 34.67vw;
  width: 92vw;
}

.box_11 {
  background: url(./img/SketchPng4ddba6e94441f4fcd08e73cd03ecfb6caf9a42c668b3f9be836f6abc02637efa.png)
    100% no-repeat;
  background-size: 100% 100%;
  padding: 3.86vw 36.4vw 0 4vw;
}

.image-text_4 {
  width: 51.6vw;
}

.section_6 {
  background-color: rgba(251, 120, 29, 1);
  border-radius: 16px;
  width: 0.8vw;
  height: 1.34vw;
  margin-top: 0.94vw;
}

.text-group_4 {
  height: 2.27vw;
  overflow-wrap: break-word;
  color: rgba(36, 36, 51, 1);
  font-size: 4vw;
  font-family: PingFangSC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 5.6vw;
}

.image-wrapper_1 {
  height: 9.07vw;
  background: url(./img/SketchPngcccc2ff3387e4d5bc9ddad591e430dd76c0654f17d0c252c0cd727c1aaf2e235.png)
    100% no-repeat;
  background-size: 100% 100%;
  width: 100vw;
  position: absolute;
  left: 0;
  top: 207.47vw;
}

.image_3 {
  width: 100vw;
  height: 9.07vw;
}

html {
  font-size: 37.5px;
}

.page {
  background-color: rgba(224, 224, 224, 1);
  position: relative;
  width: 20rem;
  height: 43.307rem;
  overflow: hidden;
}

.group_1 {
  background-color: rgba(241, 241, 241, 1);
  position: relative;
}

.section_1 {
  background-color: rgba(255, 255, 255, 1);
  padding: 0.827rem 0.747rem 1.067rem 0.907rem;
}

.box_1 {
  margin-left: 0.214rem;
}

.text-wrapper_1 {
  width: 2.88rem;
  height: 0.907rem;
  overflow-wrap: break-word;
  font-size: 0;
  letter-spacing: -0.5600000023841858px;
  font-family: Helvetica;
  font-weight: NaN;
  text-align: right;
  white-space: nowrap;
  line-height: 0.907rem;
}

.text_1 {
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.746rem;
  font-family: Helvetica;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.907rem;
}

.text_2 {
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.746rem;
  font-family: Helvetica;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.907rem;
}

.label_1 {
  width: 0.907rem;
  height: 0.56rem;
  margin: 0.107rem 0 0.24rem 11.654rem;
}

.label_2 {
  width: 0.827rem;
  height: 0.587rem;
  margin: 0.107rem 0 0.214rem 0.267rem;
}

.image_1 {
  width: 1.334rem;
  height: 0.64rem;
  margin: 0.08rem 0 0.187rem 0.267rem;
}

.box_2 {
  width: 9.68rem;
  margin: 0.854rem 8.667rem 0 0;
}

.label_3 {
  width: 0.987rem;
  height: 0.987rem;
  margin-bottom: 0.054rem;
}

.text_3 {
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.96rem;
  font-family: PingFangSC-Medium;
  font-weight: 500;
  text-align: right;
  white-space: nowrap;
  line-height: 0.96rem;
  margin-top: 0.08rem;
}

.section_2 {
  padding-bottom: 0.827rem;
}

.block_1 {
  background-color: rgba(255, 255, 255, 1);
  padding: 0.534rem 0.774rem 0.64rem 0.827rem;
}

.box_3 {
  background-color: rgba(243, 243, 243, 1);
  border-radius: 33px;
  padding: 0.4rem 9.574rem 0.374rem 0.64rem;
}

.image-text_1 {
  width: 8.187rem;
}

.label_4 {
  width: 0.747rem;
  height: 0.747rem;
  margin: 0.054rem 0 0.187rem 0;
}

.text-group_1 {
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.693rem;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.987rem;
}

.block_2 {
  background-color: rgba(255, 255, 255, 1);
  padding: 0.774rem 0.8rem 0 0.827rem;
}

.text-wrapper_2 {
  width: 18.374rem;
}

.text_4 {
  overflow-wrap: break-word;
  color: rgba(53, 131, 255, 1);
  font-size: 0.8rem;
  font-family: PingFangSC-Medium;
  font-weight: 500;
  text-align: right;
  white-space: nowrap;
  line-height: 1.12rem;
}

.text_5 {
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.8rem;
  font-family: PingFangSC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 1.12rem;
}

.text_6 {
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.8rem;
  font-family: PingFangSC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 1.12rem;
}

.text_7 {
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.8rem;
  font-family: PingFangSC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 1.12rem;
}

.box_4 {
  margin: 0.614rem 15.307rem 0 0.08rem;
}

.box_5 {
  background-color: rgba(53, 131, 255, 1);
  width: 2.987rem;
  height: 0.16rem;
}

.block_3 {
  width: 18.4rem;
  align-self: center;
  margin-top: 0.8rem;
}

.box_6 {
  background-color: rgba(255, 255, 255, 1);
  border-radius: 30px;
  padding: 0.267rem 0.827rem 0.347rem 0.774rem;
}

.image-text_2 {
  width: 3.414rem;
}

.text-group_2 {
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.693rem;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.987rem;
}

.thumbnail_1 {
  width: 0.32rem;
  height: 0.187rem;
  margin: 0.4rem 0 0.4rem 0;
}

.box_7 {
  background-color: rgba(255, 255, 255, 1);
  border-radius: 30px;
  padding: 0.267rem 0.72rem 0.347rem 0.64rem;
}

.image-text_3 {
  width: 3.654rem;
}

.text-group_3 {
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.693rem;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.987rem;
}

.image_2 {
  width: 0.64rem;
  height: 0.534rem;
  margin: 0.24rem 0 0.214rem 0;
}

.section_3 {
  background-color: rgba(255, 255, 255, 1);
  border-radius: 20px;
  width: 18.4rem;
  align-self: center;
  margin-top: -0.026rem;
  padding-bottom: 0.8rem;
}

.box_8 {
  width: 18.4rem;
  background: url(./img/SketchPnga7accd1592e0e0ea9873f644c038a9901b4a027e9d37549a351c44616a9e2526.png)
    100% no-repeat;
  background-size: 100% 100%;
  padding: 0.507rem 0.8rem 0.507rem 0.774rem;
}

.text_8 {
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.8rem;
  font-family: PingFangSC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 1.12rem;
}

.text-wrapper_3 {
  background-color: rgba(190, 189, 188, 1);
  border-radius: 4px;
  margin: 0.107rem 0 0.08rem 0;
  padding: 0.027rem 0.454rem 0.027rem 0.454rem;
}

.text_9 {
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 0.64rem;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.88rem;
}

.text-wrapper_4 {
  width: 4.48rem;
  height: 0.88rem;
  overflow-wrap: break-word;
  font-size: 0;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.88rem;
  margin: 0.534rem 13.12rem 0 0.8rem;
}

.text_10 {
  overflow-wrap: break-word;
  color: rgba(145, 145, 153, 1);
  font-size: 0.64rem;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.88rem;
}

.text_11 {
  overflow-wrap: break-word;
  color: rgba(51, 51, 51, 1);
  font-size: 0.64rem;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.88rem;
}

.text-wrapper_5 {
  width: 5.254rem;
  height: 0.88rem;
  overflow-wrap: break-word;
  font-size: 0;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.88rem;
  margin: 0.267rem 12.347rem 0 0.8rem;
}

.text_12 {
  overflow-wrap: break-word;
  color: rgba(145, 145, 153, 1);
  font-size: 0.64rem;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.88rem;
}

.text_13 {
  overflow-wrap: break-word;
  color: rgba(51, 51, 51, 1);
  font-size: 0.64rem;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.88rem;
}

.text-wrapper_6 {
  width: 6.907rem;
  height: 0.88rem;
  overflow-wrap: break-word;
  font-size: 0;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.88rem;
  margin: 0.267rem 10.694rem 0 0.8rem;
}

.text_14 {
  overflow-wrap: break-word;
  color: rgba(145, 145, 153, 1);
  font-size: 0.64rem;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.88rem;
}

.text_15 {
  overflow-wrap: break-word;
  color: rgba(51, 51, 51, 1);
  font-size: 0.64rem;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.88rem;
}

.text-wrapper_7 {
  width: 8.934rem;
  height: 0.88rem;
  overflow-wrap: break-word;
  font-size: 0;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.88rem;
  margin: 0.267rem 8.667rem 0 0.8rem;
}

.text_16 {
  overflow-wrap: break-word;
  color: rgba(145, 145, 153, 1);
  font-size: 0.64rem;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.88rem;
}

.text_17 {
  overflow-wrap: break-word;
  color: rgba(51, 51, 51, 1);
  font-size: 0.64rem;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.88rem;
}

.text-wrapper_8 {
  width: 4.48rem;
  height: 0.88rem;
  overflow-wrap: break-word;
  font-size: 0;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.88rem;
  margin: 0.267rem 13.12rem 0 0.8rem;
}

.text_18 {
  overflow-wrap: break-word;
  color: rgba(145, 145, 153, 1);
  font-size: 0.64rem;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.88rem;
}

.text_19 {
  overflow-wrap: break-word;
  color: rgba(51, 51, 51, 1);
  font-size: 0.64rem;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.88rem;
}

.text-wrapper_9 {
  background-color: rgba(53, 131, 255, 1);
  border-radius: 31px;
  margin: 0 0.8rem 0 13.467rem;
  padding: 0.24rem 1.36rem 0.214rem 1.387rem;
}

.text_20 {
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 0.693rem;
  font-family: PingFangSC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 0.987rem;
}

.section_4 {
  background-color: rgba(255, 255, 255, 1);
  border-radius: 20px;
  width: 18.4rem;
  align-self: center;
  margin-top: 0.64rem;
  padding-bottom: 0.8rem;
}

.box_9 {
  width: 18.4rem;
  background: url(./img/SketchPnga7accd1592e0e0ea9873f644c038a9901b4a027e9d37549a351c44616a9e2526.png)
    100% no-repeat;
  background-size: 100% 100%;
  padding: 0.507rem 0.8rem 0.507rem 0.8rem;
}

.text_21 {
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.8rem;
  font-family: PingFangSC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 1.12rem;
}

.text-wrapper_10 {
  background-color: rgba(58, 200, 212, 1);
  border-radius: 4px;
  margin: 0.107rem 0 0.08rem 0;
  padding: 0.027rem 0.347rem 0.027rem 0.374rem;
}

.text_22 {
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 0.64rem;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.88rem;
}

.box_10 {
  width: 16.8rem;
  align-self: center;
  margin-top: 0.614rem;
}

.group_2 {
}

.text-wrapper_11 {
  width: 4.48rem;
  height: 0.88rem;
  overflow-wrap: break-word;
  font-size: 0;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.88rem;
  margin-right: 4.454rem;
}

.text_23 {
  overflow-wrap: break-word;
  color: rgba(145, 145, 153, 1);
  font-size: 0.64rem;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.88rem;
}

.text_24 {
  overflow-wrap: break-word;
  color: rgba(51, 51, 51, 1);
  font-size: 0.64rem;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.88rem;
}

.text-wrapper_12 {
  width: 5.254rem;
  height: 0.88rem;
  overflow-wrap: break-word;
  font-size: 0;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.88rem;
  margin: 0.267rem 3.68rem 0 0;
}

.text_25 {
  overflow-wrap: break-word;
  color: rgba(145, 145, 153, 1);
  font-size: 0.64rem;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.88rem;
}

.text_26 {
  overflow-wrap: break-word;
  color: rgba(51, 51, 51, 1);
  font-size: 0.64rem;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.88rem;
}

.text-wrapper_13 {
  width: 6.907rem;
  height: 0.88rem;
  overflow-wrap: break-word;
  font-size: 0;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.88rem;
  margin: 0.267rem 2.027rem 0 0;
}

.text_27 {
  overflow-wrap: break-word;
  color: rgba(145, 145, 153, 1);
  font-size: 0.64rem;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.88rem;
}

.text_28 {
  overflow-wrap: break-word;
  color: rgba(51, 51, 51, 1);
  font-size: 0.64rem;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.88rem;
}

.text-wrapper_14 {
  width: 8.934rem;
  height: 0.88rem;
  overflow-wrap: break-word;
  font-size: 0;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.88rem;
  margin-top: 0.267rem;
}

.text_29 {
  overflow-wrap: break-word;
  color: rgba(145, 145, 153, 1);
  font-size: 0.64rem;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.88rem;
}

.text_30 {
  overflow-wrap: break-word;
  color: rgba(51, 51, 51, 1);
  font-size: 0.64rem;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.88rem;
}

.group_3 {
  border-radius: 11px;
  border: 1px solid rgba(160, 170, 185, 0.4);
  padding-top: 0.454rem;
  margin: 0.8rem 0 0.694rem 0;
}

.label_5 {
  width: 1.067rem;
  height: 1.067rem;
  align-self: center;
}

.text-wrapper_15 {
  background-color: rgba(160, 170, 185, 1);
  border-radius: 0px 0px 11px 11px;
  margin-top: 0.427rem;
  padding: 0.027rem 0.614rem 0.08rem 0.614rem;
}

.text_31 {
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 0.533rem;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 0.747rem;
}

.text-wrapper_16 {
  width: 4.48rem;
  height: 0.88rem;
  overflow-wrap: break-word;
  font-size: 0;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.88rem;
  margin: 0.267rem 13.12rem 0 0.8rem;
}

.text_32 {
  overflow-wrap: break-word;
  color: rgba(145, 145, 153, 1);
  font-size: 0.64rem;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.88rem;
}

.text_33 {
  overflow-wrap: break-word;
  color: rgba(51, 51, 51, 1);
  font-size: 0.64rem;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.88rem;
}

.text-wrapper_17 {
  background-color: rgba(53, 131, 255, 1);
  border-radius: 31px;
  margin: 0 0.8rem 0 12.934rem;
  padding: 0.32rem 1.627rem 0.32rem 1.654rem;
}

.text_34 {
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 0.693rem;
  font-family: PingFangSC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 0.987rem;
}

.section_5 {
  background-color: rgba(255, 255, 255, 1);
  border-radius: 20px;
  align-self: center;
  margin-top: 6.934rem;
  width: 18.4rem;
}

.box_11 {
  background: url(./img/SketchPng4ddba6e94441f4fcd08e73cd03ecfb6caf9a42c668b3f9be836f6abc02637efa.png)
    100% no-repeat;
  background-size: 100% 100%;
  padding: 0.774rem 7.28rem 0 0.8rem;
}

.image-text_4 {
  width: 10.32rem;
}

.section_6 {
  background-color: rgba(251, 120, 29, 1);
  border-radius: 16px;
  width: 0.16rem;
  height: 0.267rem;
  margin-top: 0.187rem;
}

.text-group_4 {
  height: 0.454rem;
  overflow-wrap: break-word;
  color: rgba(36, 36, 51, 1);
  font-size: 0.8rem;
  font-family: PingFangSC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 1.12rem;
}

.image-wrapper_1 {
  height: 1.814rem;
  background: url(./img/SketchPngcccc2ff3387e4d5bc9ddad591e430dd76c0654f17d0c252c0cd727c1aaf2e235.png)
    100% no-repeat;
  background-size: 100% 100%;
  width: 20rem;
  position: absolute;
  left: 0;
  top: 41.494rem;
}

.image_3 {
  width: 20rem;
  height: 1.814rem;
}

.page {
  background-color: rgba(255, 255, 255, 1);
  position: relative;
  width: 750px;
  height: 1624px;
  overflow: hidden;
}

.section_1 {
  background-color: rgba(241, 241, 241, 1);
  height: 1624px;
}

.group_1 {
  background-color: rgba(255, 255, 255, 1);
  padding: 31px 28px 40px 34px;
}

.group_2 {
  margin-left: 8px;
}

.text-wrapper_1 {
  width: 108px;
  height: 34px;
  overflow-wrap: break-word;
  font-size: 0;
  letter-spacing: -0.5600000023841858px;
  font-family: Helvetica;
  font-weight: NaN;
  text-align: right;
  white-space: nowrap;
  line-height: 34px;
}

.text_1 {
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 28px;
  font-family: Helvetica;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 34px;
}

.text_2 {
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 28px;
  font-family: Helvetica;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 34px;
}

.label_1 {
  width: 34px;
  height: 21px;
  margin: 4px 0 9px 437px;
}

.label_2 {
  width: 31px;
  height: 22px;
  margin: 4px 0 8px 10px;
}

.image_1 {
  width: 50px;
  height: 24px;
  margin: 3px 0 7px 10px;
}

.group_3 {
  width: 381px;
  margin: 32px 307px 0 0;
}

.label_3 {
  width: 37px;
  height: 37px;
  margin-bottom: 2px;
}

.text_3 {
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 36px;
  font-family: PingFangSC-Medium;
  font-weight: 500;
  text-align: right;
  white-space: nowrap;
  line-height: 36px;
  margin-top: 3px;
}

.group_4 {
  background-color: rgba(255, 255, 255, 1);
  border-radius: 20px;
  width: 690px;
  align-self: center;
  margin-top: 20px;
  padding-bottom: 30px;
}

.section_2 {
  background: url(./img/SketchPnga7accd1592e0e0ea9873f644c038a9901b4a027e9d37549a351c44616a9e2526.png)
    100% no-repeat;
  background-size: 100% 100%;
  padding: 19px 520px 19px 26px;
}

.image-text_1 {
  width: 144px;
}

.section_3 {
  background-color: rgba(53, 131, 255, 1);
  border-radius: 16px;
  width: 8px;
  height: 32px;
  margin: 5px 0 5px 0;
}

.text-group_1 {
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 30px;
  font-family: PingFangSC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 42px;
}

.text-wrapper_2 {
  width: 168px;
  height: 33px;
  overflow-wrap: break-word;
  font-size: 0;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 33px;
  margin: 23px 492px 0 30px;
}

.text_4 {
  overflow-wrap: break-word;
  color: rgba(145, 145, 153, 1);
  font-size: 24px;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 33px;
}

.text_5 {
  overflow-wrap: break-word;
  color: rgba(51, 51, 51, 1);
  font-size: 24px;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 33px;
}

.text-wrapper_3 {
  width: 483px;
  height: 33px;
  overflow-wrap: break-word;
  font-size: 0;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 33px;
  margin: 10px 177px 0 30px;
}

.text_6 {
  overflow-wrap: break-word;
  color: rgba(145, 145, 153, 1);
  font-size: 24px;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 33px;
}

.text_7 {
  overflow-wrap: break-word;
  color: rgba(51, 51, 51, 1);
  font-size: 24px;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 33px;
}

.text-wrapper_4 {
  width: 197px;
  height: 33px;
  overflow-wrap: break-word;
  font-size: 0;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 33px;
  margin: 10px 463px 0 30px;
}

.text_8 {
  overflow-wrap: break-word;
  color: rgba(145, 145, 153, 1);
  font-size: 24px;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 33px;
}

.text_9 {
  overflow-wrap: break-word;
  color: rgba(51, 51, 51, 1);
  font-size: 24px;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 33px;
}

.text-wrapper_5 {
  width: 159px;
  height: 33px;
  overflow-wrap: break-word;
  font-size: 0;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 33px;
  margin: 10px 501px 0 30px;
}

.text_10 {
  overflow-wrap: break-word;
  color: rgba(145, 145, 153, 1);
  font-size: 24px;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 33px;
}

.text_11 {
  overflow-wrap: break-word;
  color: rgba(51, 51, 51, 1);
  font-size: 24px;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 33px;
}

.text-wrapper_6 {
  width: 259px;
  height: 33px;
  overflow-wrap: break-word;
  font-size: 0;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 33px;
  margin: 10px 401px 0 30px;
}

.text_12 {
  overflow-wrap: break-word;
  color: rgba(145, 145, 153, 1);
  font-size: 24px;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 33px;
}

.text_13 {
  overflow-wrap: break-word;
  color: rgba(51, 51, 51, 1);
  font-size: 24px;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 33px;
}

.text-wrapper_7 {
  width: 335px;
  height: 33px;
  overflow-wrap: break-word;
  font-size: 0;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 33px;
  margin: 10px 325px 0 30px;
}

.text_14 {
  overflow-wrap: break-word;
  color: rgba(145, 145, 153, 1);
  font-size: 24px;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 33px;
}

.text_15 {
  overflow-wrap: break-word;
  color: rgba(51, 51, 51, 1);
  font-size: 24px;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 33px;
}

.text-wrapper_8 {
  width: 168px;
  height: 33px;
  overflow-wrap: break-word;
  font-size: 0;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 33px;
  margin: 10px 492px 0 30px;
}

.text_16 {
  overflow-wrap: break-word;
  color: rgba(145, 145, 153, 1);
  font-size: 24px;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 33px;
}

.text_17 {
  overflow-wrap: break-word;
  color: rgba(51, 51, 51, 1);
  font-size: 24px;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 33px;
}

.group_5 {
  padding: 20px 30px 19px 30px;
}

.box_1 {
  background-color: rgba(255, 255, 255, 1);
  border-radius: 20px;
  padding-bottom: 30px;
}

.group_6 {
  background: url(./img/SketchPnga7accd1592e0e0ea9873f644c038a9901b4a027e9d37549a351c44616a9e2526.png)
    100% no-repeat;
  background-size: 100% 100%;
  padding: 19px 520px 19px 26px;
}

.image-text_2 {
  width: 144px;
}

.group_7 {
  background-color: rgba(53, 131, 255, 1);
  border-radius: 16px;
  width: 8px;
  height: 32px;
  margin: 5px 0 5px 0;
}

.text-group_2 {
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 30px;
  font-family: PingFangSC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 42px;
}

.text-wrapper_9 {
  width: 216px;
  height: 33px;
  overflow-wrap: break-word;
  font-size: 0;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 33px;
  margin: 23px 444px 0 30px;
}

.text_18 {
  overflow-wrap: break-word;
  color: rgba(145, 145, 153, 1);
  font-size: 24px;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 33px;
}

.text_19 {
  overflow-wrap: break-word;
  color: rgba(51, 51, 51, 1);
  font-size: 24px;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 33px;
}

.text-wrapper_10 {
  width: 483px;
  height: 33px;
  overflow-wrap: break-word;
  font-size: 0;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 33px;
  margin: 10px 177px 0 30px;
}

.text_20 {
  overflow-wrap: break-word;
  color: rgba(145, 145, 153, 1);
  font-size: 24px;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 33px;
}

.text_21 {
  overflow-wrap: break-word;
  color: rgba(51, 51, 51, 1);
  font-size: 24px;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 33px;
}

.text-wrapper_11 {
  width: 415px;
  height: 33px;
  overflow-wrap: break-word;
  font-size: 0;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 33px;
  margin: 10px 245px 0 30px;
}

.text_22 {
  overflow-wrap: break-word;
  color: rgba(145, 145, 153, 1);
  font-size: 24px;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 33px;
}

.text_23 {
  overflow-wrap: break-word;
  color: rgba(51, 51, 51, 1);
  font-size: 24px;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 33px;
}

.text-wrapper_12 {
  width: 262px;
  height: 33px;
  overflow-wrap: break-word;
  font-size: 0;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 33px;
  margin: 10px 398px 0 30px;
}

.text_24 {
  overflow-wrap: break-word;
  color: rgba(145, 145, 153, 1);
  font-size: 24px;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 33px;
}

.text_25 {
  overflow-wrap: break-word;
  color: rgba(51, 51, 51, 1);
  font-size: 24px;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 33px;
}

.text-wrapper_13 {
  width: 168px;
  height: 33px;
  overflow-wrap: break-word;
  font-size: 0;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 33px;
  margin: 10px 492px 0 30px;
}

.text_26 {
  overflow-wrap: break-word;
  color: rgba(145, 145, 153, 1);
  font-size: 24px;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 33px;
}

.text_27 {
  overflow-wrap: break-word;
  color: rgba(51, 51, 51, 1);
  font-size: 24px;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 33px;
}

.image_2 {
  width: 268px;
  height: 10px;
  align-self: center;
  margin-top: 618px;
}

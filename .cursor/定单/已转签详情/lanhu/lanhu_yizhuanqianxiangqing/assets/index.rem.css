html {
  font-size: 37.5px;
}

.page {
  background-color: rgba(255, 255, 255, 1);
  position: relative;
  width: 20rem;
  height: 43.307rem;
  overflow: hidden;
}

.section_1 {
  background-color: rgba(241, 241, 241, 1);
  height: 43.307rem;
}

.group_1 {
  background-color: rgba(255, 255, 255, 1);
  padding: 0.827rem 0.747rem 1.067rem 0.907rem;
}

.group_2 {
  margin-left: 0.214rem;
}

.text-wrapper_1 {
  width: 2.88rem;
  height: 0.907rem;
  overflow-wrap: break-word;
  font-size: 0;
  letter-spacing: -0.5600000023841858px;
  font-family: Helvetica;
  font-weight: NaN;
  text-align: right;
  white-space: nowrap;
  line-height: 0.907rem;
}

.text_1 {
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.746rem;
  font-family: Helvetica;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.907rem;
}

.text_2 {
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.746rem;
  font-family: Helvetica;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.907rem;
}

.label_1 {
  width: 0.907rem;
  height: 0.56rem;
  margin: 0.107rem 0 0.24rem 11.654rem;
}

.label_2 {
  width: 0.827rem;
  height: 0.587rem;
  margin: 0.107rem 0 0.214rem 0.267rem;
}

.image_1 {
  width: 1.334rem;
  height: 0.64rem;
  margin: 0.08rem 0 0.187rem 0.267rem;
}

.group_3 {
  width: 10.16rem;
  margin: 0.854rem 8.187rem 0 0;
}

.label_3 {
  width: 0.987rem;
  height: 0.987rem;
  margin-bottom: 0.054rem;
}

.text_3 {
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.96rem;
  font-family: PingFangSC-Medium;
  font-weight: 500;
  text-align: right;
  white-space: nowrap;
  line-height: 0.96rem;
  margin-top: 0.08rem;
}

.group_4 {
  background-color: rgba(255, 255, 255, 1);
  border-radius: 20px;
  width: 18.4rem;
  align-self: center;
  margin-top: 0.534rem;
  padding-bottom: 0.8rem;
}

.section_2 {
  background: url(./img/SketchPnga7accd1592e0e0ea9873f644c038a9901b4a027e9d37549a351c44616a9e2526.png)
    100% no-repeat;
  background-size: 100% 100%;
  padding: 0.507rem 13.867rem 0.507rem 0.694rem;
}

.image-text_1 {
  width: 3.84rem;
}

.section_3 {
  background-color: rgba(53, 131, 255, 1);
  border-radius: 16px;
  width: 0.214rem;
  height: 0.854rem;
  margin: 0.134rem 0 0.134rem 0;
}

.text-group_1 {
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.8rem;
  font-family: PingFangSC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 1.12rem;
}

.text-wrapper_2 {
  width: 4.48rem;
  height: 0.88rem;
  overflow-wrap: break-word;
  font-size: 0;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.88rem;
  margin: 0.614rem 13.12rem 0 0.8rem;
}

.text_4 {
  overflow-wrap: break-word;
  color: rgba(145, 145, 153, 1);
  font-size: 0.64rem;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.88rem;
}

.text_5 {
  overflow-wrap: break-word;
  color: rgba(51, 51, 51, 1);
  font-size: 0.64rem;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.88rem;
}

.text-wrapper_3 {
  width: 12.88rem;
  height: 0.88rem;
  overflow-wrap: break-word;
  font-size: 0;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.88rem;
  margin: 0.267rem 4.72rem 0 0.8rem;
}

.text_6 {
  overflow-wrap: break-word;
  color: rgba(145, 145, 153, 1);
  font-size: 0.64rem;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.88rem;
}

.text_7 {
  overflow-wrap: break-word;
  color: rgba(51, 51, 51, 1);
  font-size: 0.64rem;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.88rem;
}

.text-wrapper_4 {
  width: 5.254rem;
  height: 0.88rem;
  overflow-wrap: break-word;
  font-size: 0;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.88rem;
  margin: 0.267rem 12.347rem 0 0.8rem;
}

.text_8 {
  overflow-wrap: break-word;
  color: rgba(145, 145, 153, 1);
  font-size: 0.64rem;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.88rem;
}

.text_9 {
  overflow-wrap: break-word;
  color: rgba(51, 51, 51, 1);
  font-size: 0.64rem;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.88rem;
}

.text-wrapper_5 {
  width: 4.24rem;
  height: 0.88rem;
  overflow-wrap: break-word;
  font-size: 0;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.88rem;
  margin: 0.267rem 13.36rem 0 0.8rem;
}

.text_10 {
  overflow-wrap: break-word;
  color: rgba(145, 145, 153, 1);
  font-size: 0.64rem;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.88rem;
}

.text_11 {
  overflow-wrap: break-word;
  color: rgba(51, 51, 51, 1);
  font-size: 0.64rem;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.88rem;
}

.text-wrapper_6 {
  width: 6.907rem;
  height: 0.88rem;
  overflow-wrap: break-word;
  font-size: 0;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.88rem;
  margin: 0.267rem 10.694rem 0 0.8rem;
}

.text_12 {
  overflow-wrap: break-word;
  color: rgba(145, 145, 153, 1);
  font-size: 0.64rem;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.88rem;
}

.text_13 {
  overflow-wrap: break-word;
  color: rgba(51, 51, 51, 1);
  font-size: 0.64rem;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.88rem;
}

.text-wrapper_7 {
  width: 8.934rem;
  height: 0.88rem;
  overflow-wrap: break-word;
  font-size: 0;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.88rem;
  margin: 0.267rem 8.667rem 0 0.8rem;
}

.text_14 {
  overflow-wrap: break-word;
  color: rgba(145, 145, 153, 1);
  font-size: 0.64rem;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.88rem;
}

.text_15 {
  overflow-wrap: break-word;
  color: rgba(51, 51, 51, 1);
  font-size: 0.64rem;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.88rem;
}

.text-wrapper_8 {
  width: 4.48rem;
  height: 0.88rem;
  overflow-wrap: break-word;
  font-size: 0;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.88rem;
  margin: 0.267rem 13.12rem 0 0.8rem;
}

.text_16 {
  overflow-wrap: break-word;
  color: rgba(145, 145, 153, 1);
  font-size: 0.64rem;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.88rem;
}

.text_17 {
  overflow-wrap: break-word;
  color: rgba(51, 51, 51, 1);
  font-size: 0.64rem;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.88rem;
}

.group_5 {
  padding: 0.534rem 0.8rem 0.507rem 0.8rem;
}

.box_1 {
  background-color: rgba(255, 255, 255, 1);
  border-radius: 20px;
  padding-bottom: 0.8rem;
}

.group_6 {
  background: url(./img/SketchPnga7accd1592e0e0ea9873f644c038a9901b4a027e9d37549a351c44616a9e2526.png)
    100% no-repeat;
  background-size: 100% 100%;
  padding: 0.507rem 13.867rem 0.507rem 0.694rem;
}

.image-text_2 {
  width: 3.84rem;
}

.group_7 {
  background-color: rgba(53, 131, 255, 1);
  border-radius: 16px;
  width: 0.214rem;
  height: 0.854rem;
  margin: 0.134rem 0 0.134rem 0;
}

.text-group_2 {
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.8rem;
  font-family: PingFangSC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 1.12rem;
}

.text-wrapper_9 {
  width: 5.76rem;
  height: 0.88rem;
  overflow-wrap: break-word;
  font-size: 0;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.88rem;
  margin: 0.614rem 11.84rem 0 0.8rem;
}

.text_18 {
  overflow-wrap: break-word;
  color: rgba(145, 145, 153, 1);
  font-size: 0.64rem;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.88rem;
}

.text_19 {
  overflow-wrap: break-word;
  color: rgba(51, 51, 51, 1);
  font-size: 0.64rem;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.88rem;
}

.text-wrapper_10 {
  width: 12.88rem;
  height: 0.88rem;
  overflow-wrap: break-word;
  font-size: 0;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.88rem;
  margin: 0.267rem 4.72rem 0 0.8rem;
}

.text_20 {
  overflow-wrap: break-word;
  color: rgba(145, 145, 153, 1);
  font-size: 0.64rem;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.88rem;
}

.text_21 {
  overflow-wrap: break-word;
  color: rgba(51, 51, 51, 1);
  font-size: 0.64rem;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.88rem;
}

.text-wrapper_11 {
  width: 11.067rem;
  height: 0.88rem;
  overflow-wrap: break-word;
  font-size: 0;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.88rem;
  margin: 0.267rem 6.534rem 0 0.8rem;
}

.text_22 {
  overflow-wrap: break-word;
  color: rgba(145, 145, 153, 1);
  font-size: 0.64rem;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.88rem;
}

.text_23 {
  overflow-wrap: break-word;
  color: rgba(51, 51, 51, 1);
  font-size: 0.64rem;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.88rem;
}

.text-wrapper_12 {
  width: 6.987rem;
  height: 0.88rem;
  overflow-wrap: break-word;
  font-size: 0;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.88rem;
  margin: 0.267rem 10.614rem 0 0.8rem;
}

.text_24 {
  overflow-wrap: break-word;
  color: rgba(145, 145, 153, 1);
  font-size: 0.64rem;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.88rem;
}

.text_25 {
  overflow-wrap: break-word;
  color: rgba(51, 51, 51, 1);
  font-size: 0.64rem;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.88rem;
}

.text-wrapper_13 {
  width: 4.48rem;
  height: 0.88rem;
  overflow-wrap: break-word;
  font-size: 0;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.88rem;
  margin: 0.267rem 13.12rem 0 0.8rem;
}

.text_26 {
  overflow-wrap: break-word;
  color: rgba(145, 145, 153, 1);
  font-size: 0.64rem;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.88rem;
}

.text_27 {
  overflow-wrap: break-word;
  color: rgba(51, 51, 51, 1);
  font-size: 0.64rem;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.88rem;
}

.image_2 {
  width: 7.147rem;
  height: 0.267rem;
  align-self: center;
  margin-top: 16.48rem;
}

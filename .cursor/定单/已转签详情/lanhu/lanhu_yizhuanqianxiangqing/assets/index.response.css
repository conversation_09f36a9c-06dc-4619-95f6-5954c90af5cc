.page {
  background-color: rgba(255, 255, 255, 1);
  position: relative;
  width: 100vw;
  height: 216.54vw;
  overflow: hidden;
}

.section_1 {
  background-color: rgba(241, 241, 241, 1);
  height: 216.54vw;
}

.group_1 {
  background-color: rgba(255, 255, 255, 1);
  padding: 4.13vw 3.73vw 5.33vw 4.53vw;
}

.group_2 {
  margin-left: 1.07vw;
}

.text-wrapper_1 {
  width: 14.4vw;
  height: 4.54vw;
  overflow-wrap: break-word;
  font-size: 0;
  letter-spacing: -0.5600000023841858px;
  font-family: Helvetica;
  font-weight: NaN;
  text-align: right;
  white-space: nowrap;
  line-height: 4.54vw;
}

.text_1 {
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 3.73vw;
  font-family: Helvetica;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 4.54vw;
}

.text_2 {
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 3.73vw;
  font-family: Helvetica;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 4.54vw;
}

.label_1 {
  width: 4.54vw;
  height: 2.8vw;
  margin: 0.53vw 0 1.2vw 58.26vw;
}

.label_2 {
  width: 4.14vw;
  height: 2.94vw;
  margin: 0.53vw 0 1.06vw 1.33vw;
}

.image_1 {
  width: 6.67vw;
  height: 3.2vw;
  margin: 0.4vw 0 0.93vw 1.33vw;
}

.group_3 {
  width: 50.8vw;
  margin: 4.26vw 40.93vw 0 0;
}

.label_3 {
  width: 4.94vw;
  height: 4.94vw;
  margin-bottom: 0.27vw;
}

.text_3 {
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 4.8vw;
  font-family: PingFangSC-Medium;
  font-weight: 500;
  text-align: right;
  white-space: nowrap;
  line-height: 4.8vw;
  margin-top: 0.4vw;
}

.group_4 {
  background-color: rgba(255, 255, 255, 1);
  border-radius: 20px;
  width: 92vw;
  align-self: center;
  margin-top: 2.67vw;
  padding-bottom: 4vw;
}

.section_2 {
  background: url(./img/SketchPnga7accd1592e0e0ea9873f644c038a9901b4a027e9d37549a351c44616a9e2526.png)
    100% no-repeat;
  background-size: 100% 100%;
  padding: 2.53vw 69.33vw 2.53vw 3.46vw;
}

.image-text_1 {
  width: 19.2vw;
}

.section_3 {
  background-color: rgba(53, 131, 255, 1);
  border-radius: 16px;
  width: 1.07vw;
  height: 4.27vw;
  margin: 0.66vw 0 0.66vw 0;
}

.text-group_1 {
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 4vw;
  font-family: PingFangSC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 5.6vw;
}

.text-wrapper_2 {
  width: 22.4vw;
  height: 4.4vw;
  overflow-wrap: break-word;
  font-size: 0;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 4.4vw;
  margin: 3.06vw 65.6vw 0 4vw;
}

.text_4 {
  overflow-wrap: break-word;
  color: rgba(145, 145, 153, 1);
  font-size: 3.2vw;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 4.4vw;
}

.text_5 {
  overflow-wrap: break-word;
  color: rgba(51, 51, 51, 1);
  font-size: 3.2vw;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 4.4vw;
}

.text-wrapper_3 {
  width: 64.4vw;
  height: 4.4vw;
  overflow-wrap: break-word;
  font-size: 0;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 4.4vw;
  margin: 1.33vw 23.6vw 0 4vw;
}

.text_6 {
  overflow-wrap: break-word;
  color: rgba(145, 145, 153, 1);
  font-size: 3.2vw;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 4.4vw;
}

.text_7 {
  overflow-wrap: break-word;
  color: rgba(51, 51, 51, 1);
  font-size: 3.2vw;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 4.4vw;
}

.text-wrapper_4 {
  width: 26.27vw;
  height: 4.4vw;
  overflow-wrap: break-word;
  font-size: 0;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 4.4vw;
  margin: 1.33vw 61.73vw 0 4vw;
}

.text_8 {
  overflow-wrap: break-word;
  color: rgba(145, 145, 153, 1);
  font-size: 3.2vw;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 4.4vw;
}

.text_9 {
  overflow-wrap: break-word;
  color: rgba(51, 51, 51, 1);
  font-size: 3.2vw;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 4.4vw;
}

.text-wrapper_5 {
  width: 21.2vw;
  height: 4.4vw;
  overflow-wrap: break-word;
  font-size: 0;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 4.4vw;
  margin: 1.33vw 66.8vw 0 4vw;
}

.text_10 {
  overflow-wrap: break-word;
  color: rgba(145, 145, 153, 1);
  font-size: 3.2vw;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 4.4vw;
}

.text_11 {
  overflow-wrap: break-word;
  color: rgba(51, 51, 51, 1);
  font-size: 3.2vw;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 4.4vw;
}

.text-wrapper_6 {
  width: 34.54vw;
  height: 4.4vw;
  overflow-wrap: break-word;
  font-size: 0;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 4.4vw;
  margin: 1.33vw 53.46vw 0 4vw;
}

.text_12 {
  overflow-wrap: break-word;
  color: rgba(145, 145, 153, 1);
  font-size: 3.2vw;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 4.4vw;
}

.text_13 {
  overflow-wrap: break-word;
  color: rgba(51, 51, 51, 1);
  font-size: 3.2vw;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 4.4vw;
}

.text-wrapper_7 {
  width: 44.67vw;
  height: 4.4vw;
  overflow-wrap: break-word;
  font-size: 0;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 4.4vw;
  margin: 1.33vw 43.33vw 0 4vw;
}

.text_14 {
  overflow-wrap: break-word;
  color: rgba(145, 145, 153, 1);
  font-size: 3.2vw;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 4.4vw;
}

.text_15 {
  overflow-wrap: break-word;
  color: rgba(51, 51, 51, 1);
  font-size: 3.2vw;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 4.4vw;
}

.text-wrapper_8 {
  width: 22.4vw;
  height: 4.4vw;
  overflow-wrap: break-word;
  font-size: 0;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 4.4vw;
  margin: 1.33vw 65.6vw 0 4vw;
}

.text_16 {
  overflow-wrap: break-word;
  color: rgba(145, 145, 153, 1);
  font-size: 3.2vw;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 4.4vw;
}

.text_17 {
  overflow-wrap: break-word;
  color: rgba(51, 51, 51, 1);
  font-size: 3.2vw;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 4.4vw;
}

.group_5 {
  padding: 2.66vw 4vw 2.53vw 4vw;
}

.box_1 {
  background-color: rgba(255, 255, 255, 1);
  border-radius: 20px;
  padding-bottom: 4vw;
}

.group_6 {
  background: url(./img/SketchPnga7accd1592e0e0ea9873f644c038a9901b4a027e9d37549a351c44616a9e2526.png)
    100% no-repeat;
  background-size: 100% 100%;
  padding: 2.53vw 69.33vw 2.53vw 3.46vw;
}

.image-text_2 {
  width: 19.2vw;
}

.group_7 {
  background-color: rgba(53, 131, 255, 1);
  border-radius: 16px;
  width: 1.07vw;
  height: 4.27vw;
  margin: 0.66vw 0 0.66vw 0;
}

.text-group_2 {
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 4vw;
  font-family: PingFangSC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 5.6vw;
}

.text-wrapper_9 {
  width: 28.8vw;
  height: 4.4vw;
  overflow-wrap: break-word;
  font-size: 0;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 4.4vw;
  margin: 3.06vw 59.2vw 0 4vw;
}

.text_18 {
  overflow-wrap: break-word;
  color: rgba(145, 145, 153, 1);
  font-size: 3.2vw;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 4.4vw;
}

.text_19 {
  overflow-wrap: break-word;
  color: rgba(51, 51, 51, 1);
  font-size: 3.2vw;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 4.4vw;
}

.text-wrapper_10 {
  width: 64.4vw;
  height: 4.4vw;
  overflow-wrap: break-word;
  font-size: 0;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 4.4vw;
  margin: 1.33vw 23.6vw 0 4vw;
}

.text_20 {
  overflow-wrap: break-word;
  color: rgba(145, 145, 153, 1);
  font-size: 3.2vw;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 4.4vw;
}

.text_21 {
  overflow-wrap: break-word;
  color: rgba(51, 51, 51, 1);
  font-size: 3.2vw;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 4.4vw;
}

.text-wrapper_11 {
  width: 55.34vw;
  height: 4.4vw;
  overflow-wrap: break-word;
  font-size: 0;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 4.4vw;
  margin: 1.33vw 32.66vw 0 4vw;
}

.text_22 {
  overflow-wrap: break-word;
  color: rgba(145, 145, 153, 1);
  font-size: 3.2vw;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 4.4vw;
}

.text_23 {
  overflow-wrap: break-word;
  color: rgba(51, 51, 51, 1);
  font-size: 3.2vw;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 4.4vw;
}

.text-wrapper_12 {
  width: 34.94vw;
  height: 4.4vw;
  overflow-wrap: break-word;
  font-size: 0;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 4.4vw;
  margin: 1.33vw 53.06vw 0 4vw;
}

.text_24 {
  overflow-wrap: break-word;
  color: rgba(145, 145, 153, 1);
  font-size: 3.2vw;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 4.4vw;
}

.text_25 {
  overflow-wrap: break-word;
  color: rgba(51, 51, 51, 1);
  font-size: 3.2vw;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 4.4vw;
}

.text-wrapper_13 {
  width: 22.4vw;
  height: 4.4vw;
  overflow-wrap: break-word;
  font-size: 0;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 4.4vw;
  margin: 1.33vw 65.6vw 0 4vw;
}

.text_26 {
  overflow-wrap: break-word;
  color: rgba(145, 145, 153, 1);
  font-size: 3.2vw;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 4.4vw;
}

.text_27 {
  overflow-wrap: break-word;
  color: rgba(51, 51, 51, 1);
  font-size: 3.2vw;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 4.4vw;
}

.image_2 {
  width: 35.74vw;
  height: 1.34vw;
  align-self: center;
  margin-top: 82.4vw;
}

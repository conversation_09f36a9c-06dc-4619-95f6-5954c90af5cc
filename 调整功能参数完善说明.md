# 调整功能参数完善说明

## 修改背景

用户反馈调整功能需要传递额外的参数：
- `contractUnionId`: 从contract的unionId字段获取
- `projectId`: 项目ID字段

这些参数需要在调整时传递给EntryProcess页面，并在保存时包含在提交数据中。

## 主要修改内容

### 1. EntryManagement.vue - 调整跳转逻辑

**文件位置**: `src/views/EntryManagement.vue`

**修改方法**: `handleAdjust`

```javascript
// 调整进场信息
const handleAdjust = async (item: EnterVo) => {
    try {
        // 获取进场单详情
        const res = await getEnterDetail(item.id)
        
        if (res.code === 200 && res.data) {
            showToast('加载详情成功')
            
            // 从返回数据中获取contract信息
            const contract = res.data.contract || {}
            const enter = res.data.enter || res.data
            
            // 跳转到进场办理页面，传递详情数据和编辑模式
            router.push({
                name: 'EntryProcess',
                query: {
                    entryId: item.id,
                    contractId: item.contractId,
                    contractUnionId: contract.unionId || enter.contractUnionId || '', // 从contract获取unionId
                    projectId: enter.projectId || item.projectId || '', // 从enter或item获取projectId
                    contractNo: item.contractNo,
                    tenantName: item.tenantName,
                    mode: 'edit' // 标识为编辑模式
                },
                state: {
                    entryData: res.data // 通过路由状态传递详情数据
                }
            })
        }
    } catch (error) {
        console.error('获取进场详情失败:', error)
        showToast('获取进场详情失败')
    }
}
```

**修改要点**:
- 从`getEnterDetail`接口返回的数据中解析contract和enter信息
- 优先从`contract.unionId`获取`contractUnionId`，备选方案是`enter.contractUnionId`
- 优先从`enter.projectId`获取`projectId`，备选方案是`item.projectId`
- 将这些参数通过路由query传递给EntryProcess页面

### 2. EntryProcess.vue - 参数接收与使用

**文件位置**: `src/views/EntryProcess.vue`

#### 2.1 新增参数变量

```javascript
// 页面参数
const entryId = ref('')
const contractId = ref('')
const contractUnionId = ref('')  // 新增
const projectId = ref('')        // 新增
const pageMode = ref('create') // 页面模式: create-新建, edit-编辑
```

#### 2.2 参数初始化

```javascript
onMounted(() => {
    // 获取基本参数
    if (route.query.contractId) {
        contractId.value = route.query.contractId as string
    }
    if (route.query.entryId) {
        entryId.value = route.query.entryId as string
    }
    if (route.query.contractUnionId) {        // 新增
        contractUnionId.value = route.query.contractUnionId as string
    }
    if (route.query.projectId) {             // 新增
        projectId.value = route.query.projectId as string
    }
    if (route.query.mode) {
        pageMode.value = route.query.mode as string
    }
    // ... 其他初始化逻辑
})
```

#### 2.3 保存数据构造

```javascript
// 构造保存数据
const saveData: EnterAddDTO = {
    id: entryId.value || undefined,
    projectId: projectId.value || undefined,        // 新增
    contractId: contractId.value,
    contractUnionId: contractUnionId.value || undefined,  // 新增
    isNotify: sendNotice.value,
    roomList: roomList.value.map(room => ({
        // ... 房间数据
    }))
}
```

#### 2.4 调试日志

```javascript
console.log('保存数据:', saveData)
console.log('页面参数:', {
    entryId: entryId.value,
    projectId: projectId.value,
    contractId: contractId.value,
    contractUnionId: contractUnionId.value,
    pageMode: pageMode.value
})
```

## 数据流转图

```
EntryManagement (调整)
    ↓ getEnterDetail(item.id)
API返回数据结构:
{
    contract: { unionId: "xxx" },
    enter: { projectId: "xxx", contractUnionId: "xxx" }
}
    ↓ 解析参数
router.push({
    query: {
        contractUnionId: contract.unionId,
        projectId: enter.projectId,
        // ... 其他参数
    }
})
    ↓ 路由跳转
EntryProcess (编辑模式)
    ↓ onMounted获取参数
{
    contractUnionId: route.query.contractUnionId,
    projectId: route.query.projectId
}
    ↓ 保存时构造数据
saveData: {
    projectId: projectId.value,
    contractUnionId: contractUnionId.value,
    // ... 其他数据
}
    ↓ 调用保存API
saveEntry(saveData)
```

## API数据结构

### EnterAddDTO 接口定义
```typescript
export interface EnterAddDTO {
    id?: string;
    projectId?: string;           // 项目ID
    contractId?: string;
    contractUnionId?: string;     // 合同联合ID
    enteredRoomCount?: number;
    isNotify?: boolean;
    isDel?: boolean;
    roomList?: EnterRoomAddDTO[];
}
```

## 测试场景

### 1. 数据获取测试
- 确认`getEnterDetail`接口返回的数据结构包含`contract.unionId`和`enter.projectId`
- 验证备选方案`enter.contractUnionId`和`item.projectId`的可用性

### 2. 参数传递测试
- 在调整跳转时检查浏览器地址栏是否包含正确的query参数
- 在EntryProcess页面的onMounted中打印参数值确认接收正确

### 3. 保存功能测试
- 在保存时检查console日志中的保存数据是否包含正确的`projectId`和`contractUnionId`
- 验证后端接口是否能正确接收和处理这些参数

### 4. 兼容性测试
- 确认新建模式的进场办理功能不受影响
- 验证没有这些参数时的降级处理（使用空字符串或undefined）

## 兼容性说明

- 所有新增参数都是可选的，不会影响现有的新建模式
- 使用了`|| undefined`的备选方案确保在数据不存在时有合理的默认值
- 保持了原有的数据流转逻辑，只是增加了额外的参数传递

## 完成状态

✅ EntryManagement调整跳转逻辑修改  
✅ EntryProcess参数接收逻辑添加  
✅ 保存数据结构完善  
✅ 调试日志添加  
✅ 代码构建测试通过  

所有修改已完成并通过构建测试，可进行功能测试验证参数传递的正确性。 
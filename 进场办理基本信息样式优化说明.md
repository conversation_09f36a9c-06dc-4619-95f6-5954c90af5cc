# 进场办理基本信息样式优化说明

## 优化概述
根据UI设计要求，对EntryProcess.vue和EntryDetail.vue页面的基本信息展示区域进行了样式优化，采用蓝色渐变背景配白色文字的设计风格。

## 设计要求
- 蓝色渐变背景（#3583FF -> #1677FF）
- 白色文字显示
- 圆角卡片设计
- 预留背景图片位置
- 移动端友好的响应式布局

## 修改内容

### 1. EntryProcess.vue 修改
- **基本信息内容调整**：精简显示合同编号、承租方、租期三个核心信息
- **样式重构**：移除原有的白色卡片样式，采用蓝色渐变背景
- **布局优化**：调整文字间距和字体权重，提升可读性

### 2. EntryDetail.vue 修改
- **保持一致性**：与EntryProcess.vue采用相同的样式设计
- **内容统一**：同样显示合同编号、承租方、租期三个信息
- **移除冗余信息**：去掉创建时间、创建人、状态等详细信息，保持简洁

## 技术实现

### 样式特点
```css
.basic-info-card {
    /* 蓝色渐变背景 */
    background: linear-gradient(135deg, #3583FF 0%, #1677FF 100%);
    
    /* 预留背景图片位置 */
    /* background-image: url(''); */
    /* background-size: cover; */
    /* background-position: center; */
    /* background-repeat: no-repeat; */
    
    border-radius: 12px;
    padding: 30px 20px;
    color: #FFFFFF;
    position: relative;
    overflow: hidden;
    box-shadow: 0 4px 16px rgba(53, 131, 255, 0.3);
}
```

### 文字样式
- **标签文字**：半透明白色 `rgba(255, 255, 255, 0.9)`，字重400
- **值文字**：纯白色 `#FFFFFF`，字重500
- **字体大小**：统一28px，适配移动端

### 背景图片支持
- 预留了背景图片CSS属性
- 支持覆盖整个卡片的背景图
- 文字内容设置了z-index确保显示在背景图之上

## 视觉效果

### 颜色方案
- 主背景：蓝色渐变（#3583FF -> #1677FF）
- 文字颜色：白色系（#FFFFFF, rgba(255,255,255,0.9)）
- 阴影效果：带蓝色透明度的投影

### 布局特点
- 圆角设计，现代感强
- 适当的内边距保证文字不会太靠近边缘
- 行间距合理，信息层次清晰

## 响应式适配
- 移动端优化的字体大小（28px）
- 合理的内边距（30px 20px）
- 最小宽度限制确保标签文字不会过度压缩

## 扩展性
- 预留背景图片位置，后续可轻松添加装饰性图片
- 渐变色可根据需要调整
- 文字内容可灵活配置

## 兼容性
- 支持现代浏览器的CSS渐变
- 移动端Safari/Chrome完美支持
- 降级处理：不支持渐变的浏览器会显示纯色背景

## 总结
此次样式优化提升了进场办理页面的视觉效果，采用现代化的卡片设计和舒适的配色方案，同时保持了良好的可读性和用户体验。蓝色主题与整体应用风格保持一致，为后续添加背景装饰预留了空间。 
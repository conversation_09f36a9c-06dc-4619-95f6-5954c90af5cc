'use strict'
// h5
// ************
// /app/vanyang/h5
//账号密码都是root/YZ-it418

import SFTPClient from 'ssh2-sftp-client';
const sftp = new SFTPClient()

const config = {
    dev: {
        host: '************',
        port: '22',
        username: 'root',
        password: 'YZ-it418',
        remotePath: '/app/vanyang/app-h5'
    },
    uat: {
        host: '**************',
        port: '22',
        username: 'root',
        password: 'zgqdWY@113',
        remotePath: '/root/h5/app'
    },
    prod: {
        host: '**************',
        port: '22',
        username: 'root',
        password: 'zgqdWY@113',
        remotePath: '/mnt/vdb/apph5'
    },
    prod2: {
        host: '**************',
        port: '22',
        username: 'root',
        password: 'zgqdWY@114',
        remotePath: '/mnt/vdb/apph5'
    },
}

const localFile = './dist'  // 本地路径

// 获取命令行参数中的环境变量，默认为 prod
const env = process.argv[2] || 'prod'
const currentConfig = config[env]

async function upload() {
    if (!currentConfig) {
        console.error('无效的环境配置！请使用 prod 或 uat')
        return
    }

    console.log(`正在上传到${env}环境服务器...`)
    try {
        await sftp.connect({
            host: currentConfig.host,
            port: currentConfig.port,
            username: currentConfig.username,
            password: currentConfig.password
        });
        await sftp.uploadDir(localFile, currentConfig.remotePath); // 上传整个目录
        console.log('上传成功！')
    } catch (err) {
        console.error('上传失败:', err)
    } finally {
        sftp.end()
    }
}
upload().then(r => {})
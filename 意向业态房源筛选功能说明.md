# 意向业态房源筛选功能说明

## 功能概述

已完成意向房源根据意向业态类型进行筛选的功能，通过 `buildingType` 参数实现房源的精确筛选。

## 接口参数更新

### API 参数结构
```typescript
export interface RoomOptionsParams {
    projectId: string         // 项目ID（必需）
    buildingType?: string     // 业态类型（对应意向业态，用于筛选房源）
    businessType?: string     // 业态类型（兼容性保留）
    keyword?: string          // 搜索关键词
    status?: string | number  // 房源状态
    pageNum?: number          // 页码
    pageSize?: number         // 每页数量
}
```

### 核心参数说明
- **`buildingType`**: 主要参数，对应前端选择的意向业态编码，用于筛选相应类型的房源
- **`businessType`**: 兼容性参数，与 `buildingType` 值相同，确保接口兼容

## 功能实现细节

### 1. 参数传递机制
```typescript
const getRoomList = async (projectId: string, buildingType?: string) => {
    const params: RoomOptionsParams = {
        projectId: projectId,
        pageSize: 1000
    }
    
    // 如果有业态类型，添加到查询参数
    if (buildingType) {
        params.buildingType = buildingType    // 主要参数
        params.businessType = buildingType    // 兼容性保留
    }
    
    const res = await getRoomOptions(params)
    // ...
}
```

### 2. 联动触发时机

#### 项目选择变化时
```typescript
const onProjectConfirm = (value: any) => {
    // ...
    if (oldProjectId !== newProjectId) {
        formData.roomId = ''
        formData.room = ''
        
        // 根据当前选择的业态类型获取房源
        const currentBuildingType = formData.propertyTypeCode
        getRoomList(newProjectId, currentBuildingType)
    }
}
```

#### 业态类型选择变化时
```typescript
const onBusinessTypeConfirm = (value: any) => {
    // ...
    // 业态类型变化时，清空房源选择并重新获取房源列表
    if (oldBusinessType !== newBusinessType && formData.projectId) {
        formData.roomId = ''
        formData.room = ''
        getRoomList(formData.projectId, newBusinessType)
    }
}
```

#### 手动点击房源字段时
```typescript
const handleRoomFieldClick = () => {
    if (!formData.projectId) {
        showToast('请先选择项目')
        return
    }
    
    // 如果还没有加载房源数据，先加载
    if (roomColumns.value.length === 0 && !roomLoading.value) {
        getRoomList(formData.projectId, formData.propertyTypeCode)
    }
    
    showRoomPicker.value = true
}
```

## 业态类型映射

根据之前的业态选择器配置，业态编码与房源类型的对应关系：

| 业态名称 | 业态编码 | buildingType | 说明 |
|---------|---------|--------------|------|
| 宿舍 | 10 | 10 | 宿舍类房源 |
| 厂房 | 20 | 20 | 厂房类房源 |
| 商业-商铺 | 31 | 31 | 商铺类房源 |
| 商业-综合体 | 32 | 32 | 综合体类房源 |
| 商业-中央食堂 | 33 | 33 | 食堂类房源 |
| 车位 | 40 | 40 | 车位类房源 |
| 办公 | 50 | 50 | 办公类房源 |

## 实际使用场景

### 场景1：选择项目后自动筛选
1. 用户选择项目："北滘-众创"
2. 如果已选择业态类型（如"厂房-20"），自动传递 `buildingType: "20"`
3. 接口返回该项目下所有厂房类型的房源

### 场景2：改变业态类型后重新筛选
1. 用户从"厂房"切换到"办公"
2. 传递 `buildingType: "50"`
3. 房源列表更新为办公类型的房源

### 场景3：无业态类型的情况
1. 用户仅选择项目，未选择业态类型
2. 不传递 `buildingType` 参数
3. 返回该项目下所有类型的房源

## 接口调用示例

### 示例1：带业态筛选
```typescript
const params = {
    projectId: "306627ad-3f55-46cb-b51e-4970c50559da",
    buildingType: "20",  // 筛选厂房类型
    businessType: "20",  // 兼容性参数
    pageSize: 1000
}

POST /business-rent-admin/room/roomOptions
Content-Type: application/json

{
    "projectId": "306627ad-3f55-46cb-b51e-4970c50559da",
    "buildingType": "20",
    "businessType": "20",
    "pageSize": 1000
}
```

### 示例2：无业态筛选
```typescript
const params = {
    projectId: "306627ad-3f55-46cb-b51e-4970c50559da",
    pageSize: 1000
}

POST /business-rent-admin/room/roomOptions
Content-Type: application/json

{
    "projectId": "306627ad-3f55-46cb-b51e-4970c50559da",
    "pageSize": 1000
}
```

## 预期返回结果

基于业态类型筛选后，返回的房源树形结构中，只包含匹配指定 `propertyType` 的房源节点。

### 筛选前（无 buildingType）
```json
{
    "code": 200,
    "data": [
        {
            "level": 4,
            "roomId": "21564",
            "propertyType": "20",  // 厂房
            "roomName": "1001"
        },
        {
            "level": 4,
            "roomId": "21565",
            "propertyType": "50",  // 办公
            "roomName": "1002"
        }
    ]
}
```

### 筛选后（buildingType: "20"）
```json
{
    "code": 200,
    "data": [
        {
            "level": 4,
            "roomId": "21564",
            "propertyType": "20",  // 只返回厂房类型
            "roomName": "1001"
        }
    ]
}
```

## 注意事项

1. **参数名称**: 后端接口使用 `buildingType` 作为业态筛选参数
2. **兼容性**: 同时传递 `businessType` 确保接口兼容
3. **筛选逻辑**: 筛选基于房源的 `propertyType` 字段
4. **空值处理**: 未选择业态类型时不传递筛选参数，返回所有房源
5. **联动清空**: 业态类型变化时会清空已选房源，需要重新选择

## 测试验证

### 验证步骤
1. 选择项目但不选择业态 → 显示所有房源
2. 选择"厂房"业态 → 只显示 propertyType="20" 的房源
3. 切换到"办公"业态 → 只显示 propertyType="50" 的房源
4. 检查网络请求中 buildingType 参数是否正确传递

### 预期效果
- 房源数量根据业态类型明显变化
- 显示的房源类型与选择的业态类型一致
- 切换业态类型时房源列表实时更新

此功能确保了用户选择的意向业态与可选房源的完美匹配，提供了精确的房源筛选体验。 
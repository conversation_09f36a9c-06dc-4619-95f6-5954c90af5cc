// 应用配置
export const APP_CONFIG = {
  // 应用标题
  title: '万洋资管平台',
  
  // API 基础配置
  api: {
    // 开发环境使用代理，生产环境使用环境变量或默认值
    // baseURL: import.meta.env.DEV ? '' : (import.meta.env.VITE_API_BASE_URL || 'https://api.wanyang.com'),
    baseURL: '/prod-api',
    timeout: 12000,
  },
  
  // Token 配置
  token: {
    key: 'access_token',
    expireKey: 'token_expire',
  },
  
  // 分页配置
  pagination: {
    defaultPageSize: 20,
    pageSizes: [10, 20, 50, 100],
  },
  
  // 上传配置
  upload: {
    maxSize: 10 * 1024 * 1024, // 10MB
    allowedTypes: ['image/jpeg', 'image/png', 'image/gif', 'application/pdf'],
  },
};

// 业务状态码
export const BUSINESS_CODE = {
  SUCCESS: 200,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  SERVER_ERROR: 500,
  TOKEN_EXPIRED: 50014,
  TOKEN_INVALID: 50008,
  OTHER_CLIENT_LOGIN: 50012,
};

// 环境判断
export const ENV = {
  isDev: import.meta.env.DEV,
  isProd: import.meta.env.PROD,
  mode: import.meta.env.MODE,
}; 
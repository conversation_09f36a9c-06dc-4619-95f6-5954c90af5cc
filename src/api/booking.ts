import request from './index'

// 定单列表查询接口
export function getBookingList(data: any) {
  return request.post('/business-rent-rest/booking/list', data)
}

// 定单详情查询接口
export function getBookingDetail(id: string) {
  return request.get('/business-rent-rest/booking/detail', { id })
}

// 保存定单接口
export function saveBooking(data: any) {
  return request.post('/business-rent-rest/booking/save', data)
}

// 退定接口
export function cancelBooking(data: any) {
  return request.post('/business-rent-rest/booking/cancel', data)
}

// 定单支付接口
export function payBooking(data: any) {
  return request.post('/business-rent-rest/booking/pay', data)
}

// 定单套打接口
export function printBooking(data: any) {
  return request.post('/business-rent-rest/booking/print', data)
}

// 定单基础信息查询接口
export function getBookingBasicInfo(id: string) {
  return request.get('/business-rent-rest/booking/bookingDetail', { id })
}

// 获取租赁定价协议文档
export function getPricingAgreementDoc(params: { id: string, type?: number }) {
    return request.post('/business-rent-rest/booking/print', params)
}

// 删除定单接口
export function deleteBooking(data: { id: string }) {
  return request.delete(`/business-rent-rest/booking/delete/${data.id}`)
}

// 作废定单接口
export function invalidBooking(data: { id: string, cancelRemark: string }) {
  return request.post(`/business-rent-rest/booking/invalid`, data)
}
export function getBookingSummary(data: { id: string }) {
  return request.post(`/business-rent-rest/booking/summary`, data)
}

// 定单详情类型定义
export interface BookingDetailVo {
  booking: {
    id: string
    customerName: string
    roomName: string
    receivableDate: string
    unpaidAmount: number
    amount: number
    [key: string]: any
  }
  [key: string]: any
}

// 支付参数类型定义
export interface BookingPaymentDto {
  bookingId: string
  amount: number
  [key: string]: any
}

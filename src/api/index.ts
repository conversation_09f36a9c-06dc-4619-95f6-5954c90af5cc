import axios, {
	type AxiosInstance,
	AxiosError,
	type AxiosRequestConfig,
	type AxiosResponse,
	type InternalAxiosRequestConfig,
} from 'axios';
import { getToken, removeToken } from '../utils/auth';
import { showToast, showConfirmDialog } from 'vant';
import router from '../router';
import { APP_CONFIG, BUSINESS_CODE } from '../config';

// 响应数据接口定义
interface ResponseData<T = any> {
	code: number;
	data: T;
	msg: string;
	[key: string]: any;
}

const baseConfig = {
	// 使用配置文件中的设置
	baseURL: APP_CONFIG.api.baseURL,
	timeout: APP_CONFIG.api.timeout,
	withCredentials: true,
};

class RequestHttp {
	service: AxiosInstance;

	public constructor(initConfig: AxiosRequestConfig) {
		this.service = axios.create(initConfig);

		/**
		 * @description 请求拦截器
		 */
		this.service.interceptors.request.use(
			(config: InternalAxiosRequestConfig) => {
				// 添加 token 到请求头
				if (config.headers && getToken()) {
					config.headers.Authorization = `Bearer ${getToken()}`;
				}
				return config;
			},
			(error: AxiosError) => {
				return Promise.reject(error);
			}
		);

		/**
		 * @description 响应拦截器
		 */
		this.service.interceptors.response.use(
			async (response: AxiosResponse<ResponseData>) => {
				const { data } = response;
				console.log(data)

				// 判断业务状态码
				if (data.code && data.code !== BUSINESS_CODE.SUCCESS) {
					showToast({
						message: data.msg || '请求失败',
						type: 'fail',
						duration: 3000,
					});

					// Token 失效相关错误码处理
					const authErrorCodes = [
						BUSINESS_CODE.TOKEN_EXPIRED,
						BUSINESS_CODE.TOKEN_INVALID,
						BUSINESS_CODE.OTHER_CLIENT_LOGIN,
						BUSINESS_CODE.UNAUTHORIZED
					];

					if (authErrorCodes.includes(data.code) || data.msg.includes('loginUser')) {
						try {
							await showConfirmDialog({
								title: '登录已过期',
								message: '您的登录状态已过期，请重新登录',
								confirmButtonText: '重新登录',
								cancelButtonText: '取消',
							});

							// 清除 token 并跳转到登录页
							removeToken();
							router.push('/');
						} catch (error) {
							// 用户取消时也清除 token
							removeToken();
						}
					}
					return Promise.reject(new Error(data.msg || '请求失败'));
				}
				// 返回完整的响应对象，保持类型一致性
				return response;
			},
			async (error: AxiosError) => {
				const errorMessage = (error.response?.data as any)?.msg || '网络请求错误';

				showToast({
					message: errorMessage,
					type: 'fail',
					duration: 3000,
				});

				// 401 未授权处理
				if (error.response?.status === BUSINESS_CODE.UNAUTHORIZED) {
					removeToken();
					router.push('/');
				}

				return Promise.reject(error);
			}
		);
	}

	/**
	 * @description GET 请求
	 */
	get<T = any>(
		url: string,
		params?: object,
		config = {}
	): Promise<ResponseData<T>> {
		return this.service.get(url, { params, ...config }).then(res => res.data);
	}

	/**
	 * @description POST 请求
	 */
	post<T = any>(
		url: string,
		data?: object | string,
		config = {}
	): Promise<ResponseData<T>> {
		return this.service.post(url, data, config).then(res => res.data);
	}

	/**
	 * @description PUT 请求
	 */
	put<T = any>(
		url: string,
		data?: object,
		config = {}
	): Promise<ResponseData<T>> {
		return this.service.put(url, data, config).then(res => res.data);
	}

	/**
	 * @description DELETE 请求
	 */
	delete<T = any>(
		url: string,
		params?: any,
		config = {}
	): Promise<ResponseData<T>> {
		return this.service.delete(url, { params, ...config }).then(res => res.data);
	}

	/**
	 * @description 文件下载
	 */
	download(url: string, data?: object, config = {}): Promise<BlobPart> {
		return this.service.post(url, data, {
			...config,
			responseType: 'blob',
		}).then(res => res.data);
	}

	/**
	 * @description 文件上传
	 */
	upload<T = any>(
		url: string,
		formData: FormData,
		config = {}
	): Promise<ResponseData<T>> {
		return this.service.post(url, formData, {
			headers: {
				'Content-Type': 'multipart/form-data',
			},
			...config,
		}).then(res => res.data);
	}
}

export default new RequestHttp(baseConfig); 
import http from './index';

// 登录接口参数
export interface LoginData {
  username: string;
  password: string;
}

// 登录响应数据
export interface LoginRes {
  access_token: string;
  user_info: {
    id: number;
    username: string;
    nickname: string;
    avatar?: string;
    roles: string[];
  };
}

// 用户信息
export interface UserInfo {
  id: number;
  username: string;
  nickname: string;
  avatar?: string;
  phone?: string;
  email?: string;
  roles: string[];
}

/**
 * 用户登录
 */
export function login(data: LoginData) {
  return http.post<LoginRes>('/auth/login', data);
}

/**
 * 用户登出
 */
export function logout() {
  return http.post('/auth/logout');
}

/**
 * 获取用户信息
 */
export function getUserInfo() {
  return http.get<UserInfo>('/user/info');
}

/**
 * 修改用户信息
 */
export function updateUserInfo(data: Partial<UserInfo>) {
  return http.put('/user/info', data);
}

/**
 * 修改密码
 */
export function changePassword(data: {
  oldPassword: string;
  newPassword: string;
}) {
  return http.put('/user/password', data);
} 
import http from './index';

// 催缴支付参数
export interface ContractBillPaymentDto {
  billId: string;
  amount: number;
  paymentList: ContractBillPaymentItemDto[];
}

// 催缴支付明细
export interface ContractBillPaymentItemDto {
  costId: string;
  payAmount: number;
}

// 催缴支付响应
export interface CollectionPaymentResponse {
  code: number;
  msg: string;
  paymentUrl?: string;
}

// 催缴详情响应
export interface ContractBillDetailVo {
  id: string;
  projectId: string;
  projectName: string;
  customerId: string;
  customerName: string;
  contractId: string;
  contractNo: string;
  roomCount: number;
  roomName: string;
  type: number; // 催缴类型(1催缴函 2催缴通知单)
  startDate: string;
  endDate: string;
  receivableDate: string;
  totalMoney: number;
  lastReceivedMoney: number;
  collectFlag: number; // 缴费状态(0未缴 1部分缴 2已缴 3缴费中)
  collectTime: string;
  sendTime: string;
  receivePerson: string;
  receivePhone: string;
  viewStatus: number; // 查看状态(0未查看 1已查看)
  viewTime: string;
  previewUrl: string;
  createBy: string;
  createByName: string;
  createTime: string;
  updateBy: string;
  updateByName: string;
  updateTime: string;
  moneyList: ContractBillMoneyVo[];
}

// 催缴金额明细
export interface ContractBillMoneyVo {
  id: string;
  billId: string;
  costId: string;
  costType: number;
  subjectName: string;
  period: number;
  startDate: string;
  endDate: string;
  receivableDate: string;
  totalAmount: number;
  discountAmount: number;
  reductionAmount: number;
  actualReceivable: number;
  receivedAmount: number;
  carryoverAmount: number;
  unreceivedAmount: number;
  createBy: string;
  createByName: string;
  createTime: string;
  updateBy: string;
  updateByName: string;
  updateTime: string;
}

/**
 * 获取催缴详细信息
 * 根据催缴ID获取催缴详细信息，包含催缴金额列表
 */
export function getCollectionDetail(id: string) {
  return http.get<ContractBillDetailVo>('/business-rent-rest/contract/bill/detail', { id });
}

/**
 * 催缴支付接口
 * 根据催缴ID和支付明细生成支付链接
 */
export function payCollectionBill(data: ContractBillPaymentDto) {
  return http.post<CollectionPaymentResponse>('/business-rent-rest/contract/bill/pay', data);
}

// 催缴类型枚举
export const COLLECTION_TYPE = {
  NOTICE: 1,      // 催缴函
  NOTIFICATION: 2 // 催缴通知单
} as const;

// 催缴类型文本映射
export const COLLECTION_TYPE_TEXT = {
  [COLLECTION_TYPE.NOTICE]: '催缴函',
  [COLLECTION_TYPE.NOTIFICATION]: '催缴通知单'
} as const;

// 缴费状态枚举
export const COLLECT_FLAG = {
  UNPAID: 0,    // 未缴
  PARTIAL: 1,   // 部分缴
  PAID: 2,      // 已缴
  PAYING: 3     // 缴费中
} as const;

// 缴费状态文本映射
export const COLLECT_FLAG_TEXT = {
  [COLLECT_FLAG.UNPAID]: '未缴',
  [COLLECT_FLAG.PARTIAL]: '部分缴',
  [COLLECT_FLAG.PAID]: '已缴',
  [COLLECT_FLAG.PAYING]: '缴费中'
} as const;

// 查看状态枚举
export const VIEW_STATUS = {
  UNVIEWED: 0,  // 未查看
  VIEWED: 1     // 已查看
} as const;

// 查看状态文本映射
export const VIEW_STATUS_TEXT = {
  [VIEW_STATUS.UNVIEWED]: '未查看',
  [VIEW_STATUS.VIEWED]: '已查看'
} as const;



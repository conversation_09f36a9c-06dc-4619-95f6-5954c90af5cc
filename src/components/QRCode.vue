<template>
  <div class="qrcode-container">
    <div 
      ref="qrcodeRef" 
      class="qrcode-wrapper"
      :style="{ width: size + 'px', height: 'auto' }"
    >
      <canvas 
        ref="canvasRef"
        :width="size"
        :height="size"
        v-show="qrCodeData"
      ></canvas>
      <!-- Logo -->
      <div class="qrcode-logo">
        <!-- <img src="@/assets/images/logo-a.png" :style="{ width: '38px', height: '38px' }" /> -->
      </div>
      <div 
        v-if="!qrCodeData && showPlaceholder"
        class="qrcode-placeholder"
        :style="{ width: size + 'px', height: size + 'px' }"
      >
        <!-- <div class="placeholder-content">
          <icon-qrcode :size="32" />
          <span>{{ placeholderText }}</span>
        </div> -->
      </div>
      <!-- 底部文字 -->
      <!-- <div v-if="qrCodeData && (userName || userPhone)" class="qrcode-info">
        <div v-if="userName" class="info-item">姓名：{{ userName }}</div>
        <div v-if="userPhone" class="info-item">电话：{{ userPhone }}</div>
      </div> -->
    </div>
    
    <!-- 下载按钮 -->
    <!-- <div v-if="qrCodeData" class="qrcode-actions">
      <a-space>
        <a-button size="small" @click="downloadQRCode" v-if="showDownload">
          <template #icon>
            <icon-download />
          </template>
          下载二维码
        </a-button>
        <a-button size="small" @click="copyLink">
          <template #icon>
            <icon-copy />
          </template>
          复制链接
        </a-button>
      </a-space>
    </div> -->
  </div>
</template>

<script setup lang="ts">
import { ref, watch, onMounted, nextTick } from 'vue'
// import { IconQrcode, IconDownload, IconCopy } from '@arco-design/web-vue/es/icon'
// import { Message } from '@arco-design/web-vue'
import QRCode from 'qrcode-generator'

interface Props {
  // 二维码内容
  value?: string
  // 二维码大小
  size?: number
  // 错误纠正级别 L M Q H
  errorCorrectionLevel?: 'L' | 'M' | 'Q' | 'H'
  // 是否显示占位符
  showPlaceholder?: boolean
  // 占位符文本
  placeholderText?: string
  // 是否显示下载按钮
  showDownload?: boolean
  // 前景色
  foreground?: string
  // 背景色
  background?: string
  // 边距
  margin?: number
  // Logo URL
  logo?: string
  // Logo 大小
  logoSize?: number
  // 用户姓名
  userName?: string
  // 用户电话
  userPhone?: string
}

const props = withDefaults(defineProps<Props>(), {
  value: '',
  size: 200,
  errorCorrectionLevel: 'H', // 使用最高纠错级别，因为要放 logo
  showPlaceholder: true,
  placeholderText: '暂无二维码',
  showDownload: false,
  foreground: '#000000',
  background: '#ffffff',
  margin: 4,
  logo: '',
  logoSize: 40,
  userName: '',
  userPhone: ''
})

const emit = defineEmits<{
  generated: [dataUrl: string]
  error: [error: Error]
}>()

const qrcodeRef = ref<HTMLDivElement>()
const canvasRef = ref<HTMLCanvasElement>()
const qrCodeData = ref('')

// 生成二维码
const generateQRCode = async () => {
  if (!props.value || !canvasRef.value) {
    qrCodeData.value = ''
    return
  }

  try {
    // 创建二维码实例
    const qr = QRCode(0, props.errorCorrectionLevel)
    qr.addData(props.value)
    qr.make()

    // 获取二维码模块数量
    const moduleCount = qr.getModuleCount()
    const canvas = canvasRef.value
    const ctx = canvas.getContext('2d')
    
    if (!ctx) {
      throw new Error('无法获取canvas上下文')
    }

    // 清空画布
    ctx.clearRect(0, 0, props.size, props.size)
    
    // 计算每个模块的大小
    const cellSize = Math.floor((props.size - props.margin * 2) / moduleCount)
    const offset = Math.floor((props.size - cellSize * moduleCount) / 2)

    // 绘制背景
    ctx.fillStyle = props.background
    ctx.fillRect(0, 0, props.size, props.size)

    // 绘制二维码
    ctx.fillStyle = props.foreground
    for (let row = 0; row < moduleCount; row++) {
      for (let col = 0; col < moduleCount; col++) {
        if (qr.isDark(row, col)) {
          ctx.fillRect(
            offset + col * cellSize,
            offset + row * cellSize,
            cellSize,
            cellSize
          )
        }
      }
    }

    // 获取数据URL
    const dataUrl = canvas.toDataURL('image/png')
    qrCodeData.value = dataUrl
    emit('generated', dataUrl)
  } catch (error) {
    console.error('生成二维码失败:', error)
    qrCodeData.value = ''
    emit('error', error as Error)
  }
}

// 下载二维码
const downloadQRCode = () => {
  if (!qrCodeData.value) return

  const link = document.createElement('a')
  link.download = `qrcode-${Date.now()}.png`
  link.href = qrCodeData.value
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
}

// 复制链接
const copyLink = () => {
  if (!props.value) return
  
  try {
    navigator.clipboard.writeText(props.value).then(() => {
      // Message.success('链接已复制')
    })
  } catch (err) {
    // 降级处理：如果 clipboard API 不可用
    const textarea = document.createElement('textarea')
    textarea.value = props.value
    document.body.appendChild(textarea)
    textarea.select()
    document.execCommand('copy')
    document.body.removeChild(textarea)
    // Message.success('链接已复制')
  }
}

// 监听value变化
watch(() => props.value, () => {
  nextTick(() => {
    generateQRCode()
  })
}, { immediate: true })

// 监听其他属性变化
watch([
  () => props.size,
  () => props.errorCorrectionLevel,
  () => props.foreground,
  () => props.background,
  () => props.margin
], () => {
  nextTick(() => {
    generateQRCode()
  })
})

onMounted(() => {
  generateQRCode()
})

// 暴露方法
defineExpose({
  generateQRCode,
  downloadQRCode,
  getDataUrl: () => qrCodeData.value
})
</script>

<style scoped lang="less">
.qrcode-container {
  display: inline-block;
  text-align: center;

  .qrcode-wrapper {
    position: relative;
    display: inline-block;
    border: 1px solid var(--color-border-2);
    border-radius: 6px;
    overflow: hidden;
    padding-bottom: 12px;

    canvas {
      display: block;
    }

    .qrcode-logo {
      position: absolute;
      left: 50%;
      top: 50%;
      transform: translate(-50%, -50%);
      background: #fff;
      border-radius: 4px;
      padding: 2px;
      box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);

      img {
        display: block;
        border-radius: 4px;
      }
    }

    .qrcode-placeholder {
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: var(--color-fill-1);
      border: 1px dashed var(--color-border-3);
      border-radius: 6px;

      .placeholder-content {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 8px;
        color: var(--color-text-3);
        font-size: 14px;
      }
    }

    .qrcode-info {
      margin-top: 12px;
      padding: 0 12px;
      
      .info-item {
        font-size: 14px;
        color: var(--color-text-2);
        line-height: 1.5;
        text-align: left;
      }
    }
  }

  .qrcode-actions {
    margin-top: 12px;
  }
}
</style> 
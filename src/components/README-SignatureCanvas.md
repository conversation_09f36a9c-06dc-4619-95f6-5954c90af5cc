# SignatureCanvas 签字画布组件

## 功能介绍

SignatureCanvas 是一个支持手写签名的 Vue 组件，适用于移动端和桌面端。用户可以用手指或鼠标在画布上签字，组件会自动将签名转换为图片并上传到服务器。

## 主要特性

- ✅ **多平台支持**: 同时支持触摸和鼠标事件
- ✅ **响应式设计**: 自动适配不同屏幕尺寸
- ✅ **高清显示**: 支持高DPI屏幕，签名清晰度高
- ✅ **图片压缩**: 自动压缩签名图片，减少上传时间
- ✅ **自动上传**: 签名完成后自动上传到服务器
- ✅ **状态管理**: 提供清除、确认等操作
- ✅ **错误处理**: 完善的错误提示和处理机制

## 使用方法

### 基础用法

```vue
<template>
  <SignatureCanvas 
    @confirm="onSignatureConfirm"
    @cancel="onSignatureCancel"
  />
</template>

<script setup>
import SignatureCanvas from '@/components/SignatureCanvas.vue'

const onSignatureConfirm = (imageUrl) => {
  console.log('签名图片URL:', imageUrl)
  // 处理签名完成后的逻辑
}

const onSignatureCancel = () => {
  console.log('用户取消签名')
  // 处理取消签名的逻辑
}
</script>
```

### 在弹窗中使用

```vue
<template>
  <van-popup v-model:show="showSignature" position="bottom" :style="{ height: '70%' }">
    <div class="signature-container">
      <h3>请签字确认</h3>
      <SignatureCanvas 
        @confirm="handleSignature"
        @cancel="closeSignature"
      />
    </div>
  </van-popup>
</template>

<script setup>
import { ref } from 'vue'
import SignatureCanvas from '@/components/SignatureCanvas.vue'

const showSignature = ref(false)

const handleSignature = (imageUrl) => {
  console.log('获得签名:', imageUrl)
  showSignature.value = false
  // 继续处理业务逻辑
}

const closeSignature = () => {
  showSignature.value = false
}
</script>
```

## API 接口

### Events 事件

| 事件名 | 说明 | 参数 |
|-------|------|------|
| confirm | 签字确认完成时触发 | `imageUrl: string` - 上传后的图片URL |
| cancel | 取消签字时触发 | 无 |

### 方法

组件内部方法（通过 ref 调用）：

```typescript
interface SignatureCanvasRef {
  clearSignature(): void  // 清除签名
  isEmpty(): boolean      // 检查是否为空
}
```

### 使用示例

```vue
<template>
  <SignatureCanvas ref="signatureRef" @confirm="onConfirm" />
  <button @click="clearSign">清除签名</button>
</template>

<script setup>
import { ref } from 'vue'

const signatureRef = ref()

const clearSign = () => {
  signatureRef.value?.clearSignature()
}

const onConfirm = (url) => {
  console.log('签名URL:', url)
}
</script>
```

## 技术特性

### 1. 画布适配
- 自动适配容器尺寸
- 支持高DPI屏幕显示
- 响应式设计

### 2. 绘制功能
- 平滑的手写效果
- 支持触摸和鼠标事件
- 可自定义画笔样式

### 3. 图片处理
- Canvas转PNG格式
- 自动图片压缩
- 白色背景填充

### 4. 上传机制
- 集成项目上传接口
- 自动错误重试
- 上传进度提示

## 配置说明

### 画笔设置

组件内部默认画笔配置：

```typescript
ctx.lineCap = 'round'        // 线条端点样式
ctx.lineJoin = 'round'       // 线条连接样式  
ctx.strokeStyle = '#000000'  // 画笔颜色
ctx.lineWidth = 3            // 画笔粗细
```

### 压缩设置

签名图片压缩参数：

```typescript
quality: 0.8        // 压缩质量 (0-1)
maxWidth: 800       // 最大宽度
maxHeight: 600      // 最大高度
```

## 样式定制

组件提供了完整的样式定制能力：

```css
.signature-canvas {
  /* 整体容器样式 */
}

.signature-container {
  /* 签字区域样式 */
  height: 300px;  /* 可自定义高度 */
}

.signature-board {
  /* 画布样式 */
  cursor: crosshair;
}

.signature-actions {
  /* 按钮区域样式 */
}
```

## 注意事项

1. **上传接口**: 确保后端上传接口 `/business-rent-rest/common/upload` 正常工作
2. **权限验证**: 组件会自动从 localStorage 获取 token 进行身份验证
3. **网络处理**: 建议在网络不佳时提供重试机制
4. **浏览器兼容**: 依赖 Canvas API，需要现代浏览器支持
5. **触摸优化**: 在移动端建议禁用页面滚动以避免冲突

## 错误处理

组件内置了完善的错误处理机制：

- 画布初始化失败
- 图片转换失败  
- 网络上传失败
- 压缩处理失败

所有错误都会通过 van-toast 显示给用户，并在控制台输出详细日志。

## 性能优化

1. **图片压缩**: 自动压缩减少上传大小
2. **事件防抖**: 避免频繁的绘制操作
3. **内存管理**: 及时清理 Canvas 资源
4. **高DPI适配**: 优化高分辨率屏幕显示效果 
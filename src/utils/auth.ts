const TOKEN_KEY = 'token';
const LOGIN_TIME_KEY = 'login_time';

export function getToken(): string | null {
  return localStorage.getItem(TOKEN_KEY);
}

export function setToken(token: string): void {
  localStorage.setItem(TOKEN_KEY, token);
  localStorage.setItem(LOGIN_TIME_KEY, Date.now().toString());
}

export function removeToken(): void {
  localStorage.removeItem(TOKEN_KEY);
  localStorage.removeItem(LOGIN_TIME_KEY);
}

export function hasToken(): boolean {
  return !!getToken();
}

export function getLoginTime(): number | null {
  const loginTime = localStorage.getItem(LOGIN_TIME_KEY);
  return loginTime ? parseInt(loginTime, 10) : null;
}

export function isTokenExpired(maxAge: number = 24 * 60 * 60 * 1000): boolean {
  const loginTime = getLoginTime();
  if (!loginTime) return true;
  
  return Date.now() - loginTime > maxAge;
}

export function clearAuth(): void {
  removeToken();
} 
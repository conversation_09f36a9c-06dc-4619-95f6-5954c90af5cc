<template>
  <div class="test-water-electric">
    <van-nav-bar title="水电费功能测试" left-arrow @click-left="$router.back()" />
    
    <div class="content">
      <van-cell-group>
        <van-cell title="测试说明" value="模拟财务部水电费录入" />
      </van-cell-group>

      <!-- 水电费测试区域 -->
      <div class="water-electric-demo">
        <div class="section-title">水电费情况（由综合或财务部填写并签字确认）</div>
        
        <van-form>
          <van-field 
            label="电度数" 
            placeholder="请输入电度数" 
            input-align="right"
            label-width="140px" 
            type="number"
            v-model="formData.elecMeterReading">
            <template #button>
              <span class="unit">度</span>
            </template>
          </van-field>
          
          <van-field 
            label="冷水度数" 
            placeholder="请输入冷水度数" 
            input-align="right"
            label-width="140px" 
            type="number"
            v-model="formData.coldWaterReading">
            <template #button>
              <span class="unit">度</span>
            </template>
          </van-field>
          
          <van-field 
            label="热水度数" 
            placeholder="请输入热水度数" 
            input-align="right"
            label-width="140px" 
            type="number"
            v-model="formData.hotWaterReading">
            <template #button>
              <span class="unit">度</span>
            </template>
          </van-field>
          
          <van-field 
            label="电费欠费" 
            placeholder="请输入电费欠费" 
            input-align="right"
            label-width="140px" 
            type="number"
            v-model="formData.elecFee">
            <template #button>
              <span class="unit">元</span>
            </template>
          </van-field>
          
          <van-field 
            label="水费欠费" 
            placeholder="请输入水费欠费" 
            input-align="right"
            label-width="140px" 
            type="number"
            v-model="formData.waterFee">
            <template #button>
              <span class="unit">元</span>
            </template>
          </van-field>
          
          <van-field 
            label="物业欠费" 
            placeholder="请输入物业欠费" 
            input-align="right"
            label-width="140px" 
            type="number"
            v-model="formData.pmFee">
            <template #button>
              <span class="unit">元</span>
            </template>
          </van-field>
        </van-form>
      </div>

      <!-- 计算结果 -->
      <div class="result-section">
        <van-cell-group>
          <van-cell title="总欠费金额" :value="`¥${formatNumber(totalFees)}`" />
          <van-cell title="确认状态" :value="isConfirmed ? '已确认' : '未确认'" />
          <van-cell v-if="confirmTime" title="确认时间" :value="confirmTime" />
        </van-cell-group>
      </div>

      <!-- 操作按钮 -->
      <div class="actions">
        <van-button type="primary" @click="confirmData" :disabled="isConfirmed" block>
          确认数据
        </van-button>
        <van-button type="default" @click="resetData" block style="margin-top: 16px;">
          重置数据
        </van-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { showToast } from 'vant'

// 表单数据
const formData = ref({
  elecMeterReading: 0,
  coldWaterReading: 0,
  hotWaterReading: 0,
  elecFee: 0,
  waterFee: 0,
  pmFee: 0
})

// 确认状态
const isConfirmed = ref(false)
const confirmTime = ref('')

// 计算总费用
const totalFees = computed(() => {
  return (formData.value.elecFee || 0) + 
         (formData.value.waterFee || 0) + 
         (formData.value.pmFee || 0)
})

// 格式化数字
const formatNumber = (num: number): string => {
  return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',')
}

// 确认数据
const confirmData = () => {
  if (totalFees.value === 0) {
    showToast('请至少输入一项费用数据')
    return
  }
  
  isConfirmed.value = true
  confirmTime.value = new Date().toLocaleString('zh-CN')
  
  showToast({
    type: 'success',
    message: '数据确认成功'
  })
}

// 重置数据
const resetData = () => {
  formData.value = {
    elecMeterReading: 0,
    coldWaterReading: 0,
    hotWaterReading: 0,
    elecFee: 0,
    waterFee: 0,
    pmFee: 0
  }
  isConfirmed.value = false
  confirmTime.value = ''
  
  showToast('数据已重置')
}
</script>

<style scoped>
.test-water-electric {
  min-height: 100vh;
  background: #f5f5f5;
}

.content {
  padding: 20px;
}

.water-electric-demo {
  background: #F8F9FF;
  border: 1px solid #E8EBFF;
  border-radius: 12px;
  margin: 20px 0;
  overflow: hidden;
}

.section-title {
  background: #3583FF;
  color: #FFFFFF;
  font-size: 16px;
  font-weight: 600;
  padding: 16px 20px;
  margin: 0;
}

.result-section {
  margin: 20px 0;
}

.actions {
  margin: 30px 0;
}

.unit {
  color: #333333;
  margin-left: 4px;
  font-size: 14px;
}
</style> 
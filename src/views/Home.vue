<template>
	<div class="home-page">
		<div class="home-page-content" v-if="!isLogin">
			<div class="ding-talk-tip">
				<div class="ding-talk-tip-title">
					<span>请在钉钉中打开</span>
				</div>
				<div class="ding-talk-tip-content">
					<span>请在钉钉中打开</span>
				</div>
			</div>
		</div>
		<div class="home-page-content" v-else>
			<div class="home-top">
				<!-- 顶部项目选择器 -->
				<div class="header-section">
					<div class="project-selector" @click="showProjectPicker = true">
						<!-- <van-icon name="location-o" size="20" color="#fff" /> -->
						<img :src="projectIcon" class="project-selector-icon" />
						<span class="project-name">{{ currentProject?.name || '选择项目' }}</span>
						<!-- <img src="../assets/images/home/<USER>" class="project-selector-icon-1" /> -->

						<van-icon name="arrow-down" size="15" color="#fff" class="project-selector-icon-1" />
					</div>
				</div>

				<!-- 欢迎区域 -->
				<div class="welcome-section">
					<div class="welcome-text" @click="handleTestUrl">
						<h2>欢迎回来，{{ nickName }}</h2>
						<!-- {{ JSON.stringify(testInfo) }} -->
					</div>
					<!-- <div class="logo-section">
						<div class="city-skyline" @click="handleTestUrl">
							<div class="building" v-for="i in 8" :key="i"
								:style="{ height: Math.random() * 40 + 20 + 'px' }">
							</div>
						</div>
					</div> -->
				</div>

			</div>


			<!-- <div style="font-size: 24px;color: #fff;margin-bottom: 10px;width: 400px;">{{ writeTextUrl }}</div> -->

			<!-- 主要功能模块 -->
			<div class="main-modules">
				<div class="module-item" v-for="(module, index) in mainModules" :key="index"
					@click="navigateTo(module.path)">
					<div class="module-icon">
						<!-- <van-icon :name="module.icon" size="48" color="#fff" /> -->
						 
						<img :src="imageMap[module.img]" alt="" class="module-icon-img">
					</div>
					<span class="module-title">{{ module.title }}</span>
				</div>
			</div>

			<!-- 待办事项 -->
			<div class="section">
				<div class="section-header">
					<img :src="todoIcon" alt="" class="section-icon">

					<!-- <van-icon name="bell" size="32" color="#1989fa" /> -->
					<span class="section-title">待办</span>
				</div>

				<!-- "pendingEffectiveCount": 16,
        "pendingTransferCount": 1,
        "pendingEnterCount": 33,
        "pendingExitCount": 0,
        "exitProcessingCount": 16 -->
				<!-- pendingEffectiveCount
integer <int32>
待生效定单数量
可选
pendingTransferCount
integer <int32>
待转签定单数量
可选
pendingEnterCount
integer <int32>
待进场数量
可选
pendingExitCount
integer <int32>
待出场数量
可选
exitProcessingCount
integer <int32>
出场办理中数量 -->

				<div class="todo-grid" v-if="pendingStatsData">
					<div class="todo-item" @click="navigateToBookingList(0)">
						<div class="todo-label">待生效定单</div>
						<div class="todo-count">{{ pendingStatsData.pendingEffectiveCount }}</div>
					</div>
					<div class="todo-item" @click="navigateToBookingList(1)">
						<div class="todo-label">待签定单</div>
						<div class="todo-count">{{ pendingStatsData.pendingTransferCount }}</div>
					</div>
					<div class="todo-item" @click="navigateToEntryManagement(0)">
						<div class="todo-label">待进场</div>
						<div class="todo-count">{{ pendingStatsData.pendingEnterCount }}</div>
					</div>
					<div class="todo-item" @click="navigateToExitManagement(0)">
						<div class="todo-label">待出场</div>
						<div class="todo-count">{{ pendingStatsData.pendingExitCount }}</div>
					</div>
					<div class="todo-item" @click="navigateToExitManagement(1)">
						<div class="todo-label">出场办理中</div>
						<div class="todo-count">{{ pendingStatsData.exitProcessingCount }}</div>
					</div>
				</div>
			</div>

			<!-- 房态统计 -->
			<div class="section">
				<div class="section-header" @click="navigateToRoomDiagram">
					<!-- <van-icon name="home-o" size="32" color="#1989fa" /> -->
					<img :src="roomStateIcon" alt="" class="section-icon">
					<span class="section-title">房态</span>
					<span class="unit-text">面积单位：m²</span>
					<van-icon name="arrow" size="16" color="#999" class="arrow-icon" />
				</div>

				<div class="room-stats" v-if="roomStatsData">
					<!-- 住宅 -->
					<div class="room-type-item" v-for="(item,index) in roomStatsData" :key="item.id" :class="['room-type-content-'+index]">
						<div class="room-type-icon">
							<!-- <van-icon name="home-o" size="40" color="#fff" /> -->
							<img :src="imageMap[item.icon]" alt="" class="room-type-icon-img">
							<span class="room-type-text">{{ item.propertyTypeName }}</span>
						</div>
						<div class="room-type-content" >
							<div class="room-type-header">
								<div class="room-counts">
									<span class="room-counts-item">在租 <span class="room-counts-item-value">{{
										item.rentArea }}</span></span>
									<span class="room-counts-item">空置 <span class="room-counts-item-value">{{
										item.emptyArea }}</span></span>
									<span class="room-counts-item">合计 <span class="room-counts-item-value">{{
										item.totalArea }}</span></span>
								</div>
							</div>
							<div class="room-type-footer">
								<span class="sub-text">待生效/签约中/已预定 <span class="sub-text-value">{{ item.toEffectArea
										}}</span></span>
							</div>
						</div>
					</div>

					<!-- 厂房 -->
					<!-- <div class="room-type-item">
						<div class="room-type-icon factory">
							<van-icon name="shop-o" size="40" color="#fff" />
						</div>
						<div class="room-type-content">
							<div class="room-type-header">
								<span class="room-type-label">厂房</span>
								<div class="room-counts">
									<span>在租 {{ statsData.roomStats.factory.rented }}</span>
									<span>空置 {{ statsData.roomStats.factory.vacant }}</span>
									<span>合计 {{ statsData.roomStats.factory.total }}</span>
								</div>
							</div>
							<div class="room-type-footer">
								<span class="sub-text">待生效/签约中/已预定 {{
									statsData.roomStats.factory.rentedOrSignedOrReserved
									}}</span>
							</div>
						</div>
					</div> -->

					<!-- 商铺 -->
					<!-- <div class="room-type-item">
						<div class="room-type-icon commercial">
							<van-icon name="shop-collect-o" size="40" color="#fff" />
						</div>
						<div class="room-type-content">
							<div class="room-type-header">
								<span class="room-type-label">商铺</span>
								<div class="room-counts">
									<span>在租 {{ statsData.roomStats.commercial.rented }}</span>
									<span>空置 {{ statsData.roomStats.commercial.vacant }}</span>
									<span>合计 {{ statsData.roomStats.commercial.total }}</span>
								</div>
							</div>
							<div class="room-type-footer">
								<span class="sub-text">待生效/签约中/已预定 {{
									statsData.roomStats.commercial.rentedOrSignedOrReserved }}</span>
							</div>
						</div>
					</div> -->
				</div>
			</div>

			<!-- 项目选择弹窗 - 改为级联选择器 -->
			<van-popup v-model:show="showProjectPicker" position="bottom" round style="height: 90vh;">
				<div class="project-picker">
					<div class="picker-header">
						<span class="picker-title">选择项目</span>
						<van-icon name="cross" size="36" @click="showProjectPicker = false" />
					</div>
					<!-- <div class="project-search">
						<van-search v-model="searchText" placeholder="搜索项目" shape="round" />
					</div> -->
					<div class="project-list">

						<div class="project-tree">
							<div v-for="org in filteredOrgTree" :key="org.id" class="org-group">
								<div class="org-title">{{ org.name }}</div>
								<div v-for="dept in org.children" :key="dept.id" class="dept-group">
									<div class="dept-title">{{ dept.name }}</div>
									<div v-for="region in dept.children" :key="region.id" class="region-group">
										<div class="region-title">{{ region.name }}</div>
										<div v-if="region.children" class="project-items">
											<div v-for="project in region.children" :key="project.id"
												class="project-item"
												:class="{ active: project.id === currentProject?.id }"
												@click="onProjectSelect(project)">
												<span>{{ project.name }}</span>
												<van-icon v-if="project.id === currentProject?.id" name="success"
													size="32" color="#1989fa" />
											</div>
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
			</van-popup>
		</div>
	</div>
</template>
<script lang="ts">
export default {
	name: 'Home'
}
</script>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { getHomeStats, getCurrentProject, setCurrentProject, getDingTalkToken, getRoomStats, getPendingStats } from '../api/home'
import type { HomeStatsData, ProjectInfo } from '../api/home'
// 引入 getOrgTree 方法
import { getOrgTree } from '../api/organization'
import { setToken, hasToken } from '../utils/auth'
import { showConfirmDialog } from 'vant'
import { useKeepAliveTest } from '../utils/keep-alive-test'

// 导入图片资源
import icon0 from '../assets/images/home/<USER>'
import icon1 from '../assets/images/home/<USER>'
import icon2 from '../assets/images/home/<USER>'
import icon3 from '../assets/images/home/<USER>'
import projectIcon from '../assets/images/home/<USER>'
import todoIcon from '../assets/images/home/<USER>'
import roomStateIcon from '../assets/images/home/<USER>'
import roomTypeIcon0 from '../assets/images/home/<USER>'
import roomTypeIcon1 from '../assets/images/home/<USER>'
import roomTypeIcon2 from '../assets/images/home/<USER>'

// 创建图片映射
const imageMap: Record<string, string> = {
    'icon-0.png': icon0,
    'icon-1.png': icon1,
    'icon-2.png': icon2,
    'icon-3.png': icon3,
    'f-0.png': roomTypeIcon0,
    'f-1.png': roomTypeIcon1,
    'f-2.png': roomTypeIcon2
}

const router = useRouter()

// 数据状态
const loading = ref(false)
const statsData = ref<HomeStatsData | null>(null)
const currentProject = ref<ProjectInfo | null>(null)
const projectList = ref<ProjectInfo[]>([])
const showProjectPicker = ref(false)
// 级联选择器相关状态
const selectedProjectPath = ref('')
const cascaderOptions = ref<any[]>([])
const searchText = ref('')
const orgTree = ref<any[]>([])

// 过滤组织树的计算属性
const filteredOrgTree = computed(() => {
	if (!searchText.value) {
		return orgTree.value
	}

	const filterNode = (node: any): any => {
		const nameMatch = node.name.toLowerCase().includes(searchText.value.toLowerCase())

		if (node.children) {
			const filteredChildren = node.children
				.map(filterNode)
				.filter((child: any) => child !== null)

			if (filteredChildren.length > 0 || nameMatch) {
				return {
					...node,
					children: filteredChildren
				}
			}
		} else if (nameMatch) {
			return node
		}

		return null
	}

	return orgTree.value
		.map(filterNode)
		.filter(node => node !== null)
})

// 主要功能模块
const mainModules = ref([
	{
		title: '房态图',
		icon: 'home-o',
		img: 'icon-0.png',
		color: '#4A90E2',
		path: '/room-state-diagram'
	},
	{
		title: '定单管理',
		icon: 'orders-o',
		img: 'icon-1.png',
		color: '#F5A623',
		path: '/booking-list'
	},
	{
		title: '进场管理',
		icon: 'sign',
		img: 'icon-2.png',
		color: '#50E3C2',
		path: '/entry-management'
	},
	{
		title: '出场管理',
		icon: 'completed',
		img: 'icon-3.png',
		color: '#B8860B',
		path: '/exit-management'
	}
])

// handleTestUrl
let writeTextUrl = ''
const handleTestUrl = () => {
	// 复制链接
	writeTextUrl = `${window.location.origin}?token=${localStorage.token}`
	navigator.clipboard.writeText(`${window.location.origin}?token=${localStorage.token}`)
	// window.location.href = 'http://localhost:5173?corpid=ding3c536f1c1c1f68dabc961a6cb783455b'
}

// 页面跳转
const navigateTo = (path: string) => {
	router.push(path)
}

// 跳转到房态图页面
const navigateToRoomDiagram = () => {
	if (currentProject.value?.id) {
		router.push({
			name: 'RoomStateDiagram',
			query: { projectId: currentProject.value.id }
		})
	} else {
		router.push({ name: 'RoomStateDiagram' })
	}
}

// 跳转到定单列表页面
const navigateToBookingList = (tabIndex: number) => {
	// 使用sessionStorage来传递tab索引
	sessionStorage.setItem('bookingTab', tabIndex.toString())
	router.push({ name: 'BookingList' })
}

// 跳转到进场管理页面
const navigateToEntryManagement = (tabIndex: number) => {
	// 使用sessionStorage来传递tab索引
	sessionStorage.setItem('entryTab', tabIndex.toString())
	router.push({ name: 'EntryManagement' })
}

// 跳转到出场管理页面
const navigateToExitManagement = (tabIndex: number) => {
	// 使用sessionStorage来传递tab索引
	sessionStorage.setItem('exitTab', tabIndex.toString())
	router.push({ name: 'ExitManagement' })
}

// 递归查找所有最底层的项目节点
const findLeafProjects = (nodes: any[]): ProjectInfo[] => {
	const projects: ProjectInfo[] = []

	const traverse = (node: any) => {
		if (!node.children || node.children.length === 0) {
			// 这是叶子节点，且 level 为 4 的是具体项目
			if (node.level === 4) {
				projects.push({
					id: node.id,
					name: node.name,
					shortName: node.name
				})
			}
		} else {
			// 继续遍历子节点
			node.children.forEach(traverse)
		}
	}

	nodes.forEach(traverse)
	return projects
}

// 获取项目列表（使用 getOrgTree 替代原有方法）
const fetchProjects = async () => {
	try {
		const response = await getOrgTree({ relProjectFlag: 2 }) // 查询已关联项目的组织树
		if (response.code === 200 && Array.isArray(response.data)) {
			// 设置组织树数据
			orgTree.value = response.data

			// 提取所有最底层的项目用于后续处理
			projectList.value = findLeafProjects(response.data)
			let currentProjectStr = localStorage.getItem('currentProject')
			if (currentProjectStr) {
				currentProject.value = JSON.parse(currentProjectStr)
			} else {
				currentProject.value = projectList.value[0]
				localStorage.setItem('currentProject', JSON.stringify(currentProject.value))
			}
		} else {

		}
	} catch (error) {
		console.error('获取项目列表失败:', error)
		// "Cannot invoke \"com.business.platform.api.model.LoginUser.getProjectPermissions()\" because \"loginUser\" is null"

	}
}

// 获取当前项目
const fetchCurrentProject = async () => {
	try {
		const currentProjectStr = localStorage.getItem('currentProject')
		if (currentProjectStr) {
			currentProject.value = JSON.parse(currentProjectStr)
		} else {
			// currentProject.value = {
			// 	id: '1',
			// 	name: '福州(马尾)万洋广场',
			// 	shortName: '福州万洋'
			// }
		}
	} catch (error) {
		console.error('获取当前项目失败:', error)
	}
}



let roomStatsData = ref<any[]>([])
// 获取房态统计数据
const fetchRoomStats = async () => {
	// @ts-ignore
	const response = await getRoomStats(currentProject.value?.id)
	// 	propertyType
	// string 
	// 物业类型
	// 可选
	// propertyTypeName
	// string 
	// 物业类型名称
	// 可选
	// rentArea
	// number 
	// 在租面积
	// 可选
	// emptyArea
	// number 
	// 空置面积
	// 可选
	// totalArea
	// number 
	// 合计面积
	// 可选
	// toEffectArea
	// number 
	// 可选
	// 待生效/签约中/已预订的面积
	// data
	console.log('response', response)
	let imgList = ['f-0.png', 'f-1.png', 'f-2.png', 'f-0.png', 'f-1.png', 'f-2.png', 'f-0.png', 'f-1.png', 'f-2.png', 'f-0.png', 'f-1.png', 'f-2.png', 'f-0.png', 'f-1.png', 'f-2.png']
	roomStatsData.value = response.data
	roomStatsData.value.forEach((item: any, index: number) => {
		if (item.propertyTypeName === '宿舍') {
			item.icon = imgList[0]
		} else if (item.propertyTypeName === '厂房') {
			item.icon = imgList[1]
		} else if (item.propertyTypeName === '商铺') {
			item.icon = imgList[2]
		} else {
			item.icon = imgList[index]
		}
	})
}

let pendingStatsData = ref({
	"pendingEffectiveCount": 16,
	"pendingTransferCount": 1,
	"pendingEnterCount": 33,
	"pendingExitCount": 0,
	"exitProcessingCount": 16
})
// 获取待办事项统计数据
const fetchPendingStats = async () => {
	/**
	 * {
	"msg": "操作成功",
	"code": 200,
	"data": {
		"pendingEffectiveCount": 16,
		"pendingTransferCount": 1,
		"pendingEnterCount": 33,
		"pendingExitCount": 0,
		"exitProcessingCount": 16
	}
}*/
	// @ts-ignore
	const response = await getPendingStats(currentProject.value?.id)
	console.log('response', response)
	pendingStatsData.value = response.data


}


// 项目选择
const onProjectSelect = async (project: any) => {
	const selectedProject: ProjectInfo = {
		id: project.id,
		name: project.name,
		shortName: project.name
	};
	localStorage.setItem('currentProject', JSON.stringify(selectedProject))

	currentProject.value = selectedProject;
	showProjectPicker.value = false;

	try {
		// await setCurrentProject(selectedProject.id);
		await fetchRoomStats()
		await fetchPendingStats()
	} catch (error) {
		console.error('设置项目失败:', error);
	}
};
const nickName = ref('')
// 设置为：http://localhost:5173?corpid=$CORPID$，用于后续测试使用。
const getDingTalkTokenMethod = async (code: string) => {
	console.log('code', code)
	const response = await getDingTalkToken(code)
	/**
	 * response {"code": 200, "msg": null, "data": {"access_token": "eyJhbGciOiJIUzUxMiJ9.eyJ1c2VyX2lkIjoxMTksIm5pY2tuYW1lIjoi5Lil546J5aifIiwidXNlcl9rZXkiOiJkMWQ1NjI4OS0zYzdmLTRkYWUtOWQyNy1hMzExY2FjNDgwNmEiLCJ1c2VybmFtZSI6Inlhbnl1anVhbiJ9.WaSZ1-eEDJEnVWWh0VzOwpx6wq3TdRfTlIspOVeZ28YN-hhsMBTW8FkvhEJTX1vLlQfSEQhzHLAEuRJPbNhTVQ", "sysUser": {"userId": 119, "deptId": null, "userName": "yanyujuan", "nickName": "严玉娟", "email": null, "phonenumber": "***********", "sex": "2", "avatar": null, "password": "$2a$10$COGYk5nmJeeuKv20UfDaDe34fze5iLbdF8hkJd59RLOdiHRSMOdCm", "status": "0", "delFlag": "0", "loginIp": null, "postName": null, "loginDate": null, "roles": [{"roleId": 100, "roleName": "菜单权限", "type": 2, "remark": null, "roomPermissions": "business", "contractPermissions": "personal", "admin": false}, {"roleId": 102, "roleName": "数据权限2", "type": 3, "remark": null, "roomPermissions": "all,business,zhongchuang", "contractPermissions": "all", "admin": false}], "admin": false}, "expires_in": 720}}*/
	console.log('response', response)
	setToken(response.data.access_token)

	await fetchCurrentProject()
	await fetchProjects() // 使用新的 fetchProjects 方法

	await fetchRoomStats()
	await fetchPendingStats()

	nickName.value = response.data.sysUser.nickName


}

let testInfo = ref({})
const initDD = () => {
	// setTimeout(() => {
	// @ts-ignore
	console.log('initDD', dd)
	// @ts-ignore
	dd.ready(function () {
		// @ts-ignore
		dd.runtime.permission.requestAuthCode({
			corpId: "ding3c536f1c1c1f68dabc961a6cb783455b", // 企业id
			onSuccess: function (info: any) {
				testInfo.value = info
				console.log('info', info)
				getDingTalkTokenMethod(info.code)
				// code = info.code // 通过该免登授权码可以获取用户身份
			}
		})
	})
	// }, 333)
	// 	dd.config({
	//     agentId: '3606300013', // 企业内部应用，该值为企业内部应用的agentId。
	//     corpId: 'ding3c536f1c1c1f68dabc961a6cb783455b',//必填，企业ID
	//     timeStamp: '', // 必填，生成签名的时间戳
	//     nonceStr: '', // 必填，自定义固定字符串。
	//     signature: '', // 必填，签名
	//     type:0/1,   //选填。0表示微应用的jsapi,1表示服务窗的jsapi；不填默认为0。该参数从dingtalk.js的0.8.3版本开始支持
	//     jsApiList : [
	//         'biz.contact.choose',
	//         'chooseChat'
	//     ] // 必填，需要使用的jsapi列表，注意：不要带dd。
	// });

	// dd.error(function (err) {
	//     alert('dd error: ' + JSON.stringify(err));
	// })//该方法必须带上，用来捕获鉴权出现的异常信息，否则不方便排查出现的问题
}
let isLogin = ref(false)
// 初始化数据
const initData = async () => {
	// 判断为钉钉环境
	// console.log(window.navigator.userAgent.indexOf('DingTalk'))
	if (window.navigator.userAgent.indexOf('DingTalk') > -1 || window.location.href.includes('dingtalk') || window.location.href.includes('Ding Talk')) {
		isLogin.value = true
		initDD()
	} else {
		// 非钉钉环境
		if (window.location.href.includes('token')) {
			isLogin.value = true
			const token = window.location.href.split('token=')[1]
			setToken(token)
		} else {
			isLogin.value = false
			// alert('请在钉钉中打开')
			return false
		}
		fetchCurrentProject()
		fetchProjects()
		fetchRoomStats()
		fetchPendingStats()
	}
}

// Keep-Alive 测试
const keepAliveTest = useKeepAliveTest('Home')

// 页面加载时初始化
onMounted(() => {
	initData()

	// 打印 keep-alive 测试报告
	setTimeout(() => {
		keepAliveTest.printReport()
	}, 1000)
})
</script>



<style scoped>
.home-page {
	min-height: 100vh;
	/* background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); */
	/* color: #fff; */
}

.home-page-content {
	min-height: 100vh;
	/* background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); */
	color: #fff;

	.home-top {
		background: url('../assets/images/home/<USER>') no-repeat center center;
		background-size: cover;
		height: 420px;
		width: 100%;
	}

	.ding-talk-tip {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		height: 100vh;
	}

	.ding-talk-tip-title {
		font-size: 32px;
		font-weight: 500;
	}

	.ding-talk-tip-content {
		font-size: 24px;
	}
}

/* 顶部项目选择器 */
.header-section {

	display: flex;
	align-items: center;
	width: 100%;
	box-sizing: border-box;
	padding: 80px 32px 0 32px;


	/* padding: 88px 32px 32px; */
	.project-selector {
		display: flex;
		align-items: center;
		gap: 16px;
		cursor: pointer;
		background: rgba(0, 0, 0, 0.3);
		box-sizing: border-box;
		padding: 16px;
		border-radius: 64px;

		.project-selector-icon {
			width: 50px;
			height: 50px;
		}

		.project-selector-icon-1 {
			/* width: 20px; */
			/* height: 20px; */
		}

		.project-name {
			font-size: 32px;
			font-weight: 500;
		}
	}




}


/* 欢迎区域 */
.welcome-section {
	padding: 32px 32px 0 60px;
	display: flex;
	justify-content: space-between;
	align-items: center;
	.welcome-text h2 {
		font-size: 30px;
		font-weight: 400;
		margin: 0;
	}
}



.logo-section {
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: 16px;
}

.logo-text {
	font-size: 96px;
	font-weight: bold;
	color: #FFD700;
	text-shadow: 4px 4px 8px rgba(0, 0, 0, 0.3);
}

.city-skyline {
	display: flex;
	align-items: flex-end;
	gap: 4px;
	height: 80px;
}

.building {
	width: 16px;
	background: rgba(255, 255, 255, 0.6);
	border-radius: 2px 2px 0 0;
}

/* 主要功能模块 */
.main-modules {
	background: #fff;
	margin: -64px 32px 32px;
	border-radius: 24px;
	padding: 40px;
	display: grid;
	grid-template-columns: repeat(4, 1fr);
	gap: 32px;
	box-shadow: 0 4px 24px rgba(0, 0, 0, 0.1);
}

.module-item {
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: 16px;
	cursor: pointer;
	transition: transform 0.2s;
}

.module-item:active {
	transform: scale(0.95);
}

.module-icon {
	width: 96px;
	height: 96px;
	border-radius: 24px;
	display: flex;
	align-items: center;
	justify-content: center;
	img {
		width: 100%;
		height: auto;
	}
}

.module-title {
	font-size: 26px;
	color: #333;
	text-align: center;
	/* font-weight: 500; */
}

/* 通用区块样式 */
.section {
	background: #fff;
	margin: 0 32px 32px;
	border-radius: 24px;
	padding: 32px;
	box-shadow: 0 4px 24px rgba(0, 0, 0, 0.1);
}

.section-header {
	display: flex;
	align-items: center;
	gap: 16px;
	margin-bottom: 32px;
	cursor: pointer;
	transition: opacity 0.2s ease;

	&:hover {
		opacity: 0.8;
	}

	.section-icon {
		width: 40px;
		/* height: 40px; */
	}

	.arrow-icon {
		margin-left: auto;
	}
}

.section-title {
	font-size: 32px;
	font-weight: 500;
	color: #333;
}

.unit-text {
	flex: 1;
	font-size: 24px;
	color: #999;
}

/* 待办事项 */
.todo-grid {
	display: grid;
	grid-template-columns: repeat(3, 1fr);
	gap: 24px;
}

.todo-item {
	text-align: center;
	padding: 24px 16px;
	background: linear-gradient( 315deg, #48eaff22 0%, #36a0f614 100%);
	border-radius: 16px;
	cursor: pointer;
	transition: transform 0.2s, opacity 0.2s;
}

.todo-item:hover {
	opacity: 0.8;
}

.todo-item:active {
	transform: scale(0.95);
}

.todo-label {
	font-size: 24px;
	color: #666;
	margin-bottom: 8px;
}

.todo-count {
	font-size: 40px;
	font-weight: bold;
	color: #333;
}

/* 房态统计 */
.room-stats {
	display: flex;
	flex-direction: column;
	gap: 24px;
}

.room-type-item {
	display: flex;
	align-items: center;
	gap: 24px;
	/* padding: 24px; */
	background: #f8f9fa;
	border-radius: 16px;
}

.room-type-icon {
	width: 120px;
	/* height: 100px; */
	border-radius: 16px;
	display: flex;
	align-items: center;
	justify-content: center;
	flex-shrink: 0;
	position: relative;

	.room-type-icon-img {
		width: 100%;
		height: auto;
	}

	.room-type-text {
		position: absolute;
		bottom: 24px;
		left: 0;
		right: 0;
		text-align: center;
		font-size: 24px;
		font-weight: 500;
		color: #666;
	}
}

/* .room-type-icon.residential {
	background: #ff6b6b;
}

.room-type-icon.factory {
	background: #4ecdc4;
}

.room-type-icon.commercial {
	background: #45b7d1;
} */

.room-type-content {
	flex: 1;
	width: 0;
}

.room-type-content-0 {
	background: linear-gradient( 90deg, #FFFFFF 0%, #FFF2EC 100%);
}
.room-type-content-1 {
	background: linear-gradient( 90deg, #FFFFFF 0%, #E1F2FF 100%);
}
.room-type-content-2 {
	background: linear-gradient( 270deg, #F2EEFF 0%, rgba(231,232,255,0) 100%);
}
.room-type-content-3 {
	background: linear-gradient( 90deg, #FFFFFF 0%, #FFF2EC 100%);
}
.room-type-content-4 {
	background: linear-gradient( 90deg, #FFFFFF 0%, #E1F2FF 100%);
}
.room-type-content-5 {
	background: linear-gradient( 270deg, #F2EEFF 0%, rgba(231,232,255,0) 100%);
}
.room-type-content-6 {
	background: linear-gradient( 90deg, #FFFFFF 0%, #FFF2EC 100%);
}
.room-type-content-7 {
	background: linear-gradient( 90deg, #FFFFFF 0%, #E1F2FF 100%);
}
.room-type-content-8 {
	background: linear-gradient( 270deg, #F2EEFF 0%, rgba(231,232,255,0) 100%);
}
.room-type-content-9 {
	background: linear-gradient( 90deg, #FFFFFF 0%, #FFF2EC 100%);
}
.room-type-content-10 {
	background: linear-gradient( 90deg, #FFFFFF 0%, #E1F2FF 100%);
}
.room-type-content-11 {
	background: linear-gradient( 270deg, #F2EEFF 0%, rgba(231,232,255,0) 100%);
}





.room-type-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 16px;
}

.room-type-label {
	font-size: 28px;
	font-weight: 500;
	color: #333;
}

.room-counts {
	display: flex;
	gap: 8px;
	font-size: 24px;
	color: #666;
	flex-wrap: nowrap;

	.room-counts-item {
		display: flex;
		align-items: center;
		flex-wrap: nowrap;
		justify-content: space-between;

		.room-counts-item-value {
			color: #333;
			font-weight: bold;
			margin-left: 2px;
		}
	}
}

.room-type-footer {
	font-size: 22px;
	color: #999;

	.sub-text-value {
		color: #333;
		font-weight: bold;
	}
}

/* 项目选择弹窗 */
.project-picker {
	height: 100%;
	padding: 32px;
	/* max-height: 800px; */
	display: grid;
	grid-template-rows: 100px 1fr;
}

.picker-header {
	height: 100px;
	display: flex;
	justify-content: space-between;
	align-items: center;
	/* margin-bottom: 32px; */
	padding-bottom: 24px;
	border-bottom: 2px solid #eee;
}

.picker-title {
	font-size: 32px;
	font-weight: 500;
	color: #333;
}

.project-list {
	/* max-height: 800px; */
	/* height: 100%; */
	overflow-y: auto;
	box-sizing: border-box;
	padding: 32px 0;
	/* height: calc(100% - 100px); */
}

.project-search {
	padding: 16px 0;
	border-bottom: 2px solid #f5f5f5;
	/* margin-bottom: 32px; */
}

.project-tree {
	padding-bottom: 32px;
}

.org-group,
.dept-group,
.region-group {
	margin-bottom: 24px;
}

.org-title {
	font-size: 28px;
	font-weight: 400;
	color: #666;
	margin-bottom: 16px;
	padding-left: 16px;
	border-left: 6px solid #1989fa;
}

.dept-title {
	font-size: 26px;
	font-weight: 400;
	color: #666;
	margin-bottom: 12px;
	padding-left: 32px;
}

.region-title {
	font-size: 24px;
	font-weight: 400;
	color: #666;
	margin-bottom: 8px;
	padding-left: 48px;
}

.project-items {
	padding-left: 64px;
}

.project-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 20px 24px;
	margin-bottom: 8px;
	border-radius: 12px;
	border: 2px solid #f0f0f0;
	cursor: pointer;
	color: #000;
	transition: all 0.2s;
	font-size: 28px;
	font-weight: bold;
}

.project-item:hover {
	border-color: #1989fa;
	background: #f0f8ff;
}

.project-item.active {
	color: #1989fa;
	border-color: #1989fa;
	background: #f0f8ff;
}

.project-item:active {
	transform: scale(0.98);
}
</style>
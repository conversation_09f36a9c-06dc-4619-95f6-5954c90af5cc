<template>
  <div class="login-container">
    <div class="login-card">
      <div class="logo-section">
        <h1 class="app-title">万洋资管平台</h1>
        <p class="app-subtitle">专业的资产管理系统</p>
      </div>
      
      <div class="login-content">
        <div v-if="loading" class="loading-section">
          <div class="spinner"></div>
          <p class="loading-text">正在登录中...</p>
        </div>
        
        <div v-else-if="loginStatus === 'success'" class="success-section">
          <div class="success-icon">✓</div>
          <h2 class="success-title">登录成功</h2>
          <p class="success-text">即将跳转到首页...</p>
        </div>
        
        <div v-else-if="loginStatus === 'error'" class="error-section">
          <div class="error-icon">✗</div>
          <h2 class="error-title">登录失败</h2>
          <p class="error-text">{{ errorMessage }}</p>
          <button @click="handleRetry" class="retry-button">重新尝试</button>
        </div>
        
        <div v-else class="manual-login-section">
          <h2 class="manual-title">手动登录</h2>
          <div class="form-group">
            <label for="token-input">请输入Token:</label>
            <textarea
              id="token-input"
              v-model="manualToken"
              placeholder="请粘贴您的访问Token"
              class="token-input"
              rows="4"
            ></textarea>
          </div>
          <button @click="handleManualLogin" class="login-button" :disabled="!manualToken.trim()">
            登录
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { setToken, hasToken } from '../utils/auth'

const router = useRouter()
const route = useRoute()

const loading = ref(false)
const loginStatus = ref<'success' | 'error' | 'manual' | null>(null)
const errorMessage = ref('')
const manualToken = ref('')

// 从URL参数中获取token
const getTokenFromUrl = (): string | null => {
  const token = route.query.token as string
  return token || null
}

// 验证token格式（基础验证）
const validateToken = (token: string): boolean => {
  if (!token || token.trim().length === 0) {
    return false
  }
  
  // 简单的token格式验证
  if (token.length < 10) {
    return false
  }
  
  return true
}

// 执行登录
const performLogin = async (token: string): Promise<void> => {
  try {
    loading.value = true
    
    if (!validateToken(token)) {
      throw new Error('Token格式不正确')
    }
    
    // 存储token到本地
    setToken(token)
    
    // 模拟验证过程（可以在这里添加API调用来验证token）
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    loginStatus.value = 'success'
    
    // 延迟跳转到首页
    setTimeout(() => {
      router.replace('/')
    }, 1500)
    
  } catch (error: any) {
    loginStatus.value = 'error'
    errorMessage.value = error.message || '登录过程中发生错误'
  } finally {
    loading.value = false
  }
}

// 手动登录
const handleManualLogin = async (): Promise<void> => {
  if (!manualToken.value.trim()) {
    errorMessage.value = '请输入有效的Token'
    loginStatus.value = 'error'
    return
  }
  
  await performLogin(manualToken.value.trim())
}

// 重试登录
const handleRetry = (): void => {
  loginStatus.value = null
  errorMessage.value = ''
  manualToken.value = ''
  
  // 尝试从URL重新获取token
  const urlToken = getTokenFromUrl()
  if (urlToken) {
    performLogin(urlToken)
  } else {
    loginStatus.value = 'manual'
  }
}

// 页面初始化
onMounted(async () => {
  // 如果已经有token且有效，直接跳转
  // if (hasToken()) {
  //   router.replace('/')
  //   return
  // }
  
  // 尝试从URL获取token
  const urlToken = getTokenFromUrl()
  
  if (urlToken) {
    // 有token，执行自动登录
    await performLogin(urlToken)
  } else {
    // 没有token，显示手动输入界面
    loginStatus.value = 'manual'
  }
})
</script>

<style scoped>
.login-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
}

.login-card {
  background: white;
  border-radius: 16px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
  padding: 40px;
  width: 100%;
  max-width: 480px;
  text-align: center;
}

.logo-section {
  margin-bottom: 40px;
}

.app-title {
  font-size: 28px;
  font-weight: bold;
  color: #333;
  margin: 0 0 8px 0;
}

.app-subtitle {
  font-size: 16px;
  color: #666;
  margin: 0;
}

.login-content {
  min-height: 200px;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

/* Loading 样式 */
.loading-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20px;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 16px;
  color: #666;
  margin: 0;
}

/* Success 样式 */
.success-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}

.success-icon {
  width: 60px;
  height: 60px;
  background: #4ade80;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 30px;
  color: white;
  font-weight: bold;
}

.success-title {
  font-size: 20px;
  color: #333;
  margin: 0;
}

.success-text {
  font-size: 14px;
  color: #666;
  margin: 0;
}

/* Error 样式 */
.error-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}

.error-icon {
  width: 60px;
  height: 60px;
  background: #ef4444;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 30px;
  color: white;
  font-weight: bold;
}

.error-title {
  font-size: 20px;
  color: #333;
  margin: 0;
}

.error-text {
  font-size: 14px;
  color: #666;
  margin: 0;
}

.retry-button {
  background: #667eea;
  color: white;
  border: none;
  border-radius: 8px;
  padding: 12px 24px;
  font-size: 14px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.retry-button:hover {
  background: #5a67d8;
}

/* Manual Login 样式 */
.manual-login-section {
  text-align: left;
}

.manual-title {
  font-size: 20px;
  color: #333;
  margin: 0 0 24px 0;
  text-align: center;
}

.form-group {
  margin-bottom: 24px;
}

.form-group label {
  display: block;
  font-size: 14px;
  color: #333;
  margin-bottom: 8px;
  font-weight: 500;
}

.token-input {
  width: 100%;
  padding: 12px;
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  font-size: 14px;
  font-family: monospace;
  resize: vertical;
  min-height: 100px;
  transition: border-color 0.2s;
}

.token-input:focus {
  outline: none;
  border-color: #667eea;
}

.login-button {
  width: 100%;
  background: #667eea;
  color: white;
  border: none;
  border-radius: 8px;
  padding: 14px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.login-button:hover:not(:disabled) {
  background: #5a67d8;
}

.login-button:disabled {
  background: #cbd5e0;
  cursor: not-allowed;
}

/* 响应式设计 */
@media (max-width: 480px) {
  .login-card {
    padding: 30px 20px;
    margin: 10px;
  }
  
  .app-title {
    font-size: 24px;
  }
  
  .app-subtitle {
    font-size: 14px;
  }
}
</style> 
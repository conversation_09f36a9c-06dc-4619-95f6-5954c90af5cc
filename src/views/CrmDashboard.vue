<script lang="ts">
export default {
  name: 'CrmDashboard'
}
</script>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { getDashboardStats } from '../api/crm'
import type { DashboardStats } from '../api/crm'

// 数据状态
const loading = ref(false)
const dashboardData = ref<DashboardStats | null>(null)

// 日期相关
const currentDate = ref('05月22日待办')
const todayStats = ref({
  newCustomers: 4,
  basicCustomers: 3,
  premiumCustomers: 5
})

// 快捷入口菜单
const quickMenus = ref([
  { name: '添加客户', icon: 'add-o' },
  { name: '跟进客户', icon: 'phone-o' },
  { name: '课程客户', icon: 'certificate' },
  { name: '来访客户', icon: 'friends-o' }
])

// 提醒数据
const reminders = ref([
  { type: '主日提醒', count: 3 },
  { type: '跟进提醒', count: 5 },
  { type: '回访提醒', count: 1 },
  { type: '生日提醒', count: 4 }
])

// 个人关键指标
const personalStats = ref({
  period: '2025-04-28 至 2025-05-04',
  totalAmount: 420000.00,
  newCustomers: 13,
  premiumCustomers: 33,
  basicCustomers: 27
})

// 个人成交趋势
const trendStats = ref({
  period: '2025-04-28 至 2025-05-04',
  changeRate: -13.27,
  subtitle: '2024年 / 第20周 / 销售额同比去年同期下降'
})

// 获取仪表板数据
const fetchDashboardData = async () => {
  try {
    loading.value = true
    const response = await getDashboardStats()
    dashboardData.value = response.data
    
    // 更新页面数据
    if (response.data) {
      todayStats.value = {
        newCustomers: response.data.todayNewCustomers,
        basicCustomers: response.data.basicCustomers,
        premiumCustomers: response.data.premiumCustomers
      }
      
      reminders.value = response.data.reminders
      personalStats.value = response.data.personalStats
      trendStats.value = response.data.trendStats
    }
  } catch (error) {
    console.error('获取仪表板数据失败:', error)
    // 使用默认数据，在生产环境中可以显示错误提示
  } finally {
    loading.value = false
  }
}

// 页面加载时获取数据
onMounted(() => {
  // fetchDashboardData() // 取消注释以启用API调用
})
</script>

<template>
  <div class="crm-dashboard">
    <!-- 顶部导航 -->
    <van-nav-bar
      title="维享信CRM平台"
      fixed
      placeholder
      left-arrow
      @click-left="$router.back()"
    >
      <template #right>
        <van-icon name="ellipsis" size="18" />
      </template>
    </van-nav-bar>

    <div class="dashboard-content">
      <!-- 日期和统计卡片 -->
      <div class="date-stats-section">
        <div class="date-header">
          <h2 class="date-title">{{ currentDate }}</h2>
          <div class="stats-row">
            <div class="stat-item">
              <div class="stat-label">今日新增客户</div>
              <div class="stat-value">{{ todayStats.newCustomers }}</div>
            </div>
            <div class="stat-divider"></div>
            <div class="stat-item">
              <div class="stat-label">基础客户</div>
              <div class="stat-value">{{ todayStats.basicCustomers }}</div>
            </div>
            <div class="stat-divider"></div>
            <div class="stat-item">
              <div class="stat-label">高端客户</div>
              <div class="stat-value">{{ todayStats.premiumCustomers }}</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 快捷入口 -->
      <div class="quick-menu-section">
        <div class="section-header">
          <h3 class="section-title">快捷入口</h3>
          <van-icon name="ellipsis" size="16" />
        </div>
        <div class="quick-menu-grid">
          <div 
            v-for="(menu, index) in quickMenus" 
            :key="index"
            class="quick-menu-item"
          >
            <van-icon :name="menu.icon" size="24" color="#666" />
            <span class="menu-name">{{ menu.name }}</span>
          </div>
        </div>
      </div>

      <!-- 提醒 -->
      <div class="reminder-section">
        <div class="section-header">
          <h3 class="section-title">提醒</h3>
          <van-icon name="ellipsis" size="16" />
        </div>
        <div class="reminder-grid">
          <div 
            v-for="(reminder, index) in reminders" 
            :key="index"
            class="reminder-item"
          >
            <div class="reminder-type">{{ reminder.type }}</div>
            <div class="reminder-count">{{ reminder.count }}</div>
          </div>
        </div>
      </div>

      <!-- 个人关键指标 -->
      <div class="personal-stats-section">
        <div class="section-header">
          <h3 class="section-title">个人关键指标</h3>
          <van-icon name="ellipsis" size="16" />
        </div>
        <div class="stats-period">{{ personalStats.period }}</div>
        <div class="amount-display">
          <span class="currency">¥</span>
          <span class="amount">{{ personalStats.totalAmount.toLocaleString() }}</span>
        </div>
        <div class="customer-stats">
          <div class="customer-stat">
            <div class="customer-label">新增客户</div>
            <div class="customer-value">{{ personalStats.newCustomers }}个</div>
          </div>
          <div class="customer-stat">
            <div class="customer-label">高端客户</div>
            <div class="customer-value">{{ personalStats.premiumCustomers }}个</div>
          </div>
          <div class="customer-stat">
            <div class="customer-label">成交客户</div>
            <div class="customer-value">{{ personalStats.basicCustomers }}个</div>
          </div>
        </div>
      </div>

      <!-- 个人成交趋势 -->
      <div class="trend-section">
        <div class="section-header">
          <h3 class="section-title">个人成交趋势</h3>
          <van-icon name="ellipsis" size="16" />
        </div>
        <div class="trend-period">{{ trendStats.period }}</div>
        <div class="trend-change">
          <span class="change-rate negative">{{ trendStats.changeRate }}%</span>
        </div>
        <div class="trend-subtitle">{{ trendStats.subtitle }}</div>
        
        <!-- 折线图占位区域 -->
        <div class="chart-placeholder">
          <div class="chart-content">
            <div class="chart-text">折线图区域</div>
            <div class="chart-subtext">图表组件待集成</div>
          </div>
        </div>
      </div>

      <!-- 底部导航占位 -->
      <div class="bottom-nav-placeholder">
        <div class="nav-item active">
          <van-icon name="home-o" size="20" />
          <span>工作台</span>
        </div>
        <div class="nav-item">
          <van-icon name="search" size="20" />
          <span>客户搜索</span>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.crm-dashboard {
  min-height: 100vh;
  background-color: #f7f8fa;
}

.dashboard-content {
  padding: 16px;
  padding-bottom: 80px;
}

/* 日期和统计区域 */
.date-stats-section {
  background: linear-gradient(135deg, #4A90E2 0%, #357ABD 100%);
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 16px;
  color: white;
}

.date-title {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 16px;
}

.stats-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.stat-item {
  text-align: center;
  flex: 1;
}

.stat-label {
  font-size: 12px;
  opacity: 0.8;
  margin-bottom: 4px;
}

.stat-value {
  font-size: 24px;
  font-weight: 600;
}

.stat-divider {
  width: 1px;
  height: 30px;
  background-color: rgba(255, 255, 255, 0.3);
  margin: 0 16px;
}

/* 通用区域样式 */
.quick-menu-section,
.reminder-section,
.personal-stats-section,
.trend-section {
  background: white;
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 16px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

/* 快捷入口 */
.quick-menu-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 16px;
}

.quick-menu-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 12px;
  border-radius: 8px;
  background-color: #f8f9fa;
}

.menu-name {
  font-size: 12px;
  color: #666;
  margin-top: 8px;
}

/* 提醒区域 */
.reminder-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 16px;
}

.reminder-item {
  text-align: center;
  padding: 12px 8px;
}

.reminder-type {
  font-size: 12px;
  color: #666;
  margin-bottom: 8px;
}

.reminder-count {
  font-size: 20px;
  font-weight: 600;
  color: #4A90E2;
}

/* 个人关键指标 */
.stats-period {
  font-size: 12px;
  color: #999;
  margin-bottom: 12px;
}

.amount-display {
  margin-bottom: 20px;
}

.currency {
  font-size: 16px;
  color: #4A90E2;
  margin-right: 4px;
}

.amount {
  font-size: 28px;
  font-weight: 600;
  color: #4A90E2;
}

.customer-stats {
  display: flex;
  justify-content: space-between;
}

.customer-stat {
  text-align: center;
  flex: 1;
}

.customer-label {
  font-size: 12px;
  color: #666;
  margin-bottom: 4px;
}

.customer-value {
  font-size: 14px;
  font-weight: 600;
  color: #333;
}

/* 趋势区域 */
.trend-period {
  font-size: 12px;
  color: #999;
  margin-bottom: 8px;
}

.trend-change {
  margin-bottom: 8px;
}

.change-rate {
  font-size: 20px;
  font-weight: 600;
}

.change-rate.negative {
  color: #ff4444;
}

.trend-subtitle {
  font-size: 12px;
  color: #999;
  margin-bottom: 20px;
}

.chart-placeholder {
  height: 120px;
  background: linear-gradient(45deg, #f0f0f0 25%, transparent 25%), 
              linear-gradient(-45deg, #f0f0f0 25%, transparent 25%), 
              linear-gradient(45deg, transparent 75%, #f0f0f0 75%), 
              linear-gradient(-45deg, transparent 75%, #f0f0f0 75%);
  background-size: 20px 20px;
  background-position: 0 0, 0 10px, 10px -10px, -10px 0px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px dashed #ddd;
}

.chart-content {
  text-align: center;
  background: white;
  padding: 16px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.chart-text {
  font-size: 14px;
  color: #666;
  margin-bottom: 4px;
}

.chart-subtext {
  font-size: 12px;
  color: #999;
}

/* 底部导航 */
.bottom-nav-placeholder {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 60px;
  background: white;
  border-top: 1px solid #eee;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 60px;
}

.nav-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  color: #999;
  font-size: 12px;
}

.nav-item.active {
  color: #4A90E2;
}
</style> 
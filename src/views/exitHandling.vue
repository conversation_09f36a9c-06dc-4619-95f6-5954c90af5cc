<template>
    <div class="exit-handling-container">
        <!-- 导航栏 -->
        <van-nav-bar title="出场办理" left-arrow fixed placeholder @click-left="goBack" />
        <!-- 基本信息 -->
        <div class="basic-info-section">
            <div class="basic-info-card">
                <div class="info-content">
                    <div class="info-row">
                        <span class="info-label">合同编号：</span>
                        <span class="info-value">{{ contractInfo.contractNo }}</span>
                    </div>
                    <div class="info-row">
                        <span class="info-label">承租方：</span>
                        <span class="info-value">{{ contractInfo.customerName }}</span>
                    </div>
                    <div class="info-row">
                        <span class="info-label">租期：</span>
                        <span class="info-value">{{ contractInfo.startDate }} - {{ contractInfo.endDate }}</span>
                    </div>
                    <div class="info-row">
                        <span class="info-label">退租日期：</span>
                        <span class="info-value">{{ contractInfo.terminateDate }}</span>
                    </div>
                </div>
            </div>
        </div>
        <div class="step-content" v-if="exitMode === 'property-only'">
            <div class="step-item" :class="{ 'step-item-active': exitSep === 1 }" @click="handleStepClick(1)">
                <img class="step-item-icon" src="../assets/images/step1.png" alt="">
                <div class="step-item-title">物业交割</div>
            </div>
            <div class="step-item step-item-line" :class="{ 'step-item-active': exitSep === 2 }">
                <span class="step-item-line-dot" v-for="i in 14"></span>
            </div>
            <div class="step-item" :class="{ 'step-item-active': exitSep === 2 }">
                <img class="step-item-icon" src="../assets/images/step2.png" alt="" v-if="exitSep !== 2">
                <img class="step-item-icon" src="../assets/images/step2on.png" alt="" v-else>
                <div class="step-item-title">费用结算</div>
            </div>
        </div>
        <div class="step-content step-content-center" v-if="exitMode === 'property-and-settlement'">
            <div class="step-item step-item-active">
                <img class="step-item-icon" src="../assets/images/step2on.png" alt="">
                <div class="step-item-title">物业交割并结算</div>
            </div>
        </div>
        <wyjg ref="wyjgRef" :exitData="exitData" :exitMode="exitMode" :exitId="exitId" :exitSep="exitSep"
            v-if="exitMode === 'property-and-settlement' || exitSep === 1" @changeExitMode="handleChangeExitMode"
            @next="handleNext" @cancel="handleCancel" />
        <fyjs ref="fyjsRef" :exitData="exitData" :exitMode="exitMode"
            v-if="exitMode === 'property-and-settlement' || exitSep === 2" @save="handleSave" @cancel="handleCancel"/>
        <!-- 选择签字模式弹框 -->
        <!-- signType
integer <int32>
签字方式: 1-线上, 2-线下 -->

        <van-popup v-model:show="showSignTypePopup" position="center" :close-on-click-overlay="true" round
            class="qr-code-popup">
            <div class="qr-code-content">
                <!-- <div class="qr-code-header">
                    <h3 class="qr-code-title">选择签字模式</h3>
                    <van-icon name="cross" @click="closeSignTypePopup" class="close-icon" />
                </div> -->


                <div class="qr-code-section">

                    <van-uploader v-model="signTypeFileList" :max-count="1"  :after-read="(file: any) => onSignTypeUpload(file)" >
                        <van-button type="primary" size="large">上传签字单</van-button>
                    </van-uploader>
                </div>
            </div>
        </van-popup>
    </div>
</template>
<script lang="ts">
export default {
    name: 'ExitHandling'
}
</script>
<script setup lang="ts">
import { ref, onMounted, nextTick } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import wyjg from '@/components/wyjg.vue'
import fyjs from '@/components/fyjs.vue'
import { getExitDetail, chooseExitProcessType, saveExitSettlement, uploadImage, uploadSignature } from '../api/exit'
import { showToast } from 'vant';

const router = useRouter()
const route = useRoute()
const exitId = route.params.id as string
const exitData = ref({})
const contractInfo = ref<any>({})

const wyjgRef = ref<any>(null)
const fyjsRef = ref<any>(null)
const exitMode = ref('')
let exitSep = ref(1)
let showSignTypePopup = ref(false)
let signTypeFileList = ref([])


const getExitData = async () => {
    const res = await getExitDetail(exitId)
    let data = res.data
    if (!!data.contractTerminateInfo && data.contractTerminateInfo.contract) {
        contractInfo.value = data.contractTerminateInfo.contract
        contractInfo.value.terminateDate = data.contractTerminateInfo.terminateDate
    }
    exitMode.value = data.exitInfo.processType === 1 ? 'property-only' : 'property-and-settlement'
    // contractTerminateInfo
    exitData.value = data
    nextTick(() => {
        if (exitMode.value === 'property-and-settlement') {
            wyjgRef.value.open(data)
            fyjsRef.value.open(data)
        } else {
            if (exitSep.value === 1) {
                wyjgRef.value.open(data)
            } else {
                fyjsRef.value.open(data)
            }
        }
    })
}


const goBack = () => {
    router.back()
}
const handleStepClick = (step: number) => {
    exitSep.value = step
    getExitData()
}
const handleChangeExitMode = async (mode: string) => {
    // exitMode.value = mode
    // exitSep.value = 1
    await chooseExitProcessType({
        exitId: exitId,
        processType: 2
    })
    getExitData()
}

const handleNext = () => {
    exitSep.value++
    getExitData()
}

const handleSave = async (data: any) => {
    // saveExitSettlement
    // let roomList = wyjgRef.value.getRoomList()
    // console.log('roomList', roomList)
    // data.exitRoomList = roomList
    let res = await saveExitSettlement(data)
    if (res.code === 200) {
        showToast('保存成功')
        // getExitData()
        sessionStorage.setItem('exitTab', '1')
        if (data.isSubmit && data.signType === 2) {
            showSignTypePopup.value = true
        } else {
            router.back()
        }
    }
}

const closeSignTypePopup = () => {
    showSignTypePopup.value = false
}

const onSignTypeUpload = async (file: any) => {
    console.log('onSignTypeUpload', file)
    let res = await uploadImage(file.file)  
    if (res.code === 200) {
        // showToast('上传成功')
        let res2 = await uploadSignature({
            exitId: exitId,
            signatureFiles: JSON.stringify([{fileUrl: res.data.fileUrl, fileName: res.data.fileName}])
        })
        if (res2.code === 200) {
            showToast('上传成功')
            router.back()
        } else {
            // showToast('上传失败')
        }
    }
}

const handleCancel = () => {
    router.back()
}


onMounted(() => {
    getExitData()
})


</script>
<style scoped lang="less">
.qr-code-popup {
    .qr-code-content {
        padding: 20px;
        background: #fff;
        border-radius: 12px;
        box-shadow: 0 4px 16px rgba(53, 131, 255, 0.3);
        .qr-code-section {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
    }
}
.exit-handling-container {

    .step-content {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 32px 120px;
        background: #fff;

        .step-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;

            .step-item-icon {
                width: 100px;
                height: 100px;
                border-radius: 100px;
            }

            .step-item-title {
                font-size: 30px;
                color: #333;
                margin-top: 10px;
                font-weight: 500;
            }
        }
        .step-item-line {
            width: 0;
            flex: 1;
            // width: 200px;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-direction: row;
            // flex: 1;
            gap: 4px;
            margin-top: -60px;
            .step-item-line-dot {
                width: 8px;
                height: 8px;
                border-radius: 10px;
                background: #f0f0f0;

            }
        }

        .step-item-active {
            .step-item-title {
                color: #3583FF;
            }

            .step-item-icon {
                background: linear-gradient(135deg, #3583FF 0%, #1677FF 100%);
            }
        }
    }

    .step-content-center {
        justify-content: center;
    }

    /* 内容区域 */
    .content-area {
        padding-bottom: 20px;
    }

    /* 基本信息区域 */
    .basic-info-section {
        // margin: 16px;
        overflow: hidden;
        background: url('../assets/images/entrance-notice-bg.png') no-repeat center center;
        background-size: cover;
        box-sizing: border-box;
        padding: 16px;
    }

    .basic-info-card {
        background: linear-gradient(135deg, #3583FF 0%, #1677FF 100%);
        /* 蓝色渐变背景 */
        /* 预留背景图片位置
    background-image: url(''); 
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    */
        border-radius: 12px;
        padding: 30px 20px;
        color: #FFFFFF;
        /* 白色文字 */
        position: relative;
        overflow: hidden;
        box-shadow: 0 4px 16px rgba(53, 131, 255, 0.3);
    }

    .info-content {
        position: relative;
        z-index: 2;
    }

    .info-row {
        display: flex;
        align-items: center;
        margin-bottom: 16px;
        font-size: 28px;
        line-height: 1.4;
    }

    .info-row:last-child {
        margin-bottom: 0;
    }

    .info-label {
        font-weight: 400;
        color: rgba(255, 255, 255, 0.9);
        min-width: 120px;
    }

    .info-value {
        font-weight: 500;
        color: #FFFFFF;
        flex: 1;
    }

    .title-icon {
        width: 32px;
        height: 32px;
    }

    .section-title {
        font-size: 32px;
        font-weight: 600;
        color: #242433;
        padding: 20px 30px;
        border-bottom: 1px solid #E8EBFF;
        display: flex;
        align-items: center;
        gap: 10px;
        background-color: #FFFFFF;
        border-radius: 12px 12px 0 0;
    }

}
</style>
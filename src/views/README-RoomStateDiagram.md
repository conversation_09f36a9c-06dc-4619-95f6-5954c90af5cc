# 房态图页面功能说明

## 页面概述

房态图页面（roomStateDiagram.vue）是万洋资管平台的核心功能之一，用于直观展示项目中各楼层房间的实时状态，支持多维度筛选和详细信息查看。

## 主要功能

### 1. 房态统计展示
- **总数统计**：显示房间总数
- **空置统计**：显示空置房间数量
- **在租统计**：显示在租房间数量  
- **待生效统计**：显示待生效/签约中/已预定房间数量
- **不可招商统计**：显示不可招商房间数量

### 2. 多维度筛选功能
- **基础筛选**：地块、楼栋、楼层三级联动筛选
- **更多筛选**：
  - 查看日期：支持选择特定日期的房态
  - 用途筛选：按物业类型筛选（写字楼、商铺、宿舍等）
  - 房态筛选：按房间状态筛选（空置、在租、待生效、不可招商）
  - 特殊标识：即将到期、自用、锁房、脏房、维修

### 3. 房态图可视化
- **楼层布局**：按楼层分组展示房间
- **状态颜色**：
  - 🟢 绿色：空置
  - 🔴 红色：在租
  - 🟠 橙色：待生效/签约中/已预定
  - ⚫ 灰色：不可招商
- **特殊标识**：右上角三角形标识特殊状态
  - 🔵 蓝色：自用
  - 🔵 浅蓝：未进场
  - 🟣 紫色：未出场

### 4. 房间详情查看
点击房间可查看详细信息：
- **基本信息**：房间状态、物业类型、计租面积、表价、空置天数
- **合同信息**：合同编号、承租人、合同租金、合同期间
- **订单信息**：客户名称、公司名称、预定金额
- **房源标识**：各种状态标签

## 技术实现

### 1. 接口集成
```typescript
// 房态简图接口
export const getRoomDiagram = (params: RoomDiagramQueryDTO) => {
    return http.post<DiagramVo>('/room/simple/diagram', params)
}

// 房间树查询接口
export const getRoomTree = (params: RoomTreeQueryDTO) => {
    return http.post<RoomOption[]>('/room/roomOptions', params)
}
```

### 2. 数据结构
```typescript
// 房态图查询参数
interface RoomDiagramQueryDTO {
    projectId?: string
    parcelId?: string
    buildingId?: string
    floorId?: string
    roomStatus?: number
    propertyType?: string
    diagramDate?: string
    dueSoon?: boolean
    isSelfUse?: boolean
    isLock?: boolean
    isDirty?: boolean
    isMaintain?: boolean
}

// 房态图响应数据
interface DiagramVo {
    totalCount: number
    emptyCount: number
    rentCount: number
    toEffectCount: number
    invalidCount: number
    floorDiagramList: FloorDiagramVo[]
    propertyList: string[]
}

// 房间房态信息
interface RoomDiagramVo {
    roomId: string
    roomName: string
    roomStatus: number
    roomStatusName: string
    propertyType: string
    propertyTypeName: string
    rentArea: number
    tablePrice: number
    tags: string[]
    isSelfUse: boolean
    needCheckIn: boolean
    needCheckOut: boolean
    isDirty: boolean
    isLock: boolean
    isMaintain: boolean
    contractVo: ContractDiagramVo | null
    bookingVo: BookDiagramVo | null
}
```

### 3. 核心方法

#### 初始化页面
```typescript
const initPage = async () => {
    // 获取用户默认项目
    if (!currentProjectId.value) {
        const projects = await getUserProjects();
        if (projects.data && projects.data.length > 0) {
            currentProjectId.value = projects.data[0].id;
        }
    }
    
    // 并行加载房间树和房态图数据
    await Promise.all([
        loadRoomTree(),
        loadRoomDiagram()
    ]);
}
```

#### 构建筛选选项
```typescript
const buildFilterOptions = () => {
    // 遍历房间树，提取地块、楼栋、楼层选项
    const traverseTree = (nodes: RoomOption[]) => {
        nodes.forEach(node => {
            if (node.level === 2 && node.parcelName) { // 地块
                parcels.add(node.parcelName);
            } else if (node.level === 3 && node.buildingName) { // 楼栋
                buildings.add(node.buildingName);
            } else if (node.level === 4) { // 房源
                if (node.floorName) floors.add(node.floorName);
                if (node.propertyType) propertyTypes.add(node.propertyType);
            }
        });
    };
}
```

#### 获取房间状态样式
```typescript
const getRoomStatusClass = (room: RoomDiagramVo): string => {
    switch (room.roomStatus) {
        case 1: return 'vacant';      // 空置
        case 2: return 'rented';      // 在租
        case 3: return 'pending';     // 待生效/签约中/已预定
        case 4: return 'unavailable'; // 不可招商
        default: return 'vacant';
    }
};
```

### 4. 样式设计
- **响应式布局**：支持移动端和PC端
- **网格展示**：每行6个房间，自适应排列
- **状态指示**：颜色编码 + 图例说明
- **交互效果**：hover缩放、点击反馈
- **弹框设计**：筛选弹框、详情弹框

## 页面路由

```typescript
{
    path: '/room-state-diagram',
    name: 'RoomStateDiagram',
    component: () => import('../views/roomStateDiagram.vue'),
    meta: {
        title: '房态图'
    }
}
```

### 路由参数
- `projectId`：可选，指定项目ID，如未提供则使用用户默认项目

### 使用示例
```typescript
// 跳转到房态图页面
router.push({ name: 'RoomStateDiagram' })

// 指定项目ID跳转
router.push({ 
    name: 'RoomStateDiagram', 
    query: { projectId: 'project123' } 
})
```

## 用户体验优化

### 1. 加载状态
- 页面初始化加载提示
- 筛选条件变化时的状态更新
- 接口请求失败的错误提示

### 2. 交互优化
- 三级联动筛选（地块-楼栋-楼层）
- 筛选条件实时生效
- 房间点击查看详情
- 筛选条件重置功能

### 3. 性能优化
- 并行加载数据
- 条件变化防抖处理
- 大数据量时的虚拟滚动（后续优化）

## 扩展功能

### 1. 导出功能
- 房态图截图导出
- 房间状态数据导出Excel

### 2. 实时更新
- WebSocket接入实时房态变化
- 自动刷新机制

### 3. 多项目支持
- 项目切换器
- 多项目对比视图

### 4. 历史回溯
- 历史房态查看
- 房态变化趋势分析

## 注意事项

1. **内存说明**：使用Vant组件时，所有px值需要乘以2（参考项目内存设置）
2. **接口适配**：部分接口可能只支持单一筛选条件，需要根据实际API调整
3. **数据权限**：房态数据基于用户权限，仅显示有权限的项目和房源
4. **性能考虑**：大量房源时建议分页加载或虚拟滚动

## API接口说明

### 1. 房态简图接口
- **接口地址**：`POST /room/simple/diagram`
- **请求参数**：`RoomDiagramQueryDTO`
- **响应数据**：`DiagramVo`

### 2. 房间树查询接口
- **接口地址**：`POST /room/roomOptions`
- **请求参数**：`RoomTreeQueryDTO`
- **响应数据**：`RoomOption[]`

### 3. 用户项目列表接口
- **接口地址**：`GET /business-rent-rest/home/<USER>
- **响应数据**：`ProjectInfo[]`

## 相关页面

- **首页**（`Home.vue`）：可从首页房态统计跳转
- **进场管理**（`EntryManagement.vue`）：查看待进场房源
- **出场管理**（`ExitManagement.vue`）：查看待出场房源
- **定单管理**（`BookingList.vue`）：查看预定房源 
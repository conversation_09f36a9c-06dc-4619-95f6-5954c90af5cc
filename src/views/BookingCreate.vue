<template>
    <div class="booking-create">
        <!-- 顶部导航 -->
        <van-nav-bar :title="isEditMode ? '编辑定单' : '新增定单'" left-arrow @click-left="$router.go(-1)" class="custom-nav-bar" />
        <!-- 项目选择 -->
        <van-field v-model="formData.projectName" required readonly name="picker" label="项目名称" placeholder="请选择"
            input-align="right" >
            <template #button>
                <!-- <i class="van-icon van-icon-arrow-down"></i> -->
            </template>
        </van-field>
        <van-popup v-model:show="showProjectPicker" destroy-on-close position="bottom">
            <van-picker :columns="projectColumns" :model-value="projectValue" @confirm="onProjectConfirm"
                @cancel="showProjectPicker = false" title="选择项目" :loading="projectLoading" />
        </van-popup>

        <!-- 甲方公司 -->
        <!-- <van-field v-model="formData.company" required readonly name="company" label="甲方公司" placeholder="请选择" input-align="right"
			@click="showCompanyPicker = true">
			<template #button>
				<i class="van-icon van-icon-arrow-down"></i>
			</template>
		</van-field>
		<van-popup v-model:show="showCompanyPicker" destroy-on-close position="bottom">
			<van-picker :columns="companyColumns" :model-value="companyValue" @confirm="onCompanyConfirm"
				@cancel="showCompanyPicker = false" />
		</van-popup> -->

        <!-- 客户名称 -->
        <van-field v-model="formData.customerName" required label="客户名称" placeholder="请输入" input-align="right"
            type="text" />

        <!-- 意向业态 -->
        <van-field v-model="propertyTypeDisplayName" :required="!formData.isUncertainRoom" readonly name="businessType"
            label="意向业态" :placeholder="formData.isUncertainRoom ? '暂不确认房源时无需选择' : '请选择'" input-align="right"
            :disabled="formData.isUncertainRoom"
            @click="formData.isUncertainRoom ? null : (showBusinessTypePicker = true)">
            <template #button>
                <i class="van-icon van-icon-arrow-down" v-if="!formData.isUncertainRoom"></i>
            </template>
        </van-field>
        <van-popup v-model:show="showBusinessTypePicker" destroy-on-close position="bottom">
            <van-picker :columns="businessTypeColumns" v-model="businessTypeValue" @confirm="onBusinessTypeConfirm"
                @cancel="showBusinessTypePicker = false" @change="onBusinessTypeChange" title="选择意向业态"
                :loading="propertyTypeLoading" />
        </van-popup>

        <!-- 意向房源 -->
        <van-field v-model="formData.roomName" :required="!formData.isUncertainRoom" readonly name="roomName"
            label="意向房源" :placeholder="formData.isUncertainRoom ? '已勾选暂不确认房源' : (formData.projectId ? '请选择' : '请先选择项目')"
            input-align="right" :disabled="!formData.projectId || formData.isUncertainRoom"
            @click="handleRoomFieldClick">
            <template #button>
                <i class="van-icon van-icon-arrow-down" v-if="formData.projectId && !formData.isUncertainRoom"></i>
            </template>
        </van-field>
        <van-popup v-model:show="showRoomPicker" destroy-on-close position="bottom">
            <van-picker :columns="roomColumns" :model-value="roomValue" @confirm="onRoomConfirm"
                @cancel="showRoomPicker = false" title="选择意向房源" :loading="roomLoading" />
        </van-popup>

        <!-- 暂不确认房源 -->
        <div class="uncertain-room">
            <div class="uncertain-room-content">
                <van-checkbox class="uncertain-room-checkbox" v-model="formData.isUncertainRoom" shape="square"
                    @change="handleUncertainRoomChange">
                    <div class="uncertain-room-title">暂不确认房源</div>
                </van-checkbox>
            </div>
        </div>

        <!-- 定金金额 -->
        <van-field v-model="formData.bookingAmount" required type="number" label="定金金额" placeholder="请输入"
            input-align="right" label-width="140px">
            <template #button>
                <span class="unit">元</span>
            </template>
        </van-field>

        <!-- 应收日期 -->
        <van-field v-model="formData.receivableDate" required readonly name="dueDate" label="应收日期" placeholder="请选择"
            input-align="right" @click="openDueDatePicker">
            <template #button>
                <i class="van-icon van-icon-arrow-down"></i>
            </template>
        </van-field>
        <van-popup v-model:show="showDueDatePicker" destroy-on-close position="bottom">
            <van-date-picker :model-value="dueDateArray" title="选择日期" @confirm="onDueDateConfirm"
                @cancel="showDueDatePicker = false" />
        </van-popup>

        <!-- 预计签约日期 -->
        <van-field v-model="formData.expectSignDate" readonly name="expectedSignDate" label="预计签约日期" placeholder="请选择"
            input-align="right" @click="openSignDatePicker">
            <template #button>
                <i class="van-icon van-icon-arrow-down"></i>
            </template>
        </van-field>
        <van-popup v-model:show="showSignDatePicker" destroy-on-close position="bottom">
            <van-date-picker :model-value="signDateArray" title="选择日期" @confirm="onSignDateConfirm"
                @cancel="showSignDatePicker = false" />
        </van-popup>

        <!-- 是否退定金 -->
        <van-field v-model="formData.isRefundable" readonly name="isRefundable" label="是否退定金" placeholder="请选择"
            input-align="right" :disabled="!isRefundableEditable"
            @click="isRefundableEditable ? (showRefundPicker = true) : null">
            <template #button>
                <i class="van-icon van-icon-arrow-down" v-if="isRefundableEditable"></i>
                <van-tag v-else color="#666" style="margin-left: 8px; font-size: 12px;">
                    {{ formData.isUncertainRoom ? '暂不确认房源默认可退' : '选择房源后默认不可退' }}
                </van-tag>
            </template>
        </van-field>
        <van-popup v-model:show="showRefundPicker" destroy-on-close position="bottom">
            <van-picker :columns="refundColumns" :model-value="refundValue" @confirm="onRefundConfirm"
                @cancel="showRefundPicker = false" />
        </van-popup>

        <!-- 收款码按钮区域 - 只在提交后显示 -->
        <div class="qrcode-section" v-if="isSubmitted && formData.status === 1">
            <div class="qrcode-title">定单收款账单码</div>
            <div class="qrcode-button-container">
                <van-button type="primary" size="large" @click="showQRCodeDialog">
                    查看收款码
                </van-button>
            </div>
        </div>

        <!-- 收款码弹框 -->
        <van-popup v-model:show="showQRCodePopup" position="center" :style="{ width: '90%', maxWidth: '400px' }"
            closeable close-on-click-overlay>
            <div class="qrcode-popup-content">
                <div class="qrcode-popup-title">定单收款码</div>

                <!-- 定单信息 -->
                <div class="order-info">
                    <div class="info-item">
                        <span class="label">项目名称：</span>
                        <span class="value">{{ formData.projectName }}</span>
                    </div>
                    <div class="info-item">
                        <span class="label">意向房源：</span>
                        <span class="value">{{ formData.isUncertainRoom ? '暂不确认房源' : formData.roomName }}</span>
                    </div>
                    <div class="info-item">
                        <span class="label">客户名称：</span>
                        <span class="value">{{ formData.customerName }}</span>
                    </div>
                    <div class="info-item">
                        <span class="label">支付金额：</span>
                        <span class="value amount">¥{{ formatAmount(formData.bookingAmount) }}</span>
                    </div>
                </div>

                <!-- 二维码显示 -->
                <div class="qrcode-display">
                    <QRCode :value="paymentUrl" :size="200" :show-placeholder="true" :show-download="!!paymentUrl"
                        placeholder-text="收款码已自动生成" @generated="handleQRCodeGenerated" @error="handleQRCodeError" />
                </div>

                <!-- 重新生成按钮 -->
                <div class="qrcode-actions">
                    <van-button type="default" size="small" @click="generateQRCode">
                        重新生成收款码
                    </van-button>
                </div>
            </div>
        </van-popup>

        <!-- 底部按钮 -->
        <div class="bottom-buttons">
            <van-button class="save-btn" :disabled="isSaved || isSubmitted" :loading="saveLoading" @click="handleSave">
                {{ isSaved ? '已保存' : '保存' }}
            </van-button>
            <van-button type="primary" class="submit-btn" :disabled="isSubmitted" :loading="submitLoading"
                @click="handleSubmit">
                {{ isSubmitted ? '已提交' : '提交' }}
            </van-button>
        </div>
    </div>
</template>
<script lang="ts">
export default {
  name: 'BookingCreate'
}
</script>
<script setup lang="ts">
import { ref, reactive, onMounted, computed, onActivated, onDeactivated, onUnmounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { showToast, showLoadingToast } from 'vant'
import { useKeepAliveTest } from '../utils/keep-alive-test'
import { useKeepAliveDebug } from '../utils/keep-alive-debug'
import QRCode from '@/components/QRCode.vue'
import { saveBooking, payBooking, getBookingDetail } from '@/api/booking'
import { getOrgTree, type SysOrgTreeVo } from '@/api/organization'
import { getRoomOptions, extractRoomsFromTree, type RoomOption, type RoomOptionsParams } from '../api/room'
import {
    getBusinessTypeName,
    getFirstLevelOptions,
    getChildrenByParentCode,
    type DictOption
} from '../api/dict'

const router = useRouter()
const route = useRoute()

// 编辑模式相关
const isEditMode = ref(false) // 是否为编辑模式
const editId = ref('') // 编辑的定单ID

// 二维码相关
const paymentUrl = ref('')

// 按钮状态管理
const isSaved = ref(false) // 是否已保存
const isSubmitted = ref(false) // 是否已提交
const saveLoading = ref(false) // 保存按钮加载状态
const submitLoading = ref(false) // 提交按钮加载状态

// 收款码弹框状态
const showQRCodePopup = ref(false) // 收款码弹框显示状态

// 表单数据
const formData = reactive({
    id: '',
    projectId: '',
    projectName: '',
    merchantId: '',
    company: '',
    customerName: '',
    propertyType: '', // 存储业态编码
    propertyTypeCode: '', // 业态编码
    propertyTypeName: '', // 业态名称
    roomId: '',
    roomName: '', // 房源名称 - 根据接口文档使用roomName
    isUncertainRoom: false, // 暂不确认房源
    bookingAmount: '',
    receivableDate: '',
    expectSignDate: '',
    isRefundable: '否', // 默认不可退，根据房源选择状态动态调整
    isSubmit: 0,
    status: 0 // 定单状态: 0-草稿,1-待收费,2-已生效,3-已转签,4-已作废
})

// 项目选择
const showProjectPicker = ref(false)
const projectValue = ref([])
const projectColumns = ref<Array<{ text: string, value: string }>>([])
const projectList = ref<SysOrgTreeVo[]>([])
const projectLoading = ref(false)

// 获取项目列表
const getProjectList = async () => {
    try {
        projectLoading.value = true
        const loadingToast = showLoadingToast('加载项目列表...')

        const res = await getOrgTree({
            // 可以根据需要添加查询参数
            relProjectFlag: 2, // 已关联项目
        })

        if (res.code === 200 && Array.isArray(res.data)) {
            projectList.value = res.data
            // 递归提取所有项目（level: 4 表示项目级别）
            const extractProjects = (nodes: SysOrgTreeVo[]): Array<{ text: string, value: string }> => {
                let projects: Array<{ text: string, value: string }> = []

                nodes.forEach(node => {
                    // 根据实际数据结构，level: 4 表示项目级别
                    if (node.level === 4) {
                        projects.push({
                            text: node.name || node.fullName,
                            value: node.id
                        })
                    }

                    // 递归处理子节点
                    if (node.children && node.children.length > 0) {
                        projects = projects.concat(extractProjects(node.children))
                    }
                })

                return projects
            }
            projectColumns.value = extractProjects(res.data)
            if (projectColumns.value.length === 0) {
                showToast('暂无可选项目')
            }
        } else {
            showToast('获取项目列表为空')
        }

        loadingToast.close()
    } catch (error) {
        console.error('获取项目列表失败:', error)
        showToast('获取项目列表失败，请重试')
    } finally {
        projectLoading.value = false
    }
}

const onProjectConfirm = (value: any) => {
    const oldProjectId = formData.projectId
    const newProjectId = value.selectedValues[0]

    formData.projectId = newProjectId
    formData.projectName = value.selectedOptions[0].text
    showProjectPicker.value = false

    // 项目变化时，清空房源选择并重新获取房源列表
    if (oldProjectId !== newProjectId) {
        formData.roomId = ''
        formData.roomName = ''

        // 根据当前选择的业态类型获取房源
        const currentBuildingType = formData.propertyTypeCode
        getRoomList(newProjectId, currentBuildingType)
    }
}

// 公司选择
const showCompanyPicker = ref(false)
const companyValue = ref([])
const companyColumns = ref([
    { text: '公司1', value: '1' },
    { text: '公司2', value: '2' },
])
const onCompanyConfirm = (value: any) => {
    formData.merchantId = value.selectedValues[0]
    formData.company = value.selectedOptions[0].text
    showCompanyPicker.value = false
}

// 固定的物业类型数据
const propertyTypeOptions = ref<DictOption[]>([
    {
        code: '10',
        name: '宿舍',
        sort: 1,
        children: [
            // {
            // 	code: '11',
            // 	name: '标准宿舍',
            // 	sort: 1
            // }
        ]
    },
    {
        code: '20',
        name: '厂房',
        sort: 5,
        children: [
            // {
            // 	code: '21',
            // 	name: '标准厂房',
            // 	sort: 1
            // }
        ]
    },
    {
        code: '30',
        name: '商业',
        sort: 10,
        children: [
            {
                code: '31',
                name: '商铺',
                sort: 10
            },
            {
                code: '32',
                name: '综合体',
                sort: 15
            },
            {
                code: '33',
                name: '中央食堂',
                sort: 20
            }
        ]
    },
    {
        code: '40',
        name: '车位',
        sort: 25
    },
    {
        code: '50',
        name: '办公',
        sort: 30
    }
])
const propertyTypeLoading = ref(false)

// 业态选择
const showBusinessTypePicker = ref(false)
const businessTypeValue = ref<number[]>([0, 0])
const businessTypeColumns = ref<Array<Array<{ text: string, value: string }>>>([])

// 计算属性：显示的业态名称
const propertyTypeDisplayName = computed(() => {
    return getBusinessTypeName(formData.propertyTypeCode, propertyTypeOptions.value)
})

// 计算属性：是否可退字段是否可编辑
// 暂不确认房源时可编辑，选择了具体房源时不可编辑
const isRefundableEditable = computed(() => {
    return formData.isUncertainRoom || !formData.roomId
})

// 初始化业态选择器数据
const initBusinessTypeColumns = () => {
    // 一级分类
    const firstLevelOptions = getFirstLevelOptions(propertyTypeOptions.value)
    const firstColumn = firstLevelOptions.map(option => ({
        text: option.name,
        value: option.code
    }))

    // 默认显示第一个一级分类对应的二级分类
    // 如果第一个分类没有children，则显示空的第二列
    const firstParentCode = firstLevelOptions[0]?.code || ''
    const secondLevelOptions = getChildrenByParentCode(firstParentCode, propertyTypeOptions.value)
    const secondColumn = secondLevelOptions.length > 0
        ? secondLevelOptions.map(option => ({
            text: option.name,
            value: option.code
        }))
        : [{ text: '请选择', value: '' }] // 如果没有子分类，显示占位符

    businessTypeColumns.value = [firstColumn, secondColumn]
}

// 业态选择确认
const onBusinessTypeConfirm = (value: any) => {
    const selectedValues = value.selectedValues
    const selectedOptions = value.selectedOptions
    const oldBusinessType = formData.propertyTypeCode
    let newBusinessType = ''

    if (selectedValues.length >= 2 && selectedValues[1] && selectedOptions[1].value !== '') {
        // 二级选择 - 有有效的二级分类
        newBusinessType = selectedValues[1]
        formData.propertyTypeCode = selectedValues[1] // 使用二级编码
        formData.propertyTypeName = selectedOptions[1].text
        formData.propertyType = selectedValues[1] // 向后兼容
    } else if (selectedValues.length >= 1) {
        // 仅选择一级或者二级为空
        newBusinessType = selectedValues[0]
        formData.propertyTypeCode = selectedValues[0]
        formData.propertyTypeName = selectedOptions[0].text
        formData.propertyType = selectedValues[0]
    }

    showBusinessTypePicker.value = false

    // 业态类型变化时，清空房源选择并重新获取房源列表
    if (oldBusinessType !== newBusinessType && formData.projectId) {
        formData.roomId = ''
        formData.roomName = ''
        getRoomList(formData.projectId, newBusinessType)
    }
}

// 监听一级分类变化，更新二级分类
const onBusinessTypeChange = (selectedValues: any, selectedIndex: number) => {
    if (selectedIndex === 0) {
        // 一级分类变化，更新二级分类
        const firstLevelIndex = selectedValues[0]
        const firstLevelOptions = getFirstLevelOptions(propertyTypeOptions.value)
        const selectedOption = firstLevelOptions[firstLevelIndex]

        if (selectedOption) {
            const secondLevelOptions = getChildrenByParentCode(selectedOption.code, propertyTypeOptions.value)

            // 根据是否有子分类来决定第二列的内容
            const secondColumn = secondLevelOptions.length > 0
                ? secondLevelOptions.map(option => ({
                    text: option.name,
                    value: option.code
                }))
                : [{ text: '请选择', value: '' }] // 如果没有子分类，显示占位符

            // 更新第二列数据
            businessTypeColumns.value = [
                businessTypeColumns.value[0], // 保持第一列不变
                secondColumn
            ]

            // 重置第二列选择到第一项
            businessTypeValue.value = [firstLevelIndex, 0]
        }
    }
}

// 初始化物业类型数据
const initPropertyTypeData = () => {
    // 使用固定数据，直接初始化选择器
    initBusinessTypeColumns()
}

// 房源选择
const showRoomPicker = ref(false)
const roomValue = ref([])
const roomColumns = ref<Array<{ text: string, value: string }>>([])
const roomList = ref<RoomOption[]>([])
const roomLoading = ref(false)

// 获取房源列表
const getRoomList = async (projectId: string, buildingType?: string) => {
    if (!projectId) {
        roomColumns.value = []
        roomList.value = []
        return
    }

    try {
        roomLoading.value = true
        const loadingToast = showLoadingToast('加载房源列表...')

        const params: RoomOptionsParams = {
            projectId: projectId,
            pageSize: 1000 // 获取所有房源
        }

        // 如果有业态类型，添加到查询参数
        if (buildingType) {
            params.buildingType = buildingType
            params.businessType = buildingType // 兼容性保留
        }

        const res = await getRoomOptions(params)

        if (res.code === 200 && Array.isArray(res.data)) {
            // 从树形结构中提取实际房源（level: 4）
            const extractedRooms = extractRoomsFromTree(res.data)
            roomList.value = extractedRooms
            roomColumns.value = extractedRooms.map((room: RoomOption) => ({
                text: `${room.parcelName || ''}-${room.buildingName || ''}-${room.roomName || ''}`.replace(/^-+/, '').replace(/-+$/, '') || room.roomId || '',
                value: room.roomId || ''
            }))

            if (roomColumns.value.length === 0) {
                showToast('暂无可选房源')
            }
        } else {
            roomColumns.value = []
            roomList.value = []
            showToast('获取房源列表为空')
        }

        loadingToast.close()
    } catch (error) {
        console.error('获取房源列表失败:', error)
        showToast('获取房源列表失败，请重试')
        roomColumns.value = []
        roomList.value = []
    } finally {
        roomLoading.value = false
    }
}

// 处理房源字段点击
const handleRoomFieldClick = () => {
    if (!formData.projectId) {
        showToast('请先选择项目')
        return
    }

    // 如果勾选了暂不确认房源，不允许选择房源
    if (formData.isUncertainRoom) {
        showToast('已勾选暂不确认房源，无需选择具体房源')
        return
    }

    // 如果还没有加载房源数据，先加载
    if (roomColumns.value.length === 0 && !roomLoading.value) {
        getRoomList(formData.projectId, formData.propertyTypeCode)
    }

    showRoomPicker.value = true
}

// 处理暂不确认房源变化
const handleUncertainRoomChange = (checked: boolean) => {
    console.log('暂不确认房源状态变化:', checked)

    if (checked) {
        // 勾选"暂不确认房源"时
        formData.isRefundable = '是' // 暂不确认房源默认可退

        // 清空房源选择
        formData.roomId = ''
        formData.roomName = ''

        // 清空业态选择（根据参考项目逻辑，暂不确认房源时无需选择业态）
        formData.propertyTypeCode = ''
        formData.propertyTypeName = ''
        formData.propertyType = ''
        businessTypeValue.value = [0, 0]

        showToast('已勾选暂不确认房源，默认可退定金')
    } else {
        // 取消勾选"暂不确认房源"时
        formData.isRefundable = '否' // 默认不可退

        // 重新初始化业态选择器
        initBusinessTypeColumns()

        showToast('已取消暂不确认房源，请重新选择业态和房源')
    }
}

const onRoomConfirm = (value: any) => {
    const selectedRoomId = value.selectedValues[0]
    const selectedRoom = roomList.value.find(room => room.roomId === selectedRoomId)

    formData.roomId = selectedRoomId
    formData.roomName = value.selectedOptions[0].text

    // 选择具体房源后，默认设置为不可退
    if (!formData.isUncertainRoom) {
        formData.isRefundable = '否'
    }

    // 如果找到房源详情，可以存储更多信息
    if (selectedRoom) {
        console.log('选中的房源详情:', selectedRoom)
        console.log('房源信息:', {
            roomId: selectedRoom.roomId,
            roomName: selectedRoom.roomName,
            buildingName: selectedRoom.buildingName,
            floorName: selectedRoom.floorName,
            rentArea: selectedRoom.rentArea,
            price: selectedRoom.price,
            rentStatus: selectedRoom.rentStatus
        })
    }

    showRoomPicker.value = false
}

// 应收日期选择
const showDueDatePicker = ref(false)
const dueDateArray = ref(['0','0','0'])
const onDueDateConfirm = (value: any) => {
    formData.receivableDate = formatDate(value.selectedValues)
    showDueDatePicker.value = false
}

// 签约日期选择
const showSignDatePicker = ref(false)
const signDateArray = ref(['0','0','0'])
const openSignDatePicker = () => {
    const today = new Date()
    const year = today.getFullYear()
    const month = String(today.getMonth() + 1).padStart(2, '0')
    const day = String(today.getDate()).padStart(2, '0')
    signDateArray.value = [String(year), String(month), String(day)]
    showSignDatePicker.value = true
}
const openDueDatePicker = () => {
    const today = new Date()
    const year = today.getFullYear()
    const month = String(today.getMonth() + 1).padStart(2, '0')
    const day = String(today.getDate()).padStart(2, '0')
    dueDateArray.value = [String(year), String(month), String(day)]
    showDueDatePicker.value = true
}
const onSignDateConfirm = (value: any) => {
    formData.expectSignDate = formatDate(value.selectedValues)
    showSignDatePicker.value = false
}

// 格式化日期
const formatDate = (date: Array<number>) => {
    return `${date[0]}-${date[1]}-${date[2]}`
}

// 退定金选择
const showRefundPicker = ref(false)
const refundValue = ref([])
const refundColumns = ref([
    { text: '是', value: '是' },
    { text: '否', value: '否' },
])
const onRefundConfirm = (value: any) => {
    formData.isRefundable = value.selectedValues[0]
    showRefundPicker.value = false
}

// 生成收款码
const generateQRCode = async () => {
    // if (!formData.id || !formData.bookingAmount) {
    //     console.warn('生成收款码所需参数不完整:', { id: formData.id, amount: formData.bookingAmount })
    //     return
    // }

    // 构建支付链接，这里可以根据实际业务需求构建
    // VITE_APP_BASE_URL='http://172.30.1.254:8571'
    const baseUrl = 'http://172.30.1.254:8571'
    // const orderId = formData.id || `temp_${Date.now()}`
    // const amount = formData.depositAmount
    // const customerName = encodeURIComponent(formData.customerName || '')

    // 构建支付链接 - 这里可以根据实际的支付系统来构建
    const paymentLink = `${baseUrl}/order-payment?id=${formData.id}`

    paymentUrl.value = paymentLink

    // paymentUrl.value = 'https://www.baidu.com'


}

// 处理二维码生成成功
const handleQRCodeGenerated = (dataUrl: string) => {
    console.log('二维码生成成功:', dataUrl)
}

// 处理二维码生成失败
const handleQRCodeError = (error: Error) => {
    showToast('二维码生成失败')
    console.error('二维码生成失败:', error)
}

// 显示收款码弹框
const showQRCodeDialog = () => {
    showQRCodePopup.value = true
}

// 金额格式化
const formatAmount = (amount: string | number | undefined) => {
    if (amount === undefined || amount === null || amount === '') {
        return '0.00'
    }
    const num = typeof amount === 'string' ? parseFloat(amount) : amount
    return num.toLocaleString('zh-CN', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
    })
}

// 表单验证
const validateForm = () => {
    if (!formData.projectName) {
        showToast('请选择项目名称')
        return false
    }
    // if (!formData.company) {
    //     showToast('请选择甲方公司')
    //     return false
    // }
    if (!formData.customerName) {
        showToast('请输入客户名称')
        return false
    }

    // 暂不确认房源时，不需要验证业态和房源
    if (!formData.isUncertainRoom) {
        if (!formData.propertyTypeCode && !formData.propertyType) {
            showToast('请选择意向业态')
            return false
        }
        if (!formData.roomName) {
            showToast('请选择意向房源或勾选暂不确认房源')
            return false
        }
    }

    if (!formData.bookingAmount) {
        showToast('请输入定金金额')
        return false
    }
    if (!formData.receivableDate) {
        showToast('请选择应收日期')
        return false
    }
    return true
}

// 保存
const handleSave = async () => {
    if (!validateForm()) return

    try {
        saveLoading.value = true

        const params = {
            ...formData,
            isSubmit: 0,
            isRefundable: formData.isRefundable === '是' ? 1 : 0,
            // 暂不确认房源时，将roomName设置为特殊标识
            roomName: formData.isUncertainRoom ? '暂不确认房源' : formData.roomName,
            roomId: formData.isUncertainRoom ? '' : formData.roomId
        }

        const res = await saveBooking(params)
        if (res.code === 200) {
            // save接口的回参msg是id，保存到formData中
            formData.id = res.msg || res.data?.id || formData.id
            formData.status = 0 // 草稿状态

            isSaved.value = true
            showToast('保存成功，可以提交了')
        } else {
            showToast(res.msg || '保存失败')
        }
    } catch (error) {
        console.error('保存失败:', error)
        // showToast('保存失败，请重试')
    } finally {
        saveLoading.value = false
    }
}

// 提交
const handleSubmit = async () => {
    if (!validateForm()) return

    // 必须先保存才能提交
    // if (!isSaved.value || !formData.id) {
    //     showToast('请先保存定单')
    //     return
    // }

    try {
        submitLoading.value = true

        const params = {
            ...formData,
            isSubmit: 1,
            isRefundable: formData.isRefundable === '是' ? 1 : 0,
            // 暂不确认房源时，将roomName设置为特殊标识
            roomName: formData.isUncertainRoom ? '暂不确认房源' : formData.roomName,
            roomId: formData.isUncertainRoom ? '' : formData.roomId
        }

        const res = await saveBooking(params)
        if (res.code === 200) {
            formData.status = 1 // 待收费状态
            formData.id = res.msg
            isSubmitted.value = true
            showToast('提交成功，定单已生效')

            // 定单生效后生成账单码
            await generateQRCode()
        } else {
            showToast(res.msg || '提交失败')
        }
    } catch (error) {
        console.error('提交失败:', error)
        // showToast('提交失败，请重试')
    } finally {
        submitLoading.value = false
    }
}

// 处理URL参数的函数
const handleUrlParams = async () => {
    const urlPropertyType = route.query.propertyType as string
    const urlRoomId = route.query.roomId as string

    // 如果URL中有propertyType参数，匹配意向业态
    if (urlPropertyType) {
        console.log('URL中检测到propertyType参数:', urlPropertyType)

        // 设置业态信息
        formData.propertyTypeCode = urlPropertyType
        formData.propertyType = urlPropertyType

        // 根据业态编码查找对应的业态名称
        const findPropertyTypeName = (code: string, options: DictOption[]): string => {
            for (const option of options) {
                if (option.code === code) {
                    return option.name
                }
                if (option.children && option.children.length > 0) {
                    const childName = findPropertyTypeName(code, option.children)
                    if (childName) return childName
                }
            }
            return ''
        }

        const propertyTypeName = findPropertyTypeName(urlPropertyType, propertyTypeOptions.value)
        if (propertyTypeName) {
            formData.propertyTypeName = propertyTypeName
            console.log('匹配到业态名称:', propertyTypeName)
        }

        // 更新业态选择器的值
        updateBusinessTypeValueByCode(urlPropertyType)

        // 如果有项目ID，查询对应业态的房源
        if (formData.projectId) {
            await getRoomList(formData.projectId, urlPropertyType)
        }
    }

    // 如果URL中有roomId参数，匹配意向房源
    if (urlRoomId) {
        console.log('URL中检测到roomId参数:', urlRoomId)

        // 如果还没有加载房源列表，先加载（不限制业态类型，以便能找到对应房源）
        if (roomColumns.value.length === 0 && formData.projectId) {
            await getRoomList(formData.projectId, formData.propertyTypeCode || undefined)
        }

        // 在房源列表中查找对应的房源
        let targetRoom = roomList.value.find(room => room.roomId === urlRoomId)

        // 如果没找到且有业态限制，尝试不限制业态重新加载
        if (!targetRoom && formData.propertyTypeCode && formData.projectId) {
            console.log('在当前业态中未找到房源，尝试加载所有房源')
            await getRoomList(formData.projectId, undefined)
            targetRoom = roomList.value.find(room => room.roomId === urlRoomId)
        }

        if (targetRoom) {
            formData.roomId = urlRoomId
            formData.roomName = `${targetRoom.parcelName || ''}-${targetRoom.buildingName || ''}-${targetRoom.roomName || ''}`.replace(/^-+/, '').replace(/-+$/, '') || targetRoom.roomId || ''
            console.log('匹配到房源:', formData.roomName)

            // 选择具体房源后，默认设置为不可退
            if (!formData.isUncertainRoom) {
                formData.isRefundable = '否'
            }
        } else {
            console.warn('未找到对应的房源:', urlRoomId)
            showToast('未找到指定的房源')
        }
    }
}

// 根据业态编码更新业态选择器的值
const updateBusinessTypeValueByCode = (code: string) => {
    const firstLevelOptions = getFirstLevelOptions(propertyTypeOptions.value)

    // 查找一级分类
    for (let i = 0; i < firstLevelOptions.length; i++) {
        const firstOption = firstLevelOptions[i]

        // 如果是一级分类
        if (firstOption.code === code) {
            businessTypeValue.value = [i, 0]
            return
        }

        // 查找二级分类
        const secondLevelOptions = getChildrenByParentCode(firstOption.code, propertyTypeOptions.value)
        for (let j = 0; j < secondLevelOptions.length; j++) {
            if (secondLevelOptions[j].code === code) {
                businessTypeValue.value = [i, j]

                // 更新二级分类选项
                const secondColumn = secondLevelOptions.map(option => ({
                    text: option.name,
                    value: option.code
                }))
                businessTypeColumns.value = [
                    businessTypeColumns.value[0],
                    secondColumn
                ]
                return
            }
        }
    }
}

// Keep-Alive 测试
const keepAliveTest = useKeepAliveTest('BookingCreate')
const keepAliveDebug = useKeepAliveDebug('BookingCreate')

// 加载编辑数据
const loadEditData = async (id: string) => {
    console.log('加载编辑数据:', id)
    const res = await getBookingDetail(id)
    if (res.code === 200) {
        let bookingData = res.data.booking
        formData.id = bookingData.id
        formData.projectId = bookingData.projectId
        formData.projectName = bookingData.projectName
        formData.merchantId = bookingData.merchantId
        formData.company = bookingData.company
        formData.customerName = bookingData.customerName
        formData.propertyType = bookingData.propertyType
        formData.propertyTypeCode = bookingData.propertyType
        // formData.propertyTypeName = bookingData.propertyTypeName
        formData.isUncertainRoom = bookingData.roomName === '暂不确认房源'
        formData.roomId = bookingData.roomId
        formData.roomName = bookingData.roomName
        formData.bookingAmount = bookingData.bookingAmount
        formData.receivableDate = bookingData.receivableDate
        formData.isRefundable = bookingData.isRefund ? '是' : '否'
        // formData.isUncertainRoom = bookingData.isUncertainRoom
        formData.status = bookingData.status
        formData.expectSignDate = bookingData.expectSignDate
    }
}

// 生命周期钩子
onMounted(async () => {
    // keepAliveDebug.recordMounted()

    // 检查是否为编辑模式
    const mode = route.query.mode as string
    const id = route.query.id as string
    
    if (mode === 'edit' && id) {
        isEditMode.value = true
        editId.value = id
        // 这里可以添加加载编辑数据的逻辑
        await loadEditData(id)
    }

    // getProjectList()
    let currentProject = localStorage.getItem('currentProject')
    if (currentProject) {
        currentProject = JSON.parse(currentProject)
        // @ts-ignore
        formData.projectId = currentProject.id
        // @ts-ignore
        formData.projectName = currentProject.name
    } else {
        // formData.projectId = '108'
        // formData.projectName = '福州(马尾)万洋广场'
    }

    // 初始化业态数据
    initPropertyTypeData()

    // 处理URL参数（如果有的话）
    await handleUrlParams()

    // 打印 keep-alive 测试报告
    // setTimeout(() => {
    //     keepAliveTest.printReport()
    //     keepAliveDebug.printReport()
    // }, 1000)
})

onActivated(() => {
    keepAliveDebug.recordActivated()
    console.log('📱 BookingCreate 组件被激活')
})

onDeactivated(() => {
    keepAliveDebug.recordDeactivated()
    console.log('📱 BookingCreate 组件被失活')
})

onUnmounted(() => {
    keepAliveDebug.recordUnmounted()
    console.log('📱 BookingCreate 组件被卸载')
})
</script>

<style lang="less" scoped>
.booking-create {
    min-height: 100vh;
    background-color: #F5F5F5;
    padding-bottom: 80px;

    :deep(.van-cell) {
        padding: 32px 32px;
        background: #fff;
    }

    .uncertain-room {
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: flex-start;
        padding: 16px;
        background: #fff;

        .uncertain-room-content {
            width: 100%;
            box-sizing: border-box;
            padding: 16px;
            background: rgba(216, 216, 216, 0.2);
            display: flex;
            align-items: center;

            .uncertain-room-checkbox {
                margin-right: 16px;
                // width: 24px;
                // height: 24px;
                font-size: 20px;
            }

            .uncertain-room-title {
                font-size: 24px;
                color: #333333;
            }
        }
    }
}

.qrcode-section {
    background: #FFFFFF;
    margin: 16px;
    border-radius: 8px;
    padding: 20px;
    text-align: center;

    .qrcode-title {
        font-size: 32px;
        color: #333333;
        margin-bottom: 16px;
        text-align: left;
        font-weight: 500;
    }

    .qrcode-button-container {
        text-align: center;
        padding: 20px 0;
    }
}

.qrcode-actions {
    margin-top: 16px;
    text-align: center;
}

.collect-code-content {
    .order-info {
        margin-bottom: 24px;

        .info-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
            padding: 0 16px;

            .label {
                color: #86909c;
                font-size: 14px;
            }

            .value {
                color: #1d2129;
                font-size: 14px;
                font-weight: 500;

                &.amount {
                    color: #f53f3f;
                    font-weight: 600;
                    font-size: 16px;
                }
            }
        }
    }

    .qrcode-display {
        margin-bottom: 24px;
        display: flex;
        justify-content: center;
    }
}

.scan-tip {
    color: #666666;
    margin-top: 12px;
}

.bottom-buttons {
    display: flex;
    gap: 30px;
    padding: 30px;
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: #FFFFFF;

    .save-btn {
        flex: 1;
        border: 1px solid #3583FF;
        color: #3583FF;
        font-weight: 500;
        border-radius: 60px;
    }

    .submit-btn {
        flex: 1;
        border: 1px solid #3583FF;
        color: #fff;
        font-weight: 500;
        border-radius: 60px;
        background: #3583FF;
    }

}


.unit {
    color: #666;
    margin-left: 4px;
}

// 收款码弹框样式
.qrcode-popup-content {
    padding: 24px;
    text-align: center;

    .qrcode-popup-title {
        font-size: 18px;
        font-weight: 600;
        color: #333333;
        margin-bottom: 20px;
        text-align: center;
    }

    .order-info {
        margin-bottom: 24px;
        background: #f8f9fa;
        border-radius: 8px;
        padding: 16px;

        .info-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
            font-size: 14px;

            &:last-child {
                margin-bottom: 0;
            }

            .label {
                color: #666666;
                font-weight: normal;
            }

            .value {
                color: #333333;
                font-weight: 500;

                &.amount {
                    color: #f53f3f;
                    font-weight: 600;
                    font-size: 16px;
                }
            }
        }
    }

    .qrcode-display {
        margin-bottom: 20px;
        display: flex;
        justify-content: center;
    }

    .qrcode-actions {
        text-align: center;
    }
}
</style>
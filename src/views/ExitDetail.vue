<script lang="ts">
export default {
  name: 'ExitDetail'
}
</script>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { getExitDetail } from '../api/exit'
import { showToast, showLoadingToast } from 'vant'

const router = useRouter()
const route = useRoute()

// 页面数据
const loading = ref(false)
const exitDetail = ref<any>(null)
const basicInfo = ref({
    contractNo: '',
    customerName: '',
    rentPeriod: '',
    terminateDate: '',
    createTime: '',
    createByName: '',
    status: '',
    progressStatusName: ''
})
const roomList = ref<any[]>([])
const costList = ref<any[]>([])
const expandedRooms = ref<string[]>([])

// 获取出场详情
const fetchExitDetail = async () => {
    const exitId = route.query.exitId as string
    if (!exitId) {
        showToast('缺少出场单ID')
        return
    }

    try {
        loading.value = true
        const loadingToast = showLoadingToast({
            message: '加载中...',
            forbidClick: true,
        })

        const response = await getExitDetail(exitId)
        
        if (response.code === 200 && response.data) {
            exitDetail.value = response.data
            setDetailData(response.data)
        } else {
            showToast(response.msg || '获取详情失败')
        }
        
        loadingToast.close()
    } catch (error) {
        console.error('获取出场详情失败:', error)
        showToast('获取详情失败')
    } finally {
        loading.value = false
    }
}

// 设置详情数据
const setDetailData = (data: any) => {
    const exitInfo = data.exitInfo || {}
    const contractInfo = data.contractTerminateInfo?.contract || {}
    
    // 设置基本信息
    basicInfo.value = {
        contractNo: contractInfo.contractNo || '',
        customerName: contractInfo.customerName || '',
        rentPeriod: contractInfo.startDate && contractInfo.endDate 
            ? `${contractInfo.startDate} 至 ${contractInfo.endDate}`
            : '',
        terminateDate: data.contractTerminateInfo?.terminateDate || '',
        createTime: formatDateTime(exitInfo.createTime || ''),
        createByName: exitInfo.createByName || '',
        status: getStatusText(exitInfo.progressStatus),
        progressStatusName: getProgressStatusText(exitInfo.progressStatus)
    }
    
    // 设置房源列表
    roomList.value = data.exitRoomList || []
    
    // 设置费用列表
    costList.value = data.exitCostList || []
    
    // 默认展开第一个房间
    if (roomList.value.length > 0) {
        expandedRooms.value = [roomList.value[0].id]
    }
}

// 获取办理进度状态文本
const getProgressStatusText = (status: number) => {
    // 办理进度: 0-不可见, 1-待办理, 5-物业交割, 10-费用结算, 15-交割并结算, 20-客户签字, 25-发起退款, 30-已完成, 40-已作废
    switch (status) {
        case 0: return '不可见'
        case 1: return '待办理'
        case 5: return '物业交割' 
        case 10: return '费用结算'
        case 15: return '交割并结算'
        case 20: return '客户签字'
        case 25: return '发起退款'
        case 30: return '已完成'
        case 40: return '已作废'
        default: return '未知'
    }
}

// 获取状态文本
const getStatusText = (progressStatus: number): string => {
    if (progressStatus === 1) return '待办理'
    if (progressStatus >= 5 && progressStatus < 30) return '办理中'
    if (progressStatus === 30) return '已办理'
    if (progressStatus === 40) return '已作废'
    return '未知'
}

// 格式化日期时间
const formatDateTime = (dateStr: string): string => {
    if (!dateStr) return ''
    const date = new Date(dateStr)
    return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
    })
}

// 格式化日期
const formatDate = (dateStr: string): string => {
    if (!dateStr) return ''
    return dateStr.split('T')[0]
}

// 切换房间展开状态
const toggleRoomExpand = (roomId: string) => {
    const index = expandedRooms.value.indexOf(roomId)
    if (index > -1) {
        expandedRooms.value.splice(index, 1)
    } else {
        expandedRooms.value.push(roomId)
    }
}

// 获取确认状态文本
const getConfirmStatusText = (isConfirmed: boolean): string => {
    return isConfirmed ? '已确认' : '待确认'
}

// 获取确认状态类名
const getConfirmStatusClass = (isConfirmed: boolean): string => {
    return isConfirmed ? 'confirmed' : 'pending'
}

// 返回上一页
const goBack = () => {
    router.back()
}

// 页面挂载
onMounted(() => {
    fetchExitDetail()
})
</script>

<template>
    <div class="exit-detail">
        <!-- 导航栏 -->
        <van-nav-bar title="出场详情" left-arrow fixed placeholder @click-left="goBack" />

        <!-- 加载状态 -->
        <van-loading v-if="loading" class="loading-center" size="24px" vertical>
            加载中...
        </van-loading>

        <!-- 内容区域 -->
        <div v-else-if="exitDetail" class="content-area">
            <!-- 基本信息 -->
            <div class="basic-info-section">
                <div class="basic-info-card">
                    <div class="info-content">
                        <div class="info-row">
                            <span class="info-label">合同编号：</span>
                            <span class="info-value">{{ basicInfo.contractNo }}</span>
                        </div>
                        <div class="info-row">
                            <span class="info-label">承租方：</span>
                            <span class="info-value">{{ basicInfo.customerName }}</span>
                        </div>
                        <div class="info-row">
                            <span class="info-label">租期：</span>
                            <span class="info-value">{{ basicInfo.rentPeriod }}</span>
                        </div>
                        <div class="info-row">
                            <span class="info-label">退租日期：</span>
                            <span class="info-value">{{ basicInfo.terminateDate }}</span>
                        </div>
                        <div class="info-row">
                            <span class="info-label">办理状态：</span>
                            <span class="info-value">{{ basicInfo.progressStatusName }}</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 出场房源 -->
            <div class="rooms-section">
                <div class="section-title">
                    <img src="../assets/images/house-icon.svg" alt="房源" class="title-icon" />
                    出场房源 · {{ roomList.length }}间
                </div>

                <!-- 房源列表 -->
                <div class="room-list">
                    <div v-for="(room, roomIndex) in roomList" :key="room.id" class="room-card">
                        <!-- 房间头部 -->
                        <div class="room-header" @click="toggleRoomExpand(room.id)">
                            <div class="room-info">
                                <div class="room-name-row">
                                    <img class="house-icon" src="../assets/images/house-icon.svg" alt="房源" />
                                    <span class="room-name">{{ room.roomName }}</span>
                                </div>
                                <div class="room-date">退租日期：{{ formatDate(room.terminateDate || '') || '未设置' }}</div>
                            </div>
                            <van-icon 
                                :name="expandedRooms.includes(room.id) ? 'arrow-up' : 'arrow-down'" 
                                class="expand-icon" 
                            />
                        </div>

                        <!-- 房间详情 -->
                        <div v-if="expandedRooms.includes(room.id)" class="room-content">
                            <!-- 基本信息 -->
                            <div class="room-basic-info">
                                <div class="info-row">
                                    <span class="info-label">楼栋:</span>
                                    <span class="info-value">{{ room.buildingName || '-' }}</span>
                                </div>
                                <div class="info-row">
                                    <span class="info-label">地块:</span>
                                    <span class="info-value">{{ room.parcelName || '-' }}</span>
                                </div>
                                <div class="info-row">
                                    <span class="info-label">退租日期:</span>
                                    <span class="info-value">{{ formatDate(room.terminateDate || '') || '未设置' }}</span>
                                </div>
                            </div>

                            <!-- 确认状态 -->
                            <div class="confirm-section">
                                <div class="section-title-blue">确认状态</div>
                                
                                <div class="confirm-item">
                                    <span class="confirm-label">商服确认:</span>
                                    <span class="confirm-value" :class="getConfirmStatusClass(room.isBusinessConfirmed)">
                                        {{ getConfirmStatusText(room.isBusinessConfirmed) }}
                                    </span>
                                </div>
                                
                                <div class="confirm-item">
                                    <span class="confirm-label">工程确认:</span>
                                    <span class="confirm-value" :class="getConfirmStatusClass(room.isEngineeringConfirmed)">
                                        {{ getConfirmStatusText(room.isEngineeringConfirmed) }}
                                    </span>
                                </div>
                                
                                <div class="confirm-item">
                                    <span class="confirm-label">财务确认:</span>
                                    <span class="confirm-value" :class="getConfirmStatusClass(room.isFinanceConfirmed)">
                                        {{ getConfirmStatusText(room.isFinanceConfirmed) }}
                                    </span>
                                </div>
                            </div>

                            <!-- 备注 -->
                            <div class="remark-section" v-if="room.remark">
                                <div class="section-title-blue">备注</div>
                                <div class="remark-content">{{ room.remark }}</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 费用明细 -->
            <div class="costs-section" v-if="costList.length > 0">
                <div class="section-title">
                    <img src="../assets/images/settlement-icon.svg" alt="费用" class="title-icon" />
                    费用明细 · {{ costList.length }}项
                </div>

                <div class="cost-list">
                    <div v-for="(cost, costIndex) in costList" :key="costIndex" class="cost-item">
                        <div class="cost-info">
                            <div class="cost-name">{{ cost.subjectName }}</div>
                            <div class="cost-details">
                                <div class="cost-period">{{ cost.startDate }} 至 {{ cost.endDate }}</div>
                                <div class="cost-amount">¥{{ cost.amount }}</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 空状态 -->
        <van-empty v-else description="暂无详情数据" />
    </div>
</template>

<style scoped>
.exit-detail {
    min-height: 100vh;
    background-color: #F1F1F1;
}

/* 加载状态 */
.loading-center {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 200px;
    flex-direction: column;
}

/* 内容区域 */
.content-area {
    padding-bottom: 20px;
}

/* 基本信息区域 */
.basic-info-section {
    margin: 16px;
    overflow: hidden;
}

.basic-info-card {
    background: linear-gradient(135deg, #3583FF 0%, #1677FF 100%); /* 蓝色渐变背景 */
    border-radius: 12px;
    padding: 30px 20px;
    color: #FFFFFF; /* 白色文字 */
    position: relative;
    overflow: hidden;
    box-shadow: 0 4px 16px rgba(53, 131, 255, 0.3);
}

.info-content {
    position: relative;
    z-index: 2;
}

.info-row {
    display: flex;
    align-items: center;
    margin-bottom: 16px;
    font-size: 28px;
    line-height: 1.4;
}

.info-row:last-child {
    margin-bottom: 0;
}

.info-label {
    font-weight: 400;
    color: rgba(255, 255, 255, 0.9);
    min-width: 120px;
}

.info-value {
    font-weight: 500;
    color: #FFFFFF;
    flex: 1;
}

.title-icon {
    width: 32px;
    height: 32px;
}

.section-title {
    font-size: 32px;
    font-weight: 600;
    color: #242433;
    padding: 20px 30px;
    border-bottom: 1px solid #E8EBFF;
    display: flex;
    align-items: center;
    gap: 10px;
    background-color: #FFFFFF;
    border-radius: 12px 12px 0 0;
}

/* 房源区域 */
.rooms-section {
    margin: 0 16px 16px;
}

.room-list {
    margin-top: 16px;
}

.room-card {
    background-color: #FFFFFF;
    border-radius: 12px;
    margin-bottom: 16px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
    overflow: hidden;
}

/* 房间头部 */
.room-header {
    display: flex;
    align-items: center;
    padding: 16px;
    cursor: pointer;
    border-bottom: 1px solid #F5F5F5;
}

.room-info {
    flex: 1;
}

.room-name-row {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
}

.house-icon {
    width: 30px;
    height: 30px;
    margin-right: 16px;
}

.room-name {
    font-size: 28px;
    font-weight: 500;
    color: #242433;
}

.room-date {
    font-size: 24px;
    color: #919199;
}

.expand-icon {
    color: #C8C9CC;
    font-size: 16px;
}

/* 房间内容 */
.room-content {
    padding: 0 16px 16px;
}

/* 房间基本信息 */
.room-basic-info {
    padding: 16px 0;
    border-bottom: 1px solid #F5F5F5;
    margin-bottom: 16px;
}

/* 蓝色标题样式 */
.section-title-blue {
    font-size: 28px;
    font-weight: 500;
    color: #1677FF;
    padding: 22px 0 22px 16px;
    border-bottom: 1px solid #E8EBFF;
}

/* 确认状态区域 */
.confirm-section {
    margin-bottom: 16px;
}

.confirm-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    background-color: #f8f9fa;
    margin-bottom: 8px;
    border-radius: 8px;
}

.confirm-item:last-child {
    margin-bottom: 0;
}

.confirm-label {
    font-size: 28px;
    color: #666;
}

.confirm-value {
    font-size: 28px;
    font-weight: 500;
}

.confirm-value.confirmed {
    color: #52c41a;
}

.confirm-value.pending {
    color: #ff4d4f;
}

/* 备注区域 */
.remark-section {
    margin-bottom: 16px;
}

.remark-content {
    padding: 16px;
    background-color: #f8f9fa;
    border-radius: 8px;
    font-size: 28px;
    color: #333;
    line-height: 1.5;
}

/* 费用区域 */
.costs-section {
    margin: 0 16px 16px;
}

.cost-list {
    margin-top: 16px;
}

.cost-item {
    background-color: #FFFFFF;
    border-radius: 12px;
    margin-bottom: 16px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
    overflow: hidden;
    padding: 16px;
}

.cost-info {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.cost-name {
    font-size: 28px;
    font-weight: 500;
    color: #242433;
}

.cost-details {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.cost-period {
    font-size: 24px;
    color: #919199;
}

.cost-amount {
    font-size: 28px;
    font-weight: 600;
    color: #ff6b35;
}
</style>

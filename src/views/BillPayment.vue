<script setup lang="ts">
import { ref, computed, onMounted, nextTick } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import {
	NavBar,
	Button,
	Checkbox,
	CheckboxGroup,
	Loading,
	Empty,
	showConfirmDialog,
	showDialog,
	showToast,
	showLoadingToast,
	closeToast
} from 'vant'
import { getContractDetail, payContract, COST_TYPE_TEXT, COST_STATUS, COST_TYPE } from '../api/contract'
import type { ContractDetailVo, ContractPaymentDto, ContractPaymentItemDto, CostVo } from '../api/contract'
// @ts-ignore
import { formatMoney, formatDate } from '../utils/format'

// 获取路由
const router = useRouter()
const route = useRoute()

// 页面状态
const loading = ref(false)
const contractDetail = ref<ContractDetailVo | null>(null)
const contractId = ref<string>('')

// 选中的账单ID
const checkedBills = ref<string[]>([])

// 判断是否是扫码进入（通过URL参数id判断）
const isScannedEntry = computed(() => {
	return !!route.query.id
})

// 获取合同ID
const getContractId = () => {
	const id = route.query.id as string
	if (!id) {
		showToast('缺少合同ID参数')
		if (!isScannedEntry.value) {
			router.back()
		}
		return ''
	}
	return id
}

// 获取账单状态
const getBillStatus = (bill: CostVo): BillStatusType => {
	const today = new Date()
	const receivableDate = new Date(bill.receivableDate)

	if (receivableDate < today) {
		const diffDays = Math.floor((today.getTime() - receivableDate.getTime()) / (1000 * 60 * 60 * 24))
		if (diffDays > 0) {
			return 'overdue' // 逾期
		}
	}

	if (receivableDate.toDateString() === today.toDateString()) {
		return 'expired' // 已到期（今日到期）
	}

	return 'upcoming' // 未到期
}

// 状态类型定义
type BillStatusType = 'overdue' | 'expired' | 'upcoming'

// 状态颜色映射
const STATUS_COLOR: Record<BillStatusType, string> = {
	overdue: '#FF9428',   // 逾期-橙色
	expired: '#FF4141',   // 已到期-红色  
	upcoming: '#3AC8D4'   // 未到期-蓝绿色
}

// 状态文字映射
const STATUS_TEXT: Record<BillStatusType, string> = {
	overdue: '逾期',
	expired: '已到期',
	upcoming: '未到期'
}

// 租赁信息
const rentalInfo = computed(() => {
	if (!contractDetail.value?.contract) {
		return {
			units: [],
			tenant: '',
			contractPeriod: ''
		}
	}

	let _roomList: any[] = []
	if (!!contractDetail.value.contract.roomName) {
		_roomList = contractDetail.value.contract.roomName.split('、')
	}else {
		
	}


	return {
		units: _roomList,
		tenant: contractDetail.value.contract.customerName || '',
		contractPeriod: `${formatDate(contractDetail.value.contract.startDate)} ～ ${formatDate(contractDetail.value.contract.endDate)}`
	}
})

const displayUnits = computed(() => {
	// "一期地块-A01-001号、一期地块-A01-002号、一期地块-A01-003号、一期地块-A01-004号、一期地块-A01-005号、一期地块-A01-006号"

	if (!rentalInfo.value.units) return [];
	if (rentalInfo.value.units.length <= 3) return rentalInfo.value.units;
	return [...rentalInfo.value.units.slice(0, 3), '...'];
});

// 账单列表（从接口获取）
const bills = computed(() => {
	if (!contractDetail.value?.unpaidBills) return []

	return contractDetail.value.unpaidBills
		.filter(bill => {
			// 计算未支付金额
			const unpaidAmount = bill.actualReceivable - bill.receivedAmount

			// 只展示需要缴费的账单
			return bill.canPay &&
				bill.status === COST_STATUS.PENDING_RECEIVE &&
				bill.actualReceivable > 0 && // 只展示正数账单
				unpaidAmount > 0 && // 只展示有未支付金额的账单
				!bill.isRevenueBill // 营收抽点的预估账单不展示
		})
		.map(bill => ({
			...bill,
			costTypeName: COST_TYPE_TEXT[bill.costType as keyof typeof COST_TYPE_TEXT] || '其他',
			statusType: getBillStatus(bill),
			unpaidAmount: bill.actualReceivable - bill.receivedAmount // 计算未支付金额
		}))
		.sort((a, b) => {
			// 按账单费用类型（保证金、租金、其他费用）+应收款日期顺序排序
			// 首先按费用类型排序：保证金(1) < 租金(2) < 其他费用(3)
			if (a.costType !== b.costType) {
				return a.costType - b.costType
			}
			// 相同费用类型按应收日期排序
			return new Date(a.receivableDate).getTime() - new Date(b.receivableDate).getTime()
		})
})

// 检查账单选择是否符合顺序规则
const validateBillSelection = (newCheckedIds: string[]): { valid: boolean; message?: string } => {
	if (newCheckedIds.length === 0) return { valid: true }

	// 获取保证金和租金账单
	const bondAndRentBills = bills.value.filter(bill =>
		bill.costType === COST_TYPE.BOND || bill.costType === COST_TYPE.RENT
	)

	if (bondAndRentBills.length === 0) return { valid: true }

	// 检查保证金和租金账单是否按顺序选择
	const checkedBondAndRent = bondAndRentBills.filter(bill =>
		newCheckedIds.includes(bill.id)
	)

	if (checkedBondAndRent.length === 0) return { valid: true }

	// 找到选中的最后一笔保证金或租金账单的索引
	const lastCheckedIndex = bondAndRentBills.findIndex(bill =>
		bill.id === checkedBondAndRent[checkedBondAndRent.length - 1].id
	)

	// 检查前面的账单是否都已选中
	for (let i = 0; i <= lastCheckedIndex; i++) {
		if (!newCheckedIds.includes(bondAndRentBills[i].id)) {
			return {
				valid: false,
				message: '您有待缴纳的历史账单，请先完成历史账单缴纳，才能缴纳后面的账单'
			}
		}
	}

	return { valid: true }
}

// 自动补选前置账单
const autoSelectPreviousBills = (billId: string): string[] => {
	const targetBill = bills.value.find(bill => bill.id === billId)
	if (!targetBill) return checkedBills.value

	// 如果是其他费用，不需要补选
	if (targetBill.costType === COST_TYPE.OTHER) {
		return [...new Set([...checkedBills.value, billId])]
	}

	// 对于保证金和租金，需要补选前面的账单
	const bondAndRentBills = bills.value.filter(bill =>
		bill.costType === COST_TYPE.BOND || bill.costType === COST_TYPE.RENT
	)

	const targetIndex = bondAndRentBills.findIndex(bill => bill.id === billId)
	if (targetIndex === -1) return checkedBills.value

	// 选中目标账单及之前的所有保证金和租金账单
	const previousBillIds = bondAndRentBills
		.slice(0, targetIndex + 1)
		.map(bill => bill.id)

	// 保留其他费用的选择，合并保证金和租金的选择
	const otherBillIds = checkedBills.value.filter(id => {
		const bill = bills.value.find(b => b.id === id)
		return bill && bill.costType === COST_TYPE.OTHER
	})

	return [...new Set([...otherBillIds, ...previousBillIds])]
}

// 监听账单选择变化
const handleBillCheck = (billId: string, checked: boolean) => {
	if (checked) {
		// 选中账单时，自动补选前置账单
		const newCheckedIds = autoSelectPreviousBills(billId)
		checkedBills.value = newCheckedIds
	} else {
		// 取消选中时，检查是否符合规则
		const newCheckedIds = checkedBills.value.filter(id => id !== billId)
		const validation = validateBillSelection(newCheckedIds)

		if (!validation.valid) {
			showDialog({
				title: '提示',
				message: validation.message || '操作不符合规则',
			})
			return
		}

		checkedBills.value = newCheckedIds
	}
}

// 是否全选
const isAllChecked = computed({
	get: () => checkedBills.value.length === bills.value.length && bills.value.length > 0,
	set: (val) => {
		if (val) {
			// 全选 - 直接选择所有账单
			checkedBills.value = bills.value.map(bill => bill.id)
		} else {
			// 取消全选
			checkedBills.value = []
		}
	}
})

// 计算选中账单总金额
const totalAmount = computed(() => {
	const total = bills.value
		.filter(bill => checkedBills.value.includes(bill.id))
		.reduce((sum, bill) => {
			return sum + bill.unpaidAmount
		}, 0)

	// 使用 Math.round 避免浮点数精度问题
	const roundedTotal = Math.round(total * 100) / 100
	return formatMoney(roundedTotal)
})

// 默认勾选逻辑
const setDefaultCheckedBills = () => {
	if (bills.value.length === 0) return

	const today = new Date()
	today.setHours(0, 0, 0, 0)

	// 找出逾期和当天应收的账单
	const overdueAndTodayBills = bills.value.filter(bill => {
		const receivableDate = new Date(bill.receivableDate)
		receivableDate.setHours(0, 0, 0, 0)
		return receivableDate <= today
	})

	if (overdueAndTodayBills.length > 0) {
		// 如果有逾期和当天的账单，默认勾选它们
		checkedBills.value = overdueAndTodayBills.map(bill => bill.id)
	} else {
		// 如果没有逾期和当天的，默认勾选第一笔
		checkedBills.value = [bills.value[0].id]
	}
}

// 获取合同详情
const fetchContractDetail = async () => {
	loading.value = true
	try {
		const response = await getContractDetail(contractId.value)
		contractDetail.value = response.data

		// 数据加载完成后设置默认勾选
		nextTick(() => {
			setDefaultCheckedBills()
		})
	} catch (error) {
		console.error('获取合同详情失败:', error)
		showToast('获取合同详情失败')
		if (!isScannedEntry.value) {
			router.back()
		}
	} finally {
		loading.value = false
	}
}

// 返回上一页
const goBack = () => {
	router.back()
}

// 去支付方法
const handlePayment = async () => {
	if (checkedBills.value.length === 0) {
		showDialog({
			title: '提示',
			message: '请至少选择一个账单进行支付',
		})
		return
	}

	const selectedBills = bills.value.filter(bill => checkedBills.value.includes(bill.id))
	const totalAmountValue = Math.round(selectedBills.reduce((sum, bill) => sum + bill.unpaidAmount, 0) * 100) / 100

	try {
		await showConfirmDialog({
			title: '确认支付',
			message: `确认支付￥${formatMoney(totalAmountValue)}?`,
		})

		showLoadingToast({ message: '正在处理支付...', forbidClick: true })

		// 构造支付参数
		const costList: ContractPaymentItemDto[] = selectedBills.map(bill => ({
			costId: bill.id,
			payAmount: Math.round(bill.unpaidAmount * 100) / 100
			// payAmount: 0.01
		}))

		const paymentData: ContractPaymentDto = {
			contractId: contractId.value,
			amount: totalAmountValue,
			// amount: 0.01,
			costList
		}

		const response = await payContract(paymentData)
		closeToast()

		if (response.code === 200 && response.paymentUrl) {
			// 跳转到支付页面
			window.location.href = response.paymentUrl
		} else if (response.code === 200) {
			showToast('支付成功')
			// 如果是扫码进入，关闭浏览器；否则返回上一页
			setTimeout(() => {
				if (isScannedEntry.value) {
					window.close()
				} else {
					router.back()
				}
			}, 2000)
		} else {
			showToast(response.msg || '支付失败')
		}
	} catch (error) {
		// closeToast()
		setTimeout(() => {
			closeToast()
			}, 2000)
		// if (error !== 'cancel') {
		// 	console.error('支付失败:', error)
		// 	showToast('支付处理失败，请重试')
		// }
	}
}

// 页面初始化
onMounted(() => {
	contractId.value = getContractId()
	if (contractId.value) {
		fetchContractDetail()
	}
})
</script>

<template>
	<div class="bill-payment">
		<!-- 顶部导航栏 -->
		<van-nav-bar title="账单缴费" :left-arrow="!isScannedEntry" fixed placeholder @click-left="goBack" />

		<!-- 加载状态 -->
		<div v-if="loading" class="loading-container">
			<van-loading color="#3583FF" />
			<div class="loading-text">加载中...</div>
		</div>

		<!-- 账单内容区域 -->
		<div v-else-if="contractDetail" class="bill-content">
			<!-- 租户信息卡片 -->
			<div class="tenant-card">
				<div class="tenant-info">
					<div class="info-row">
						<span class="label">租赁单元：</span>
					</div>
					<div class="unit-names">
						<div v-for="(unit, index) in displayUnits" :key="index" class="unit-name">
							{{ unit }} {{ index === 0 || index === 1 ? '、' : '' }}
						</div>
					</div>
					<div class="info-row">
						<span class="label">承租方：</span>
						<span>{{ rentalInfo.tenant }}</span>
					</div>
					<div class="info-row">
						<span class="label">合同周期：</span>
						<span>{{ rentalInfo.contractPeriod }}</span>
					</div>
				</div>
			</div>

			<!-- 账单列表 -->
			<div class="bill-list">
				<div v-for="bill in bills" :key="bill.id" class="bill-item">
					<div class="bill-container">
						<div class="bill-header">
							<div class="bill-type">{{ bill.costTypeName }}</div>
							<div class="bill-status" :style="{ backgroundColor: STATUS_COLOR[bill.statusType] }">
								{{ STATUS_TEXT[bill.statusType] }}
							</div>
						</div>

						<div class="bill-period">{{ formatDate(bill.startDate) }} ～ {{ formatDate(bill.endDate) }}</div>

						<div class="bill-divider"></div>

						<div class="bill-details">
							<div class="due-date">应缴纳日期：{{ formatDate(bill.receivableDate) }}</div>
							<div class="bill-amount-info">
								<div class="bill-amount">¥{{ formatMoney(bill.unpaidAmount) }}</div>
								<div v-if="bill.receivedAmount > 0" class="bill-paid-info">
									已付 ¥{{ formatMoney(bill.receivedAmount) }}
								</div>
							</div>
						</div>

						<div class="bill-checkbox">
							<van-checkbox :model-value="checkedBills.includes(bill.id)"
								@update:model-value="(checked) => handleBillCheck(bill.id, checked)" />
						</div>
					</div>
				</div>
			</div>
		</div>

		<!-- 空状态 -->
		<van-empty v-else-if="!loading" description="未找到合同信息" />

		<!-- 底部支付栏 -->
		<div v-if="contractDetail && bills.length > 0" class="payment-footer">
			<div class="footer-left">
				<van-checkbox v-model="isAllChecked">全选</van-checkbox>
				<div class="total">
					<span class="total-label">合计</span>
					<span class="total-amount">¥{{ totalAmount }}</span>
				</div>
			</div>

			<van-button type="primary" class="pay-button" :disabled="!checkedBills.length" @click="handlePayment">
				去支付
			</van-button>
		</div>
	</div>
</template>

<style scoped>
.bill-payment {
	min-height: 100vh;
	background-color: #f1f1f1;
	display: flex;
	flex-direction: column;
	padding-bottom: 160px;
}

.loading-container {
	flex: 1;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	gap: 16px;
}

.loading-text {
	font-size: 28px;
	color: #666;
}

.bill-content {
	flex: 1;
	padding: 16px;
	margin-bottom: 120px;
}

/* 租户信息卡片 */
.tenant-card {
	background: linear-gradient(to bottom, #009AFF, #005FFF);
	border-radius: 20px;
	overflow: hidden;
	margin-bottom: 20px;
}

.tenant-info {
	padding: 24px 16px;
	color: #ffffff;
}

.info-row {
	display: flex;
	font-size: 24px;
	margin-bottom: 10px;
	line-height: 1.4;
}

.label {
	font-weight: 400;
}

.unit-names {
	margin-bottom: 16px;
	display: flex;
	align-items: center;
	flex-wrap: wrap;
}

.unit-name {
	font-size: 28px;
	font-weight: 600;
	line-height: 1.4;
}

/* 账单列表 */
.bill-list {
	display: flex;
	flex-direction: column;
	gap: 16px;
}

.bill-item {
	position: relative;
}

.bill-container {
	background: #fff;
	border-radius: 20px;
	padding: 16px;
	border: 1px solid #f0f0f0;
	position: relative;
}

.bill-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 10px;
	box-sizing: border-box;
	padding-left: 56px;
}

.bill-type {
	font-size: 30px;
	font-weight: 500;
	color: #242433;
}

.bill-status {
	color: #fff;
	font-size: 24px;
	padding: 2px 8px;
	border-radius: 4px;
}

.bill-period {
	font-size: 26px;
	color: #000;
	margin-bottom: 16px;
	box-sizing: border-box;
	padding-left: 56px;
}

.bill-divider {
	height: 1px;
	background-color: #EEEEEE;
	margin: 16px 0;
}

.bill-details {
	display: flex;
	justify-content: space-between;
	align-items: center;
	box-sizing: border-box;
	padding: 18px 0 18px 56px;
}

.due-date {
	font-size: 24px;
	color: #919199;
}

.bill-amount-info {
	text-align: right;
}

.bill-amount {
	font-size: 28px;
	font-weight: 600;
	color: #FF6900;
}

.bill-paid-info {
	font-size: 20px;
	color: #666;
	margin-top: 4px;
}

.bill-checkbox {
	position: absolute;
	left: 16px;
	top: 16px;
}

/* 底部支付栏 */
.payment-footer {
	position: fixed;
	height: 120px;
	bottom: 0;
	left: 0;
	right: 0;
	background: #fff;
	display: flex;
	justify-content: space-between;
	align-items: center;
	box-sizing: border-box;
	overflow: hidden;
	padding: 16px;
	box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.04);
	z-index: 10;
}

.footer-left {
	display: flex;
	align-items: center;
	gap: 24px;
}

.total {
	display: flex;
	flex-direction: column;
}

.total-label {
	font-size: 26px;
	font-weight: 500;
	color: #242433;
}

.total-amount {
	font-size: 34px;
	font-weight: 500;
	color: #FF6900;
}

.pay-button {
	width: 240px;
	background: #3583FF;
	border-radius: 45px;
	font-size: 34px;
	font-weight: 500;
	height: 90px;
	padding: 0 40px;
	border: none;
	color: #fff;
}

:deep(.van-checkbox__label) {
	font-size: 28px !important;
}

:deep(.van-checkbox__icon) {
	font-size: 40px !important;
}

/* 自定义 van-nav-bar 样式 */
:deep(.van-nav-bar .van-icon) {
	color: #333333;
}
</style>
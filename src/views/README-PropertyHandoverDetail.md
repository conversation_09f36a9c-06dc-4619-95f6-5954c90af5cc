# 物业交割单确认页面 (PropertyHandoverDetail)

## 功能介绍

物业交割单确认页面用于处理租户退场时的物业交割确认流程，支持财务部门和工程部门的分别确认。

## 主要功能

### 1. 基础信息展示
- 承租方信息显示
- 合同号显示
- 交割日期展示

### 2. 部门切换
- 综合或财务部确认：负责水电费情况录入和确认
- 工程或客服部确认：负责资产状态检查和确认
- 不同部门看到不同的字段和确认状态

### 3. 房源交割确认
- 支持多房源同时处理
- 每个房源包含以下检查项：
  - 门、窗、墙体及其他情况
  - 钥匙交接情况
  - 清洁卫生情况
  - 各类资产设备状态

### 4. 状态管理
- 完好：无需赔偿
- 损坏：需要赔偿金
- 丢失：需要赔偿金

### 5. 赔偿金计算
- 自动计算选中房源的总赔偿金额
- 支持千分位格式显示
- 实时更新合计金额

### 6. 批量操作
- 支持全选/取消全选房源
- 批量确认选中的房源

### 7. 电子签字
- 集成手写签名功能
- 支持触摸和鼠标签字
- 自动图片压缩和上传
- 签字图片保存到服务器

### 8. 水电费管理（综合或财务部专用）
- 电度数录入
- 冷水度数录入
- 热水度数录入
- 电费欠费录入
- 水费欠费录入
- 物业欠费录入
- 自动记录确认时间和确认人
- 确认状态追踪

## 路由参数

页面接收以下查询参数：

```typescript
{
  contractNo?: string,    // 合同号
  tenant?: string,        // 承租方名称
  exitId?: string,        // 出场单ID（必需）
  tab?: 'finance' | 'engineering',  // 默认标签页
  unchecked?: 'finance' | 'engineering'  // 未确认类型
}
```

## API接口

### 获取物业交割详情
```typescript
GET /exit/propertyDetail
参数：
- exitId: string (出场单ID)
- confirmType: number (确认类型: 1-财务确认, 2-工程确认)
```

### 保存物业签字
```typescript
POST /exit/savePropertySign
请求体: ExitPropertySignDTO
```

## 使用示例

### 路由跳转
```typescript
// 跳转到财务确认页面
router.push({
  path: '/property-handover-detail',
  query: {
    exitId: '123',
    contractNo: 'HT202501001',
    tenant: '某某公司',
    tab: 'finance'
  }
})

// 跳转到工程确认页面
router.push({
  path: '/property-handover-detail',
  query: {
    exitId: '123',
    contractNo: 'HT202501001', 
    tenant: '某某公司',
    tab: 'engineering'
  }
})
```

### 数据结构
```typescript
// 房源数据结构
interface ExitRoomVo {
  id: string
  roomName: string
  exitDate: string
  doorWindowStatus: number    // 1-完好, 2-损坏
  doorWindowPenalty: number   // 门窗赔偿金
  keyHandoverStatus: number   // 1-已交齐, 2-未交齐
  keyPenalty: number         // 钥匙赔偿金
  cleaningStatus: number     // 1-洁净, 2-需保洁
  cleaningPenalty: number    // 保洁费
  exitRoomAssetsList: ExitRoomAssetsVo[]  // 资产列表
}

// 资产数据结构
interface ExitRoomAssetsVo {
  id: string
  name: string              // 资产名称
  specification?: string    // 规格
  status: number           // 1-完好, 2-损坏, 3-丢失
  penalty: number          // 赔偿金
}
```

## 注意事项

1. **必需参数**: exitId是必需的路由参数，用于获取交割详情
2. **权限控制**: 不同部门只能确认对应的业务范围
3. **数据验证**: 提交前会验证是否选择了房源
4. **防重复提交**: 提交时会禁用按钮防止重复操作
5. **错误处理**: 网络请求失败时会显示错误提示
6. **状态同步**: 切换标签页时会重新获取对应部门的数据

## 开发说明

- 使用 Vue 3 Composition API
- 基于 Vant 4 UI组件库
- 支持TypeScript类型检查
- 响应式数据管理
- 自动计算合计金额
- 千分位数字格式化
- 集成Canvas签字组件
- 自动图片上传和压缩

## 签字功能说明

### 签字流程
1. 用户点击"签字确认"按钮
2. 弹出签字画布
3. 用户手写签名
4. 自动压缩签名图片
5. 上传到服务器获取URL
6. 提交物业交割确认

### 技术实现
- **SignatureCanvas组件**: 自定义Canvas签字组件
- **图片压缩**: 使用Canvas API压缩图片
- **上传接口**: `/business-rent-rest/common/upload`
- **文件格式**: PNG格式，支持透明背景
- **压缩参数**: 质量0.8，最大800x600像素

### 使用示例
```typescript
// 启动签字流程
const showSignatureDialog = () => {
  showSignature.value = true
}

// 处理签字完成
const onSignatureConfirm = async (imageUrl: string) => {
  // 根据部门类型构造不同的数据
  const roomData = {
    // 基础数据
    doorWindowStatus: room.doorWindowStatus,
    // ...其他基础字段
    
    // 财务部门专用字段
    ...(confirmType === 1 && {
      elecMeterReading: room.elecMeterReading,
      coldWaterReading: room.coldWaterReading,
      hotWaterReading: room.hotWaterReading,
      elecFee: room.elecFee,
      waterFee: room.waterFee,
      pmFee: room.pmFee,
      isFinanceConfirmed: true,
      financeConfirmSignature: imageUrl
    }),
    
    // 工程部门专用字段
    ...(confirmType === 2 && {
      isEngineeringConfirmed: true,
      engineeringConfirmSignature: imageUrl
    })
  }
  
  await savePropertySign(signData)
}
```

## 字段说明

### 综合或财务部字段
| 字段名 | 类型 | 必填 | 说明 |
|-------|------|------|------|
| elecMeterReading | number | 是 | 电度数 |
| coldWaterReading | number | 是 | 冷水度数 |
| hotWaterReading | number | 是 | 热水度数 |
| elecFee | number | 是 | 电费欠费 |
| waterFee | number | 是 | 水费欠费 |
| pmFee | number | 是 | 物业欠费 |
| isFinanceConfirmed | boolean | 否 | 财务确认状态 |
| financeConfirmTime | string | 否 | 财务确认时间 |
| financeConfirmBy | string | 否 | 财务确认人OA |
| financeConfirmByName | string | 否 | 财务确认人姓名 |
| financeConfirmSignature | string | 否 | 财务部签名图片URL |

### 工程或客服部字段
| 字段名 | 类型 | 必填 | 说明 |
|-------|------|------|------|
| isEngineeringConfirmed | boolean | 否 | 工程确认状态 |
| engineeringConfirmTime | string | 否 | 工程确认时间 |
| engineeringConfirmBy | string | 否 | 工程确认人OA |
| engineeringConfirmByName | string | 否 | 工程确认人姓名 |
| engineeringConfirmSignature | string | 否 | 工程部签名图片URL | 
<script lang="ts">
export default {
  name: 'EntryDetail'
}
</script>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { getEnterDetail } from '../api/entry'
import { showToast, showLoadingToast } from 'vant'

const router = useRouter()
const route = useRoute()

// 页面数据
const loading = ref(false)
const entryDetail = ref<any>(null)
const basicInfo = ref({
    contractNo: '',
    contractPurpose: '',
    rentPeriod: '',
    tenantName: '',
    createTime: '',
    createByName: '',
    status: ''
})
const roomList = ref<any[]>([])
const expandedRooms = ref<string[]>([])

// 获取进场详情
const fetchEntryDetail = async () => {
    const entryId = route.query.entryId as string
    if (!entryId) {
        showToast('缺少进场单ID')
        return
    }

    try {
        loading.value = true
        const loadingToast = showLoadingToast({
            message: '加载中...',
            forbidClick: true,
        })

        const response = await getEnterDetail(entryId)
        
        if (response.code === 200 && response.data) {
            entryDetail.value = response.data
            setDetailData(response.data)
        } else {
            showToast(response.msg || '获取详情失败')
        }
        
        loadingToast.close()
    } catch (error) {
        console.error('获取进场详情失败:', error)
        showToast('获取详情失败')
    } finally {
        loading.value = false
    }
}

// 设置详情数据
const setDetailData = (data: any) => {
    const enterData = data.enter || data
    const contract = data.contract || {}
    
    // 设置基本信息
    basicInfo.value = {
        contractNo: contract.contractNo || enterData.contractNo || '',
        contractPurpose: getDictLabel('diversification_purpose', (contract.contractPurpose || enterData.contractPurpose)?.toString()) || '',
        rentPeriod: contract.startDate && contract.endDate 
            ? `${contract.startDate} 至 ${contract.endDate}`
            : enterData.rentStartDate && enterData.rentEndDate
            ? `${enterData.rentStartDate} 至 ${enterData.rentEndDate}`
            : '',
        tenantName: contract.customerName || enterData.tenantName || '',
        createTime: formatDateTime(enterData.createTime || ''),
        createByName: enterData.createByName || '',
        status: getStatusText(enterData)
    }
    
    // 设置房源列表
    roomList.value = data.enterRoomList || []
    
    // 默认展开第一个房间
    if (roomList.value.length > 0) {
        expandedRooms.value = [roomList.value[0].roomId]
    }
}

// 获取字典标签文本
const getDictLabel = (dictType: string, value: string): string => {
    const dictMap: Record<string, Record<string, string>> = {
        'diversification_purpose': {
            '1': '办公',
            '2': '商业', 
            '3': '仓储',
            '4': '其他'
        }
    }
    return dictMap[dictType]?.[value] || value
}

// 获取状态文本
const getStatusText = (enterData: any): string => {
    // 根据业务逻辑判断状态
    const totalRooms = roomList.value.length
    const enteredRooms = roomList.value.filter(room => room.enterDate).length
    
    if (enteredRooms === 0) {
        return '待办理'
    } else if (enteredRooms === totalRooms) {
        return '已办理'
    } else {
        return '部分办理'
    }
}

// 格式化日期时间
const formatDateTime = (dateStr: string): string => {
    if (!dateStr) return ''
    const date = new Date(dateStr)
    return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
    })
}

// 格式化日期
const formatDate = (dateStr: string): string => {
    if (!dateStr) return ''
    return dateStr.split('T')[0]
}

// 切换房间展开状态
const toggleRoomExpand = (roomId: string) => {
    const index = expandedRooms.value.indexOf(roomId)
    if (index > -1) {
        expandedRooms.value.splice(index, 1)
    } else {
        expandedRooms.value.push(roomId)
    }
}

// 获取资产状态文本
const getAssetStatusText = (isMissing: boolean): string => {
    return isMissing ? '缺失' : '完好'
}

// 获取资产状态类名
const getAssetStatusClass = (isMissing: boolean): string => {
    return isMissing ? 'missing' : 'good'
}

// 返回上一页
const goBack = () => {
    router.back()
}

// 页面挂载
onMounted(() => {
    fetchEntryDetail()
})
</script>

<template>
    <div class="entry-detail">
        <!-- 导航栏 -->
        <van-nav-bar title="进场详情" left-arrow fixed placeholder @click-left="goBack" />

        <!-- 加载状态 -->
        <van-loading v-if="loading" class="loading-center" size="24px" vertical>
            加载中...
        </van-loading>

        <!-- 内容区域 -->
        <div v-else-if="entryDetail" class="content-area">
            <!-- 基本信息 -->
            <div class="basic-info-section">
                <div class="basic-info-card">
                    <div class="info-content">
                        <div class="info-row">
                            <span class="info-label">合同编号：</span>
                            <span class="info-value">{{ basicInfo.contractNo }}</span>
                        </div>
                        <div class="info-row">
                            <span class="info-label">承租方：</span>
                            <span class="info-value">{{ basicInfo.tenantName }}</span>
                        </div>
                        <div class="info-row">
                            <span class="info-label">租期：</span>
                            <span class="info-value">{{ basicInfo.rentPeriod }}</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 进场房源 -->
            <div class="rooms-section">
                <div class="section-title">
                    <img src="../assets/images/house-icon.svg" alt="房源" class="title-icon" />
                    进场房源 · {{ roomList.length }}间
                </div>

                <!-- 房源列表 -->
                <div class="room-list">
                    <div v-for="(room, roomIndex) in roomList" :key="room.roomId" class="room-card">
                        <!-- 房间头部 -->
                        <div class="room-header" @click="toggleRoomExpand(room.roomId)">
                            <div class="room-info">
                                <div class="room-name-row">
                                    <img class="house-icon" src="../assets/images/house-icon.svg" alt="房源" />
                                    <span class="room-name">{{ room.roomName }}</span>
                                </div>
                                <div class="room-date">进场日期：{{ formatDate(room.enterDate || '') || '未设置' }}</div>
                            </div>
                            <van-icon 
                                :name="expandedRooms.includes(room.roomId) ? 'arrow-up' : 'arrow-down'" 
                                class="expand-icon" 
                            />
                        </div>

                        <!-- 房间详情 -->
                        <div v-if="expandedRooms.includes(room.roomId)" class="room-content">
                            <!-- 基本信息 -->
                            <div class="room-basic-info">
                                <div class="info-row">
                                    <span class="info-label">楼栋:</span>
                                    <span class="info-value">{{ room.buildingName || '-' }}</span>
                                </div>
                                <div class="info-row">
                                    <span class="info-label">地块:</span>
                                    <span class="info-value">{{ room.parcelName || '-' }}</span>
                                </div>
                                <div class="info-row">
                                    <span class="info-label">进场日期:</span>
                                    <span class="info-value">{{ formatDate(room.enterDate || '') || '未设置' }}</span>
                                </div>
                            </div>

                            <!-- 房间配套情况 -->
                            <div class="assets-section">
                                <div class="section-title-blue">房间配套情况</div>
                                
                                <div v-if="!room.assetList || room.assetList.length === 0" class="empty-assets">
                                    暂无配套设施
                                </div>
                                
                                <div v-for="(asset, assetIndex) in room.assetList" :key="assetIndex" class="asset-item">
                                    <div class="asset-info">
                                        <span class="asset-name">{{ asset.name }}{{ asset.specification ? `(${asset.specification})` : '' }}</span>
                                        <div class="asset-details">
                                            <span class="asset-count">x{{ asset.count || 1 }}</span>
                                            <div class="switch-container">
                                                <span class="switch-label">{{ !asset.isMissing ? '齐全' : '缺失' }}</span>
                                                <van-switch 
                                                    v-model="asset.isMissing"
                                                    disabled
                                                    size="20"
                                                    active-color="#ff4444"
                                                    inactive-color="#52c41a"
                                                />
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 水电度数 -->
                            <div class="utility-section" >
                                <div class="section-title-blue">水电度数记录</div>
                                
                                <div v-if="room.elecMeterReading" class="utility-item">
                                    <span class="utility-label">电表读数:</span>
                                    <span class="utility-value">{{ room.elecMeterReading }} 度</span>
                                </div>
                                
                                <div v-if="room.coldWaterReading" class="utility-item">
                                    <span class="utility-label">冷水表读数:</span>
                                    <span class="utility-value">{{ room.coldWaterReading }} 吨</span>
                                </div>
                                
                                <div v-if="room.hotWaterReading" class="utility-item">
                                    <span class="utility-label">热水表读数:</span>
                                    <span class="utility-value">{{ room.hotWaterReading }} 吨</span>
                                </div>
                            </div>

                            <!-- 备注 -->
                            <div class="remark-section" v-if="room.remark">
                                <div class="section-title-blue">备注</div>
                                <div class="remark-content">{{ room.remark }}</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 空状态 -->
        <van-empty v-else description="暂无详情数据" />
    </div>
</template>

<style scoped>
.entry-detail {
    min-height: 100vh;
    background-color: #F1F1F1;
}

/* 加载状态 */
.loading-center {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 200px;
    flex-direction: column;
}

/* 内容区域 */
.content-area {
    padding-bottom: 20px;
}

/* 基本信息区域 */
.basic-info-section {
    margin: 16px;
    overflow: hidden;
}

.basic-info-card {
    background: linear-gradient(135deg, #3583FF 0%, #1677FF 100%); /* 蓝色渐变背景 */
    /* 预留背景图片位置
    background-image: url(''); 
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    */
    border-radius: 12px;
    padding: 30px 20px;
    color: #FFFFFF; /* 白色文字 */
    position: relative;
    overflow: hidden;
    box-shadow: 0 4px 16px rgba(53, 131, 255, 0.3);
}

.info-content {
    position: relative;
    z-index: 2;
}

.info-row {
    display: flex;
    align-items: center;
    margin-bottom: 16px;
    font-size: 28px;
    line-height: 1.4;
}

.info-row:last-child {
    margin-bottom: 0;
}

.info-label {
    font-weight: 400;
    color: rgba(255, 255, 255, 0.9);
    min-width: 120px;
}

.info-value {
    font-weight: 500;
    color: #FFFFFF;
    flex: 1;
}

.title-icon {
    width: 32px;
    height: 32px;
}

.section-title {
    font-size: 32px;
    font-weight: 600;
    color: #242433;
    padding: 20px 30px;
    border-bottom: 1px solid #E8EBFF;
    display: flex;
    align-items: center;
    gap: 10px;
    background-color: #FFFFFF;
    border-radius: 12px 12px 0 0;
}

/* 房源区域 */
.rooms-section {
    margin: 0 16px 16px;
}

.room-list {
    margin-top: 16px;
}

.room-card {
    background-color: #FFFFFF;
    border-radius: 12px;
    margin-bottom: 16px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
    overflow: hidden;
}

/* 房间头部 */
.room-header {
    display: flex;
    align-items: center;
    padding: 16px;
    cursor: pointer;
    border-bottom: 1px solid #F5F5F5;
}

.room-info {
    flex: 1;
}

.room-name-row {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
}

.house-icon {
    width: 30px;
    height: 30px;
    margin-right: 16px;
}

.room-name {
    font-size: 28px;
    font-weight: 500;
    color: #242433;
}

.room-date {
    font-size: 24px;
    color: #919199;
}

.expand-icon {
    color: #C8C9CC;
    font-size: 16px;
}

/* 房间内容 */
.room-content {
    padding: 0 16px 16px;
}

/* 房间基本信息 */
.room-basic-info {
    padding: 16px 0;
    border-bottom: 1px solid #F5F5F5;
    margin-bottom: 16px;
}

/* 蓝色标题样式 */
.section-title-blue {
    font-size: 28px;
    font-weight: 500;
    color: #1677FF;
    padding: 22px 0 22px 16px;
    border-bottom: 1px solid #E8EBFF;
}

/* 空状态样式 */
.empty-assets {
    text-align: center;
    color: #C8C9CC;
    font-size: 14px;
    padding: 20px 0;
}

/* 资产项目样式 */
.asset-item {
    padding: 16px 0;
    border-bottom: 1px solid #F5F5F5;
}

.asset-item:last-child {
    border-bottom: none;
}

.asset-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.asset-name {
    font-size: 28px;
    color: #242433;
    line-height: 1.4em;
    flex: 1;
}

.asset-details {
    display: flex;
    align-items: center;
    gap: 16px;
    flex-shrink: 0;
}

.asset-count {
    font-size: 24px;
    color: #666;
}

.status-tag {
    font-size: 24px;
    font-weight: 500;
    border: none;
    min-width: 60px;
    text-align: center;
}

/* 水电度数区域 */
.utility-section {
    margin-bottom: 16px;
}

.utility-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    background-color: #f8f9fa;
    margin-bottom: 8px;
    border-radius: 8px;
}

.utility-item:last-child {
    margin-bottom: 0;
}

.utility-label {
    font-size: 28px;
    color: #666;
}

.utility-value {
    font-size: 28px;
    color: #333;
    font-weight: 500;
}

/* 备注区域 */
.remark-section {
    margin-bottom: 16px;
}

.remark-content {
    padding: 16px;
    background-color: #f8f9fa;
    border-radius: 8px;
    font-size: 28px;
    color: #333;
    line-height: 1.5;
}

/* 开关容器样式 */
.switch-container {
    display: flex;
    align-items: center;
    gap: 12px;
}

.switch-label {
    font-size: 26px;
    font-weight: 500;
    color: #242433;
    min-width: 60px;
}
</style>
<template>
    <div class="exit-management-page">
        <!-- 顶部导航 -->
        <van-nav-bar title="出场管理" left-arrow @click-left="onClickLeft" class="custom-nav-bar" />

        <!-- 搜索栏 -->
        <div class="search-section">
            <van-search v-model="searchValue" placeholder="请输入楼栋/房源/承租方" @search="handleSearch" @clear="resetList"
                class="custom-search" />
        </div>

        <!-- 状态标签栏 -->
        <div class="status-tabs">
            <div v-for="(tab, index) in statusTabs" :key="index" class="status-tab"
                :class="{ active: activeTab === index }" @click="handleTabChange(index)">
                <span class="tab-text">{{ tab.label }}</span>
            </div>
            <div class="time-filter">
                <van-dropdown-menu class="time-dropdown">
                    <van-dropdown-item v-model="timeFilterValue" :options="timeFilterOptions"
                        @change="handleTimeFilterChange" />
                </van-dropdown-menu>
            </div>
        </div>

        <!-- 筛选栏 -->
        <!-- <div class="filter-section">
            <div class="filter-right">
                <span class="more-filter" @click="showMoreFilter">
                    <span class="more-filter-text">更多筛选</span>
                    <img src="@/assets/images/filter.png" class="more-filter-img" />
                </span>
            </div>
        </div> -->

        <!-- 列表内容 -->
        <div class="list-content-scroll" >
            <div class="list-content" v-if="exitList.length > 0">
                <div v-for="item in exitList" :key="item.id" class="exit-item" @click="goToDetail(item)">
                    <!-- 标题栏 -->
                    <moduleTitle :title="item.customerName" />

                    <!-- 信息区域 -->
                    <div class="item-content">
                        <div class="info-section">
                            <div class="info-item">
                                <span class="info-label">合同编号：</span>
                                <span class="info-value">{{ item.contractNo }}</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">退租日期：</span>
                                <span class="info-value">{{ item.terminateDate || '待确定' }}</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">退租房源数：</span>
                                <span class="info-value">{{ item.terminateRoomCount || 0 }}</span>
                            </div>
                            <div class="info-item" v-if="activeTab === 1">
                                <span class="info-label">办理进度：</span>
                                <span class="info-value">{{ item.progressStatusName || '' }}</span>
                            </div>
                            <div class="info-item info-handle">
                                <!-- 办理进度: 0-不可见, 1-待办理, 5-物业交割, 10-费用结算, 15-交割并结算, 20-客户签字, 25-发起退款, 30-已完成, 40-已作废 -->
                                <van-button type="primary" size="small" round @click.stop="handleExit(item)"
                                    class="action-btn" v-if="item.progressStatus === 1">
                                    办理出场
                                </van-button>
                                <!-- <a-button type="text" size="mini" @click="handleContinueExit(record)" v-if="record.progressStatus === 5 || record.progressStatus === 10 || record.progressStatus === 15 || record.progressStatus === 20"> -->
                                    <van-button type="default" size="small" @click="handleCancelExit(item)" class="action-btn" v-if="item.progressStatus === 5 || item.progressStatus === 15">
                                        作废
                                    </van-button>       
                                <van-button type="primary" size="small" round @click.stop="handleContinueExit(item)"
                                    class="action-btn" v-if="item.progressStatus === 5 || item.progressStatus === 10 || item.progressStatus === 15 || item.progressStatus === 20">  
                                    继续办理
                                </van-button>

                            </div>
                        </div>
                    </div>
                </div>
            </div>
                    <!-- 空状态 -->
            <div class="list-content" v-else>
                <div class="empty-content">
                    <van-empty description="暂无数据" />
                </div>
            </div>


        <!-- 加载更多 -->
        <van-list v-model:loading="loading" :finished="finished" finished-text="没有更多了" @load="fetchExitList"
            v-if="exitList.length > 0" />

        </div>


        

        <!-- 更多筛选弹框 -->
        <van-popup v-model:show="showFilterPopup" position="bottom" :style="{ height: '70%' }" round
            class="filter-popup">
            <div class="filter-content">
                <div class="filter-header">
                    <h3 class="filter-title">更多筛选</h3>
                    <van-icon name="cross" @click="closeFilterPopup" class="close-icon" />
                </div>

                <div class="filter-form">
                    <!-- 退租日期 -->
                    <van-cell-group>
                        <van-field readonly clickable label="退租日期" v-model="exitDateRangeText" placeholder="请选择退租日期区间"
                            @click="showExitDatePicker = true" />
                    </van-cell-group>

                    <!-- 创建日期 -->
                    <van-cell-group>
                        <van-field readonly clickable label="创建日期" v-model="createTimeRangeText" placeholder="请选择创建日期区间"
                            @click="showCreateTimePicker = true" />
                    </van-cell-group>

                    <!-- 合同编号 -->
                    <van-cell-group>
                        <van-field v-model="filterForm.contractNo" label="合同编号" placeholder="请输入合同编号" clearable />
                    </van-cell-group>

                    <!-- 创建人 -->
                    <van-cell-group>
                        <van-field v-model="filterForm.createByName" label="创建人" placeholder="请输入创建人姓名" clearable />
                    </van-cell-group>
                </div>

                <!-- 退租日期选择器 -->
                <van-popup v-model:show="showExitDatePicker" position="bottom">
                    <van-picker-group title="退租日期" :tabs="['开始日期', '结束日期']" @confirm="onExitDateConfirm"
                        @cancel="onExitDateCancel">
                        <van-date-picker v-model="exitStartDate" :min-date="minDate" :max="maxDate" />
                        <van-date-picker v-model="exitEndDate" :min-date="exitEndMinDate" :max="maxDate" />
                    </van-picker-group>
                </van-popup>

                <!-- 创建日期选择器 -->
                <van-popup v-model:show="showCreateTimePicker" position="bottom">
                    <van-picker-group title="创建日期" :tabs="['开始日期', '结束日期']" @confirm="onCreateTimeConfirm"
                        @cancel="onCreateTimeCancel">
                        <van-date-picker v-model="createStartDate" :min-date="minDate" :max-date="maxDate" />
                        <van-date-picker v-model="createEndDate" :min-date="createEndMinDate" :max-date="maxDate" />
                    </van-picker-group>
                </van-popup>

                <div class="filter-actions">
                    <van-button class="reset-btn" @click="resetFilter">重置</van-button>
                    <van-button type="primary" class="confirm-btn" @click="applyFilter">确定</van-button>
                </div>
            </div>
        </van-popup>

        <!-- 出场提示弹窗 -->
        <van-dialog
            v-model:show="showExitDialog"
            :show-cancel-button="false"
            :show-confirm-button="false"
            class-name="exit-dialog"
            title="出场提示"
            close-on-click-overlay
        >
            <div class="exit-dialog-content">
                <!-- 提示内容 -->
                <div class="dialog-tips">
                    <div class="tip-item">1、房源可分批次做出场物业交割，但必须所有退租房源统一结算</div>
                    <div class="tip-item">2、物业交割单必须商服和物业全都确认，结算单才能发给客户签字</div>
                </div>
                
                <!-- 操作按钮 -->
                <div class="dialog-actions">
                    <van-button 
                        type="default" 
                        class="action-button secondary-button"
                        :loading="processLoading"
                        @click="handlePropertyFirst"
                    >
                        先物业交割<br/>后续结算
                    </van-button>
                    
                    <van-button 
                        type="primary" 
                        class="action-button primary-button"
                        :loading="processLoading"
                        @click="handlePropertyAndSettlement"
                    >
                        物业交割并结算
                    </van-button>
                </div>
            </div>
        </van-dialog>
        <exitHandling :exitData="exitData" />
    </div>
</template>

<script lang="ts">
export default {
  name: 'ExitManagement'
}
</script>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { showToast, showConfirmDialog, showLoadingToast, closeToast } from 'vant'
import { useRouter } from 'vue-router'
import moduleTitle from '@/components/ModuleTitle.vue'
import { getExitListB , getExitDetail, chooseExitProcessType, cancelExit, type ExitQueryDTO, type ExitVo, type ExitDetailVo, type ChooseExitProcessTypeDTO } from '../api/exit'

// 扩展ExitVo类型以支持前端添加的字段
interface ExtendedExitVo extends ExitVo {
    status?: number
    statusName?: string
    progressStatusName?: string
}


const router = useRouter()

// 搜索相关
const searchValue = ref('')
const pageNum = ref(1)
const pageSize = ref(10)

// 状态标签
const activeTab = ref(0)
const statusTabs = ref([
    { label: '待办理', count: 0, searchType: 0 },
    { label: '办理中', count: 0, searchType: 1 },
    { label: '已办理', count: 0, searchType: 2 }
])

// 时间筛选
const timeFilterValue = ref('全部')
const timeFilterOptions = [
    { text: '近3天', value: '近3天' },
    { text: '近7天', value: '近7天' },
    { text: '近30天', value: '近30天' },
    { text: '全部', value: '全部' }
]

// 列表数据
const exitList = ref<ExtendedExitVo[]>([])
const loading = ref(false)
const finished = ref(false)

// 出场提示弹窗状态
const showExitDialog = ref(false)
const currentExitItem = ref<ExtendedExitVo | null>(null)

// 更多筛选弹框相关
const showFilterPopup = ref(false)
const filterForm = ref({
    exitDateStart: '',     // 退租日期开始
    exitDateEnd: '',       // 退租日期结束
    createTimeStart: '',   // 创建日期开始
    createTimeEnd: '',     // 创建日期结束
    createByName: '',      // 创建人
    contractNo: ''         // 合同编号
})

// 日期区间和最小最大值限制
const minDate = new Date(2020, 0, 1)
const maxDate = new Date(2030, 11, 31)

// 日期选择器相关
const showExitDatePicker = ref(false)
const showCreateTimePicker = ref(false)

// 退租日期选择
const exitStartDate = ref(['2024', '01', '01'])
const exitEndDate = ref(['2024', '12', '31'])
const exitEndMinDate = computed(() =>
    new Date(
        Number(exitStartDate.value[0]),
        Number(exitStartDate.value[1]) - 1,
        Number(exitStartDate.value[2])
    )
)

// 创建日期选择
const createStartDate = ref(['2024', '01', '01'])
const createEndDate = ref(['2024', '12', '31'])
const createEndMinDate = computed(() =>
    new Date(
        Number(createStartDate.value[0]),
        Number(createStartDate.value[1]) - 1,
        Number(createStartDate.value[2])
    )
)

// 计算属性：日期区间显示文本
const exitDateRangeText = ref('')
const createTimeRangeText = ref('')

// 获取办理进度状态文本
const getProgressStatusText = (status: number) => {
    // 办理进度: 0-不可见, 1-待办理, 5-物业交割, 10-费用结算, 15-交割并结算, 20-客户签字, 25-发起退款, 30-已完成, 40-已作废
    switch (status) {
        case 0:
            return '不可见'
        case 1:
            return '待办理'
        case 5:
            return '物业交割' 
        case 10:
            return '费用结算'
        case 15:
            return '交割并结算'
        case 20:
            return '客户签字'
        case 25:
            return '发起退款'
        case 30:
            return '已完成'
        case 40:
            return '已作废'
        default:
            return '未知'
    }
}

// 获取列表数据
const fetchExitList = async () => {
    try {
        loading.value = true
        const params: ExitQueryDTO = {
            pageNum: pageNum.value,
            pageSize: pageSize.value,
            status: statusTabs.value[activeTab.value].searchType, // 0-待办理,1-办理中,2-已办理
            buildingOrRoomOrTenantName: searchValue.value || undefined, // 楼栋/房源/承租方
            projectId: '01dd0aa7-57c6-40ca-83ab-d04e01835e5c', // 默认项目ID
            // 添加筛选条件
            terminateStartDate: filterForm.value.exitDateStart || undefined,
            terminateEndDate: filterForm.value.exitDateEnd || undefined,
            createByName: filterForm.value.createByName || undefined,
            contractNo: filterForm.value.contractNo || undefined,
        }

        // 添加时间筛选逻辑
        if (timeFilterValue.value !== '全部') {
            const now = new Date()
            const daysMap = { '近3天': 3, '近7天': 7, '近30天': 30 }
            const days = daysMap[timeFilterValue.value as keyof typeof daysMap]
            if (days) {
                params.nearDays = days
            }
        }

        const res = await getExitListB(params)
        console.log('出场管理列表:', res)
        if (res.code === 200 && res.rows) {
            const newItems = Array.isArray(res.rows) ? res.rows : []
            const extendedItems: ExtendedExitVo[] = newItems.map((item: ExitVo) => {
                // 根据progressStatus判断状态
                const status = item.progressStatus // 0-待办理 1-办理中 2-已办理
                const statusName = status === 0 ? '待办理' : status === 1 ? '办理中' : '已办理'
                return { ...item, status, statusName, progressStatusName: getProgressStatusText(item.progressStatus) }
            })
            exitList.value = [...exitList.value, ...extendedItems]
            pageNum.value++
            finished.value = newItems.length < pageSize.value
        }
    } catch (error) {
        console.error('获取出场管理列表失败:', error)
        showToast('获取出场管理列表失败')
    } finally {
        loading.value = false
    }
}
 
// 重置列表
const resetList = () => {
    pageNum.value = 1
    exitList.value = []
    finished.value = false
    fetchExitList()
}
let exitData = ref({})

// 搜索
const handleSearch = () => {
    resetList()
}

// 切换状态标签
const handleTabChange = (index: number) => {
    activeTab.value = index
    resetList()
}

// 时间筛选变化
const handleTimeFilterChange = () => {
    resetList()
}

// 返回上一页
const onClickLeft = () => {
    router.back()
}

// 办理出场
const handleExit = async (item: ExtendedExitVo) => {
    currentExitItem.value = item
    showExitDialog.value = true
    // try {
    //     showLoadingToast({
    //         message: '选择办理类型...',
    //         duration: 0
    //     })

    //     // 先选择办理类型
    //     await chooseExitProcessType({
    //         exitId: item.id,
    //         processType: 2 // 默认选择2-交割并结算
    //     })

    //     closeToast()
        
    //     // 跳转到出场办理页面
    //     router.push({
    //         name: 'ExitProcess',
    //         params: { id: item.id },
    //         query: { 
    //             contractUnionId: item.contractUnionId,
    //             contractId: item.contractId
    //         }
    //     })
    // } catch (error) {
    //     closeToast()
    //     console.error('办理出场失败:', error)
    //     showToast('办理出场失败，请重试')
    // }
}

// 继续办理
const handleContinueExit = async (item: ExtendedExitVo) => {
    router.push({
        name: 'ExitHandling',
        params: { id: item.id }
    })
    // exitMode.value = item.processType === 1 ? 'property-only' : 'property-and-settlement'
    // let res = await getExitDetail(item.id)
    // exitData.value = res.data
    // console.log('继续办理:', res)
    // currentExitItem.value = item
    // showExitDialog.value = true
}
const handleCancelExit = async (item: ExtendedExitVo) => {
    // console.log('作废:', item)
    
    try {
        // 获取出场单详情
        await showConfirmDialog({
            title: '确认提示',
            message: `确认作废该出场单吗？`,
            confirmButtonText: '确认',
            cancelButtonText: '取消'
        })


    } catch (error) {
        console.error('获取出场详情失败:', error)
        // showToast('获取出场详情失败')
        return
    }
    const res = await cancelExit({exitId: item.id})
        
        if (res.code === 200) {
            showToast('已作废')
            resetList()
            
            // 跳转到出场办理页面，传递详情数据和编辑模式
            // router.push({
            //     name: 'ExitProcess',
            //     query: {
            //         exitId: item.id,
            //         contractId: item.contractId,
            //         contractNo: item.contractNo,
            //         customerName: item.customerName,
            //         mode: 'edit' // 标识为编辑模式
            //     },
            //     state: {
            //         exitData: res.data as any // 通过路由状态传递详情数据
            //     }
            // })
        }
}

// 调整出场信息
const handleAdjust = async (item: ExtendedExitVo) => {
    try {
        // 获取出场单详情
        const res = await getExitDetail(item.id)
        
        if (res.code === 200 && res.data) {
            showToast('加载详情成功')
            
            // 跳转到出场办理页面，传递详情数据和编辑模式
            router.push({
                name: 'ExitProcess',
                query: {
                    exitId: item.id,
                    contractId: item.contractId,
                    contractNo: item.contractNo,
                    customerName: item.customerName,
                    mode: 'edit' // 标识为编辑模式
                },
                state: {
                    exitData: res.data as any // 通过路由状态传递详情数据
                }
            })
        }
    } catch (error) {
        console.error('获取出场详情失败:', error)
        showToast('获取出场详情失败')
    }
}

// 查看详情
const handleViewDetail = (item: ExtendedExitVo) => {
    router.push({
        name: 'ExitDetail',
        query: {
            exitId: item.id,
            contractId: item.contractId,
            contractNo: item.contractNo,
            customerName: item.customerName
        }
    })
}

// 处理办理类型选择的Loading状态
const processLoading = ref(false)

// 调用选择办理类型接口
const handleChooseProcessType = async (processType: number) => {
    if (!currentExitItem.value?.id) {
        showToast('出场单ID不能为空')
        return false
    }

    try {
        processLoading.value = true
        await chooseExitProcessType({
            exitId: currentExitItem.value.id,
            processType
        })
        
        showToast('办理类型选择成功')
        // 刷新列表数据
        resetList()
        return true
    } catch (error) {
        console.error('选择办理类型失败:', error)
        showToast('选择办理类型失败')
        return false
    } finally {
        processLoading.value = false
    }
}

// 先物业交割，后续结算
const handlePropertyFirst = async () => {
    const success = await handleChooseProcessType(1) // 1-先交割后结算
    if (success) {
        showExitDialog.value = false
        router.push({
        name: 'ExitHandling',
        params: { id: currentExitItem.value?.id }
    })
    }
}

// 物业交割并结算
const handlePropertyAndSettlement = async () => {
    const success = await handleChooseProcessType(2) // 2-交割并结算
    if (success) {
        showExitDialog.value = false
        router.push({
        name: 'ExitHandling',
        params: { id: currentExitItem.value?.id }
    })
    }
}

// 取消操作
const handleCancel = () => {
    showExitDialog.value = false
    currentExitItem.value = null
}

// 显示浮动按钮的出场对话框
const showFloatingExitDialog = () => {
    // 如果是待办理状态且有数据，选择第一个项目作为当前项目
    if (activeTab.value === 0 && exitList.value.length > 0) {
        currentExitItem.value = exitList.value[0]
        showExitDialog.value = true
    }
}

// 显示筛选弹框
const showMoreFilter = () => {
    showFilterPopup.value = true
}

// 关闭筛选弹框
const closeFilterPopup = () => {
    showFilterPopup.value = false
}

// 退租日期确认
const onExitDateConfirm = () => {
    filterForm.value.exitDateStart = exitStartDate.value.join('-')
    filterForm.value.exitDateEnd = exitEndDate.value.join('-')
    exitDateRangeText.value = `${filterForm.value.exitDateStart} 至 ${filterForm.value.exitDateEnd}`
    showExitDatePicker.value = false
}

// 退租日期取消
const onExitDateCancel = () => {
    showExitDatePicker.value = false
}

// 创建日期确认
const onCreateTimeConfirm = () => {
    filterForm.value.createTimeStart = createStartDate.value.join('-')
    filterForm.value.createTimeEnd = createEndDate.value.join('-')
    createTimeRangeText.value = `${filterForm.value.createTimeStart} 至 ${filterForm.value.createTimeEnd}`
    showCreateTimePicker.value = false
}

// 创建日期取消
const onCreateTimeCancel = () => {
    showCreateTimePicker.value = false
}

// 应用筛选
const applyFilter = () => {
    showFilterPopup.value = false
    resetList()
}

// 重置筛选
const resetFilter = () => {
    filterForm.value = {
        exitDateStart: '',
        exitDateEnd: '',
        createTimeStart: '',
        createTimeEnd: '',
        createByName: '',
        contractNo: ''
    }
    // 重置日期选择器数据
    exitStartDate.value = ['2024', '01', '01']
    exitEndDate.value = ['2024', '12', '31']
    createStartDate.value = ['2024', '01', '01']
    createEndDate.value = ['2024', '12', '31']
    exitDateRangeText.value = ''
    createTimeRangeText.value = ''
    resetList()
}

// 跳转详情
const goToDetail = (item: ExtendedExitVo) => {
    router.push({
        name: 'ExitDetail',
        query: { contractId: item.contractId, exitId: item.id }
    })
}

// 获取状态徽章类型
const getStatusBadgeClass = (status: number) => {
    return status === 0 ? 'pending' : status === 1 ? 'processing' : 'completed'
}

// 初始化
onMounted(() => {
    // 检查是否有从首页传递的tab索引
    let exitTab = sessionStorage.getItem('exitTab')
    if (exitTab) {
        const tabIndex = parseInt(exitTab)
        if (tabIndex >= 0 && tabIndex < statusTabs.value.length) {
            activeTab.value = tabIndex
        }
        sessionStorage.removeItem('exitTab')
    }
    fetchExitList()
})
</script>

<style lang="less" scoped>
.exit-management-page {
    background-color: #f5f5f5;
    min-height: 100vh;
}

/* 导航栏样式 */
.custom-nav-bar {
    background-color: #fff;
    border-bottom: 1px solid #f0f0f0;
}

/* 搜索栏样式 */
.search-section {
    height: 100px;
    background: #fff;
    box-sizing: border-box;
    overflow: hidden;
    padding: 20px 30px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.custom-search {
    flex: 1;
    padding: 0 !important;
}

.custom-search :deep(.van-search__content) {
    background-color: #f3f3f3;
    border-radius: 30px;
}

/* 状态标签栏 */
.status-tabs {
    height: 100px;
    box-sizing: border-box;
    overflow: hidden;
    display: flex;
    background-color: #fff;
    padding: 0 20px;
    border-bottom: 1px solid #f0f0f0;
    align-items: center;
}

.status-tab {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 24px 0;
    cursor: pointer;
    position: relative;
}

.status-tab.active {
    color: #1890ff;
}

.status-tab.active::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 4px;
    background-color: #1890ff;
    border-radius: 2px;
}

.tab-text {
    font-size: 30px;
    font-weight: 500;
}

.time-filter {
    flex-shrink: 0;
    margin-left: 20px;
}

.time-dropdown {
    background: #f8f9fa;
    border-radius: 20px;
    padding: 8px 16px;
}

.time-dropdown :deep(.van-dropdown-menu__bar) {
    height: auto !important;
    background: transparent;
    border: none;
    padding: 0;
}

.time-dropdown :deep(.van-ellipsis) {
    color: #666 !important;
    font-size: 28px;
}

/* 筛选栏 */
.filter-section {
    height: 100px;
    box-sizing: border-box;
    overflow: hidden;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    padding: 20px;
    background-color: #F1F1F1;
    border-bottom: 1px solid #f0f0f0;
}

.filter-right {
    display: flex;
    align-items: center;
}

.more-filter {
    background: #fff;
    border-radius: 40px;
    padding: 12px 30px;
    color: #000;
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 28px;
    cursor: pointer;

    .more-filter-img {
        width: 24px;
        height: 24px;
    }
}

/* 列表内容 */
.list-content-scroll {
    height: calc(100vh - 300px);
    overflow: hidden;
}
.list-content {
    height: 100%;
    box-sizing: border-box;
    padding: 20px;

    overflow-y: auto;
}

.empty-content {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 400px;
}

.exit-item {
    background-color: #fff;
    border-radius: 12px;
    overflow: hidden;
    margin-bottom: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
    cursor: pointer;
    transition: all 0.2s ease;
}

.exit-item:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    transform: translateY(-1px);
}

.exit-item:active {
    transform: translateY(0);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

/* 信息区域 */
.item-content {
    display: flex;
    padding: 20px;
    background-color: #fff;
}

.info-section {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 12px;

    .info-handle {
        display: flex;
        justify-content: flex-end;
        gap: 12px;
        flex-wrap: wrap;
    }
}

.info-item {
    display: flex;
    align-items: center;
    font-size: 28px;
}

.info-label {
    color: #888;
    flex-shrink: 0;
}

.info-value {
    color: #333;
}

.action-btn {
    min-width: 140px;
    height: 56px;
    line-height: 56px;
    font-size: 28px;
    border-radius: 34px;
}

.detail-btn {
    background: #f8f9fa;
    border-color: #f8f9fa;
    color: #333;
}

/* 筛选弹框样式 */
.filter-popup {
    .filter-content {
        padding: 0;
        background-color: #fff;
    }

    .filter-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 30px 40px;
        border-bottom: 1px solid #f0f0f0;

        .filter-title {
            font-size: 36px;
            font-weight: bold;
            color: #333;
            margin: 0;
        }

        .close-icon {
            font-size: 40px;
            color: #999;
            cursor: pointer;
        }
    }

    .filter-form {
        padding: 0 20px;
        max-height: 50vh;
        overflow-y: auto;

        :deep(.van-cell-group) {
            margin-bottom: 20px;
        }

        :deep(.van-field__label) {
            font-size: 30px;
            color: #333;
            width: 140px;
        }

        :deep(.van-field__value) {
            font-size: 30px;
        }

        :deep(.van-field__control) {
            font-size: 30px;
        }
    }

    .filter-actions {
        display: flex;
        gap: 20px;
        padding: 30px 40px;
        border-top: 1px solid #f0f0f0;
        background-color: #fff;

        .reset-btn,
        .confirm-btn {
            flex: 1;
            height: 80px;
            font-size: 32px;
            border-radius: 8px;
        }

        .reset-btn {
            background-color: #f8f9fa;
            border-color: #e9ecef;
            color: #6c757d;
        }

        .confirm-btn {
            background-color: #1890ff;
            border-color: #1890ff;
        }
    }
}

/* 出场提示弹窗样式 */
:deep(.exit-dialog) {
    border-radius: 16px;
    overflow: hidden;
    width: 90vw;
    max-width: 600px;
}

:deep(.exit-dialog .van-dialog__content) {
    padding: 0;
}

:deep(.exit-dialog .van-dialog__header) {
    padding: 32px 24px 16px;
    text-align: center;
    border-bottom: none;
}

:deep(.exit-dialog .van-dialog__title) {
    font-size: 36px;
    font-weight: 600;
    color: #333;
}

.exit-dialog-content {
    padding: 0 24px 32px;
}

.dialog-tips {
    margin-bottom: 48px;
    padding: 0 8px;
}

.tip-item {
    font-size: 28px;
    color: #333;
    line-height: 44px;
    margin-bottom: 16px;
    text-align: left;
}

.tip-item:last-child {
    margin-bottom: 0;
}

.dialog-actions {
    display: flex;
    gap: 16px;
    justify-content: center;
}

.action-button {
    flex: 1;
    height: 96px;
    font-size: 28px;
    border-radius: 48px;
    min-width: 200px;
}

.action-button:deep(.van-button__text) {
    font-weight: 500;
    line-height: 1.3;
    text-align: center;
}

.secondary-button {
    background-color: #fff;
    border: 2px solid #3583FF;
    color: #3583FF;
}

.secondary-button:deep(.van-button__text) {
    color: #3583FF;
}

.primary-button {
    background-color: #3583FF;
    border: 2px solid #3583FF;
    color: #fff;
}

.primary-button:deep(.van-button__text) {
    color: #fff;
}

/* 浮动按钮样式 */
.floating-action {
    position: fixed;
    bottom: 100px;
    right: 30px;
    z-index: 999;
}

.floating-btn {
    width: 160px;
    height: 80px;
    font-size: 30px;
    font-weight: 600;
    background: linear-gradient(135deg, #3583FF 0%, #1677FF 100%);
    border: none;
    box-shadow: 0 8px 24px rgba(53, 131, 255, 0.4);
    transition: all 0.3s ease;
}

.floating-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 32px rgba(53, 131, 255, 0.5);
}

.floating-btn:active {
    transform: translateY(0);
    box-shadow: 0 6px 20px rgba(53, 131, 255, 0.3);
}
.time-dropdown {
    background: transparent !important;
}
.van-dropdown-menu__bar {
    box-shadow: none !important;
}
</style> 
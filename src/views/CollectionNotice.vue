<template>
    <div class="collection-notice">
        <!-- 顶部导航 -->
        <van-nav-bar
            :title="pageTitle"
            left-arrow
            @click-left="$router.go(-1)"
            class="custom-nav-bar"
        />
        
        <div class="content">
            <!-- 基本信息卡片 -->
            <div class="info-card">
                <div class="info-row">
                    <span class="label">合同号</span>
                    <span class="value">{{ contractInfo.contractNo }}</span>
                </div>
                <div class="info-row">
                    <span class="label">承租方</span>
                    <span class="value">{{ contractInfo.tenant }}</span>
                </div>
                <div class="info-row">
                    <span class="label">租期</span>
                    <span class="value">{{ contractInfo.rentPeriod }}</span>
                </div>
                <div class="info-row">
                    <span class="label">租赁房间数</span>
                    <span class="value">{{ contractInfo.roomCount }}</span>
                </div>
            </div>

            <!-- 账单区域 -->
            <div class="bill-section" v-if="collectionDetail">
                <div class="section-header">
                    <div class="header-left">
                        <div class="blue-bar"></div>
                        <span class="section-title">{{ billSectionTitle }}</span>
                    </div>
                    <div class="total-amount">
                        <span class="amount-label">未付合计</span>
                        <span class="amount-value">{{ totalAmount }}元</span>
                    </div>
                </div>

                <!-- 动态渲染账单列表 -->
                <div class="bill-item" v-for="bill in billList" :key="bill.id">
                    <div class="bill-header">
                        <span class="bill-type">{{ bill.billType }}{{ bill.period }}</span>
                        <span class="period" v-if="bill.periodDate">{{ bill.periodDate }}</span>
                    </div>
                    <div class="bill-detail">
                        <div class="payment-row">
                            <span class="payment-label">应付</span>
                            <span class="payment-amount paid">{{ bill.shouldPay }}元</span>
                            <span class="payment-label">未付</span>
                            <span class="payment-amount unpaid">{{ bill.unpaid }}元</span>
                        </div>
                        <div class="due-date">应付日期 {{ bill.dueDate }}</div>
                    </div>
                </div>
            </div>

            <!-- 加载状态 -->
            <div v-if="loading" class="loading-container">
                <van-loading size="24px">加载中...</van-loading>
            </div>
        </div>

        <!-- 底部支付栏 -->
        <div class="payment-bar">
            <div class="payment-info">
                <span class="payment-label">{{ paymentLabelText }}</span>
                <span class="payment-total">{{ totalAmount }}元</span>
            </div>
            <button class="pay-button" @click="handlePayment">
                去支付
            </button>
        </div>

        <!-- 温馨提示 -->
        <div class="notice-bar" v-if="collectionDetail">
            <div class="notice-content">
                <span class="notice-text">{{ noticeText }}</span>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useRoute } from 'vue-router'
import { showToast, showLoadingToast, showConfirmDialog, closeToast } from 'vant'
import { getCollectionDetail, payCollectionBill, type ContractBillDetailVo, type ContractBillPaymentDto } from '../api/collection'
const route = useRoute()

// 催缴详情数据
const collectionDetail = ref<ContractBillDetailVo | null>(null)
const loading = ref(false)

// 页面类型（从URL参数获取）
const pageType = computed(() => {
    const type = route.query.type as string
    return type === '2' ? 'detail' : 'notice' // type=2为催缴通知单，其他为催缴函
})

// 页面标题
const pageTitle = computed(() => {
    return pageType.value === 'detail' ? '催缴通知单' : '催缴函'
})

// 账单标题
const billSectionTitle = computed(() => {
    return pageType.value === 'detail' ? '本期账单' : '逾期账单'
})

// 支付栏金额标签
const paymentLabelText = computed(() => {
    return pageType.value === 'detail' ? '本期应缴' : '逾期金额'
})

// 底部提醒文字
const noticeText = computed(() => {
    if (pageType.value === 'detail') {
        // 催缴通知单：温馨提醒
        return `重要提醒：请贵方于${getLatestDueDate()}前支付本期款项，以免逾期产生违约金，感谢您的配合！`
    } else {
        // 催缴函：严肃提醒
        return '严肃提醒：贵方已逾期未缴款，请于收到本函5日内足额支付账单款项，否则我司将依法启动法律程序并追索全部损失！'
    }
})

// 计算属性 - 合同信息
const contractInfo = computed(() => {
    if (!collectionDetail.value) return {}
    return {
        contractNo: collectionDetail.value.contractNo,
        tenant: collectionDetail.value.customerName,
        rentPeriod: `${formatDate(collectionDetail.value.startDate)}至${formatDate(collectionDetail.value.endDate)}`,
        roomCount: `${collectionDetail.value.roomCount}间`
    }
})

// 计算属性 - 总金额
const totalAmount = computed(() => {
    if (!collectionDetail.value) return 0
    return collectionDetail.value.totalMoney || 0
})

// 计算属性 - 账单列表
const billList = computed(() => {
    if (!collectionDetail.value?.moneyList) return []
    return collectionDetail.value.moneyList.map(item => ({
        id: item.costId,
        billType: item.subjectName,
        period: item.period > 0 ? `第${item.period}期` : '',
        periodDate: `${formatDate(item.startDate)}至${formatDate(item.endDate)}`,
        shouldPay: item.actualReceivable,
        unpaid: item.unreceivedAmount,
        dueDate: formatDate(item.receivableDate)
    }))
})

// 格式化日期
const formatDate = (dateStr: string) => {
    if (!dateStr) return ''
    const date = new Date(dateStr)
    return date.toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit'
    }).replace(/\//g, '-')
}

// 获取最新的应付日期（用于底部提醒）
const getLatestDueDate = () => {
    if (!collectionDetail.value?.moneyList?.length) return ''

    // 找到最新的应付日期
    const latestDate = collectionDetail.value.moneyList
        .map(item => new Date(item.receivableDate))
        .sort((a, b) => b.getTime() - a.getTime())[0]

    if (!latestDate) return ''

    const year = latestDate.getFullYear()
    const month = latestDate.getMonth() + 1
    const day = latestDate.getDate()

    return `${year}年${month}月${day}日`
}

// 获取催缴详情
const fetchCollectionDetail = async () => {
    const id = route.query.id as string
    if (!id) {
        showToast('缺少催缴ID参数')
        return
    }

    loading.value = true
    const loadingToast = showLoadingToast({
        message: '加载中...',
        forbidClick: true,
    })

    try {
        const response = await getCollectionDetail(id)
        collectionDetail.value = response.data
    } catch (error) {
        console.error('获取催缴详情失败:', error)
        showToast('获取催缴详情失败')
    } finally {
        loading.value = false
        loadingToast.close()
    }
}

// 处理支付
const handlePayment = async () => {
    if (!collectionDetail.value) {
        showToast('催缴信息不完整')
        return
    }

    if (totalAmount.value <= 0) {
        showToast('支付金额必须大于0')
        return
    }

    try {
        // 显示确认对话框
        await showConfirmDialog({
            title: '确认支付',
            message: `确认支付￥${totalAmount.value}?`,
        })

        // 显示加载提示
        showLoadingToast({
            message: '正在处理支付...',
            forbidClick: true
        })

        const paymentData: ContractBillPaymentDto = {
            billId: collectionDetail.value.id,
            amount: totalAmount.value,
            paymentList: collectionDetail.value.moneyList.map(item => ({
                costId: item.costId,
                payAmount: item.unreceivedAmount
            }))
        }

        const response = await payCollectionBill(paymentData)
        closeToast()

        if (response.code === 200 && response.paymentUrl) {
            // 跳转到支付链接
            window.location.href = response.paymentUrl
        } else if (response.code === 200) {
            showToast('支付成功')
            // 2秒后关闭页面
            setTimeout(() => {
                window.close()
            }, 2000)
        } else {
            showToast(response.msg || '支付失败')
        }
    } catch (error) {
        closeToast()
        // 用户取消支付时不显示错误信息
        if (error !== 'cancel') {
            console.error('支付失败:', error)
            showToast('支付处理失败，请重试')
        }
    }
}

// 页面加载时获取数据
onMounted(() => {
    fetchCollectionDetail()
})
</script>

<style lang="less" scoped>
.collection-notice {
    min-height: 100vh;
    background-color: #f5f5f5;
    padding-bottom: 280px; /* 750设计稿下的底部内边距 */
}

/* 导航栏样式 */
.custom-nav-bar {
    background-color: #fff;
    border-bottom: 1px solid #f0f0f0;
}

.content {
    padding: 32px; /* 750设计稿下的内边距 */
}

/* 基本信息卡片 */
.info-card {
    background: #fff;
    border-radius: 16px; /* 750设计稿下的圆角 */
    padding: 32px; /* 750设计稿下的内边距 */
    margin-bottom: 32px; /* 750设计稿下的外边距 */
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);

    .info-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 24px 0; /* 750设计稿下的内边距 */
        border-bottom: 1px solid #f5f5f5;

        &:last-child {
            border-bottom: none;
        }

        .label {
            font-size: 28px; /* 750设计稿下的字体大小 */
            color: #666;
        }

        .value {
            font-size: 28px; /* 750设计稿下的字体大小 */
            color: #333;
            font-weight: 500;
        }
    }
}

/* 账单区域 */
.bill-section {
    background: #fff;
    border-radius: 16px; /* 750设计稿下的圆角 */
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);

    .section-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 32px; /* 750设计稿下的内边距 */
        background: #f8f9fa;
        border-bottom: 1px solid #f0f0f0;

        .header-left {
            display: flex;
            align-items: center;

            .blue-bar {
                width: 8px; /* 750设计稿下的宽度 */
                height: 32px; /* 750设计稿下的高度 */
                background: #1677ff;
                border-radius: 4px; /* 750设计稿下的圆角 */
                margin-right: 16px; /* 750设计稿下的外边距 */
            }

            .section-title {
                font-size: 32px; /* 750设计稿下的字体大小 */
                font-weight: 600;
                color: #333;
            }
        }

        .total-amount {
            display: flex;
            align-items: center;
            gap: 16px; /* 750设计稿下的间距 */

            .amount-label {
                font-size: 24px; /* 750设计稿下的字体大小 */
                color: #666;
            }

            .amount-value {
                font-size: 32px; /* 750设计稿下的字体大小 */
                font-weight: 600;
                color: #ff4d4f;
            }
        }
    }

    .bill-item {
        padding: 32px; /* 750设计稿下的内边距 */
        border-bottom: 1px solid #f5f5f5;

        &:last-child {
            border-bottom: none;
        }

        .bill-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 24px; /* 750设计稿下的外边距 */

            .bill-type {
                font-size: 30px; /* 750设计稿下的字体大小 */
                font-weight: 600;
                color: #333;
            }

            .period {
                font-size: 24px; /* 750设计稿下的字体大小 */
                color: #999;
            }
        }

        .bill-detail {
            .payment-row {
                display: flex;
                align-items: center;
                gap: 32px; /* 750设计稿下的间距 */
                margin-bottom: 16px; /* 750设计稿下的外边距 */

                .payment-label {
                    font-size: 26px; /* 750设计稿下的字体大小 */
                    color: #666;
                }

                .payment-amount {
                    font-size: 26px; /* 750设计稿下的字体大小 */
                    font-weight: 500;

                    &.paid {
                        color: #52c41a;
                    }

                    &.unpaid {
                        color: #ff4d4f;
                    }
                }
            }

            .due-date {
                font-size: 24px; /* 750设计稿下的字体大小 */
                color: #999;
            }
        }
    }
}

/* 底部支付栏 */
.payment-bar {
    position: fixed;
    bottom: 120px; /* 750设计稿下的底部距离 */
    left: 0;
    right: 0;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 32px; /* 750设计稿下的内边距 */
    background: #fff;
    border-top: 1px solid #f0f0f0;
    z-index: 100;

    .payment-info {
        display: flex;
        align-items: center;
        gap: 16px; /* 750设计稿下的间距 */

        .payment-label {
            font-size: 28px; /* 750设计稿下的字体大小 */
            color: #666;
        }

        .payment-total {
            font-size: 36px; /* 750设计稿下的字体大小 */
            font-weight: 600;
            color: #ff4d4f;
        }
    }

    .pay-button {
        background: #1677ff;
        color: #fff;
        border: none;
        border-radius: 40px; /* 750设计稿下的圆角 */
        padding: 24px 64px; /* 750设计稿下的内边距 */
        font-size: 30px; /* 750设计稿下的字体大小 */
        font-weight: 500;
        cursor: pointer;
        transition: all 0.3s;

        &:hover {
            background: #0958d9;
        }

        &:active {
            transform: scale(0.98);
        }
    }
}

/* 温馨提示 */
.notice-bar {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: #fff3cd;
    border-top: 1px solid #ffeaa7;
    z-index: 99;

    .notice-content {
        display: flex;
        align-items: flex-start;
        padding: 24px 32px; /* 750设计稿下的内边距 */
        gap: 16px; /* 750设计稿下的间距 */

        .notice-icon {
            width: 32px; /* 750设计稿下的图标大小 */
            height: 32px; /* 750设计稿下的图标大小 */
            color: #856404;
            margin-top: 4px; /* 750设计稿下的外边距 */
            flex-shrink: 0;
        }

        .notice-text {
            font-size: 24px; /* 750设计稿下的字体大小 */
            color: #856404;
            line-height: 1.4;
        }
    }
}

/* 加载状态 */
.loading-container {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 80px 0; /* 750设计稿下的内边距 */
    color: #999;
}
</style>

<template>
    <div class="page">
        <van-nav-bar title="房态图" left-arrow @click-left="$router.back()" class="nav-bar" />

        <!-- 统计信息 -->
        <!-- <div class="stats-bar" v-if="statsData">
            <div class="stats-item">
                <span class="stats-count">{{ statsData.totalCount }}</span>
                <span class="stats-label">总数</span>
            </div>
            <div class="stats-item">
                <span class="stats-count">{{ statsData.emptyCount }}</span>
                <span class="stats-label">空置</span>
            </div>
            <div class="stats-item">
                <span class="stats-count">{{ statsData.rentCount }}</span>
                <span class="stats-label">在租</span>
            </div>
            <div class="stats-item">
                <span class="stats-count">{{ statsData.toEffectCount }}</span>
                <span class="stats-label">待生效</span>
            </div>
            <div class="stats-item">
                <span class="stats-count">{{ statsData.invalidCount }}</span>
                <span class="stats-label">不可招商</span>
            </div>
        </div> -->

        <div class="filter-bar">
            <van-dropdown-menu class="dropdown-menu">
                <van-dropdown-item v-model="selectedParcel" :options="parcelOptions" @change="onParcelChange" />
                <van-dropdown-item v-model="selectedBuilding" :options="buildingOptions" @change="onBuildingChange" />
                <van-dropdown-item v-model="selectedFloor" :options="floorOptions" @change="onFloorChange" />
            </van-dropdown-menu>
            <!-- <van-button type="primary" size="small" class="more-btn" @click="showMoreFilter">更多</van-button> -->
            <div class="filter-right">
                <span class="more-filter" @click="showMoreFilter">
                    <span class="more-filter-text">更多</span>
                    <img src="@/assets/images/filter.png" class="more-filter-img" />
                </span>
            </div>
        </div>

        <div class="legend">
            <div v-for="(item, index) in legendItems" :key="index" class="legend-item">
                <span :class="['legend-color', item.class, item.isHalf ? 'half' : '']" v-if="!item.isHalf"></span>
                <span :class="['legend-color-half']" v-else>
                    <span :class="['half', item.class]"></span>
                </span>
                <span class="legend-text">{{ item.text }}</span>
            </div>
        </div>

        <!-- 加载状态 -->
        <van-loading v-if="loading" class="loading-center" size="24px" vertical>
            加载房态数据中...
        </van-loading>

        <!-- 空状态 -->
        <van-empty v-else-if="!loading && (!diagramData || diagramData.floorDiagramList.length === 0)"
            description="暂无房态数据" />

        <!-- 房态图 -->
        <div v-else class="rooms-container">
            <div v-for="(floor, floorIndex) in diagramData?.floorDiagramList || []" :key="floor.floorId" class="floor">
                <div class="floor-label">{{ floor.floorName }}</div>
                <div class="room-grid">
                    <div v-for="room in floor.rooms" :key="room.roomId" :class="['room', getRoomStatusClass(room)]"
                        @click="onRoomClick(room)">
                        <span class="room-number">{{ room.roomName }}</span>

                        <!-- 特殊状态标识 -->
                        <span v-if="hasSpecialStatus(room)" :class="['room-status-half']">
                            <span :class="['room-half', getSpecialStatusClass(room)]"></span>
                        </span>
                    </div>
                </div>
            </div>
        </div>

        <!-- 更多筛选弹框 -->
        <van-popup v-model:show="showFilterPopup" position="bottom" round
            class="filter-popup">
            <div class="filter-content">
                <div class="filter-header">
                    <h3 class="filter-title">更多筛选</h3>
                    <van-icon name="cross" @click="closeFilterPopup" class="close-icon" />
                </div>

                <div class="filter-form">
                    <!-- 查看日期 -->
                    <van-cell-group>
                        <van-field readonly clickable label="远期房态日期" v-model="filterForm.viewDate"
                            placeholder="请选择远期房态日期" @click="openViewDatePicker" label-width="120px" input-align="right"/>
                        <!-- </van-cell-group> -->

                        <!-- 用途（物业类型） -->
                        <!-- <van-cell-group > -->
                        <van-field label="用途" v-if="propertyTypeOptions.length > 0" label-width="90px">
                            <template #input>
                                <div class="property-type-checkboxes">
                                    <van-checkbox-group v-model="filterForm.propertyTypes" direction="horizontal">
                                        <van-checkbox v-for="type in propertyTypeOptions" :key="type.value"
                                            :name="type.value" shape="square">
                                            {{ type.text }}
                                        </van-checkbox>
                                    </van-checkbox-group>
                                </div>
                            </template>
                        </van-field>
                        <!-- </van-cell-group> -->

                        <!-- 房态 -->
                        <!-- <van-cell-group> -->
                        <van-field label="房态" label-width="90px">
                            <template #input>
                                <div class="room-status-radios">
                                    <van-radio-group v-model="filterForm.roomStatus" direction="horizontal">
                                        <van-radio v-for="status in roomStatusOptions" :key="status.value"
                                            :name="status.value">
                                            {{ status.text }}
                                        </van-radio>
                                    </van-radio-group>
                                </div>
                            </template>
                        </van-field>
                        <!-- </van-cell-group> -->

                        <!-- 其他筛选条件 -->
                        <!-- <van-cell-group> -->
                        <!-- <van-field label="特殊标识" label-width="90px">
                            <template #input>
                                <div class="special-status-checkboxes">
                                    <van-checkbox-group v-model="filterForm.specialStatuses" direction="horizontal">
                                        <van-checkbox name="dueSoon" shape="square">即将到期</van-checkbox>
                                        <van-checkbox name="isSelfUse" shape="square">自用</van-checkbox>
                                        <van-checkbox name="isLock" shape="square">锁房</van-checkbox>
                                        <van-checkbox name="isDirty" shape="square">脏房</van-checkbox>
                                        <van-checkbox name="isMaintain" shape="square">维修</van-checkbox>
                                    </van-checkbox-group>
                                </div>
                            </template>
                        </van-field> -->
                    </van-cell-group>
                </div>

                <!-- 查看日期选择器 -->
                <van-popup v-model:show="showViewDatePicker" position="bottom">
                    <van-date-picker :model-value="viewDate" title="选择查看日期" :min-date="minDate" :max-date="maxDate"
                        @confirm="onViewDateConfirm" @cancel="onViewDateCancel" />
                </van-popup>

                <div class="filter-actions">
                    <van-button class="reset-btn" @click="resetFilter">重置</van-button>
                    <van-button type="primary" class="confirm-btn" @click="applyFilter">确定</van-button>
                </div>
            </div>
        </van-popup>

        <!-- 房间详情弹框 -->
        <van-popup v-model:show="showRoomDetail" position="center" round style="width: 90vw;" class="room-detail-popup">
            <div v-if="selectedRoom" class="room-detail-content">
                <div class="room-detail-header">
                    <h3 class="room-detail-title">房源详情</h3>
                    <van-icon name="cross" @click="closeRoomDetail" class="close-icon" />
                </div>

                <div class="room-detail-body">
                    <div class="room-list">
                        <div class="room-item">
                            <div class="room-item-img-box">
                                <img src="../assets/images/ld.png" alt="" class="room-item-img">
                            </div>
                            <div class="room-item-name">{{ selectedBuildingName }}-{{ selectedRoom.roomName }}</div>
                            <div class="room-item-status" :class="[getRoomStatusClass(selectedRoom)]">{{ selectedRoom.roomStatusName }}</div>
                        </div>
                    </div>
                    <div class="room-detail-btn-box" v-if="selectedRoom.roomStatus === 1">
                        <van-button type="primary" size="small" class="room-detail-btn" @click="onBookingClick">租客预定</van-button>
                    </div>
                    <!-- 点击在租房源，可查看账单码；若未进场，可办理进场；如下图 -->
                    <div class="room-detail-btn-box room-detail-btn-box-bottom" v-if="selectedRoom.roomStatus === 2">
                        <van-button type="primary" size="small" class="room-detail-btn" @click="handleEnter" v-if="selectedRoom.needCheckIn">办理进场</van-button>
                        <van-button type="default" size="small" class="room-detail-btn" @click="handleBillCode">查看账单码</van-button>
                    </div>
                    <!-- <div class="room-basic-info">
                        <div class="info-row">
                            <span class="info-label">房间状态：</span>
                            <span class="info-value">{{ selectedRoom.roomStatusName }}</span>
                        </div>
                        <div class="info-row">
                            <span class="info-label">物业类型：</span>
                            <span class="info-value">{{ selectedRoom.propertyTypeName }}</span>
                        </div>
                        <div class="info-row">
                            <span class="info-label">计租面积：</span>
                            <span class="info-value">{{ selectedRoom.rentArea }}㎡</span>
                        </div>
                        <div class="info-row" v-if="selectedRoom.tablePrice">
                            <span class="info-label">表价：</span>
                            <span class="info-value">¥{{ selectedRoom.tablePrice }}/月</span>
                        </div>
                        <div class="info-row" v-if="selectedRoom.emptyDays > 0">
                            <span class="info-label">空置天数：</span>
                            <span class="info-value">{{ selectedRoom.emptyDays }}天</span>
                        </div>
                    </div> -->

                    <!-- 合同信息 -->
                    <!-- <div v-if="selectedRoom.contractVo" class="contract-info">
                        <h4 class="section-title">合同信息</h4>
                        <div class="info-row">
                            <span class="info-label">合同编号：</span>
                            <span class="info-value">{{ selectedRoom.contractVo.contractNo }}</span>
                        </div>
                        <div class="info-row">
                            <span class="info-label">承租人：</span>
                            <span class="info-value">{{ selectedRoom.contractVo.tenantName }}</span>
                        </div>
                        <div class="info-row">
                            <span class="info-label">合同租金：</span>
                            <span class="info-value">¥{{ selectedRoom.contractVo.rentPrice }}/月</span>
                        </div>
                        <div class="info-row">
                            <span class="info-label">合同期间：</span>
                            <span class="info-value">{{ formatDateRange(selectedRoom.contractVo.startDate,
                                selectedRoom.contractVo.endDate) }}</span>
                        </div>
                    </div> -->

                    <!-- 订单信息 -->
                    <!-- <div v-if="selectedRoom.bookingVo" class="booking-info">
                        <h4 class="section-title">订单信息</h4>
                        <div class="info-row">
                            <span class="info-label">客户名称：</span>
                            <span class="info-value">{{ selectedRoom.bookingVo.customerName }}</span>
                        </div>
                        <div class="info-row">
                            <span class="info-label">公司名称：</span>
                            <span class="info-value">{{ selectedRoom.bookingVo.companyName }}</span>
                        </div>
                        <div class="info-row">
                            <span class="info-label">预定金额：</span>
                            <span class="info-value">¥{{ selectedRoom.bookingVo.bookingAmount }}</span>
                        </div>
                    </div> -->

                    <!-- 房源标识 -->
                    <!-- <div v-if="selectedRoom.tags && selectedRoom.tags.length > 0" class="room-tags">
                        <h4 class="section-title">房源标识</h4>
                        <div class="tags-container">
                            <van-tag v-for="tag in selectedRoom.tags" :key="tag" type="primary" size="medium">
                                {{ tag }}
                            </van-tag>
                        </div>
                    </div> -->
                </div>
            </div>
        </van-popup>

        <!-- 收款码弹框 -->
        <van-popup v-model:show="showQRCodePopup" position="center" :close-on-click-overlay="true" round
            class="qr-code-popup">
            <div class="qr-code-content">
                <div class="qr-code-header">
                    <h3 class="qr-code-title">收款码</h3>
                    <van-icon name="cross" @click="closeQRCodePopup" class="close-icon" />
                </div>

                <div class="qr-code-section">

                    <div class="qr-code-wrapper">
                        <QRCode :value="qrCodeData" :size="200" />
                    </div>

                </div>

            </div>
        </van-popup>
    </div>
</template>

<script lang="ts">
export default {
  name: 'RoomStateDiagram'
}
</script>

<script setup lang="ts">
import { ref, reactive, onMounted, computed, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { showToast, showLoadingToast, closeToast } from 'vant';
import {
    getRoomDiagram,
    type RoomDiagramQueryDTO,
    type DiagramVo,
    type RoomDiagramVo,
    type FloorDiagramVo
} from '../api/room';
import { getUserProjects } from '../api/home';
import { getEntryList, initEntry, getUnenteredRooms, getEnterDetail, notifyCustomer, type EnterQueryDTO, type EnterVo } from '../api/entry'
import { getParcelList, getBuildingSelectList, getFloorList, type SysParcel, type SysBuilding, type SysFloor } from '../api/project'
import QRCode from '@/components/QRCode.vue'

const route = useRoute();
const router = useRouter();

// 物业类型字典数据
const PROPERTY_TYPE_DICT = [
    {
        "label": "宿舍",
        "dictLabel": "宿舍",
        "value": "10",
        "dictValue": "10",
        "elTagType": null,
        "elTagClass": null,
        "dictCode": 122,
        "parentCode": null,
        "childList": [],
        "children": null
    },
    {
        "label": "厂房",
        "dictLabel": "厂房",
        "value": "20",
        "dictValue": "20",
        "elTagType": null,
        "elTagClass": null,
        "dictCode": 123,
        "parentCode": null,
        "childList": [],
        "children": null
    },
    {
        "label": "商业",
        "dictLabel": "商业",
        "value": "30",
        "dictValue": "30",
        "elTagType": null,
        "elTagClass": null,
        "dictCode": 124,
        "parentCode": null,
        "childList": [
            {
                "label": "商铺",
                "dictLabel": "商铺",
                "value": "31",
                "dictValue": "31",
                "elTagType": null,
                "elTagClass": null,
                "dictCode": 125,
                "parentCode": 124,
                "childList": null,
                "children": null
            },
            {
                "label": "综合体",
                "dictLabel": "综合体",
                "value": "32",
                "dictValue": "32",
                "elTagType": null,
                "elTagClass": null,
                "dictCode": 126,
                "parentCode": 124,
                "childList": null,
                "children": null
            },
            {
                "label": "中央食堂",
                "dictLabel": "中央食堂",
                "value": "33",
                "dictValue": "33",
                "elTagType": null,
                "elTagClass": null,
                "dictCode": 127,
                "parentCode": 124,
                "childList": null,
                "children": null
            }
        ],
        "children": null
    },
    {
        "label": "商铺",
        "dictLabel": "商铺",
        "value": "31",
        "dictValue": "31",
        "elTagType": null,
        "elTagClass": null,
        "dictCode": 125,
        "parentCode": 124,
        "childList": null,
        "children": null
    },
    {
        "label": "综合体",
        "dictLabel": "综合体",
        "value": "32",
        "dictValue": "32",
        "elTagType": null,
        "elTagClass": null,
        "dictCode": 126,
        "parentCode": 124,
        "childList": null,
        "children": null
    },
    {
        "label": "中央食堂",
        "dictLabel": "中央食堂",
        "value": "33",
        "dictValue": "33",
        "elTagType": null,
        "elTagClass": null,
        "dictCode": 127,
        "parentCode": 124,
        "childList": null,
        "children": null
    },
    {
        "label": "车位",
        "dictLabel": "车位",
        "value": "40",
        "dictValue": "40",
        "elTagType": null,
        "elTagClass": null,
        "dictCode": 128,
        "parentCode": null,
        "childList": [],
        "children": null
    },
    {
        "label": "办公",
        "dictLabel": "办公",
        "value": "50",
        "dictValue": "50",
        "elTagType": null,
        "elTagClass": null,
        "dictCode": 129,
        "parentCode": null,
        "childList": [],
        "children": null
    },
    {
        "label": "广告位",
        "dictLabel": "广告位",
        "value": "60",
        "dictValue": "60",
        "elTagType": null,
        "elTagClass": null,
        "dictCode": 130,
        "parentCode": null,
        "childList": [],
        "children": null
    },
    {
        "label": "设备类",
        "dictLabel": "设备类",
        "value": "70",
        "dictValue": "70",
        "elTagType": null,
        "elTagClass": null,
        "dictCode": 131,
        "parentCode": null,
        "childList": [],
        "children": null
    },
    {
        "label": "日租房",
        "dictLabel": "日租房",
        "value": "80",
        "dictValue": "80",
        "elTagType": null,
        "elTagClass": null,
        "dictCode": 132,
        "parentCode": null,
        "childList": [],
        "children": null
    },
    {
        "label": "其他",
        "dictLabel": "其他",
        "value": "90",
        "dictValue": "90",
        "elTagType": null,
        "elTagClass": null,
        "dictCode": 133,
        "parentCode": null,
        "childList": [],
        "children": null
    }
];

// 收款码弹窗
const showQRCodePopup = ref(false);
const qrCodeData = ref('');
const closeQRCodePopup = () => {
    showQRCodePopup.value = false;
}

// 响应式数据
const loading = ref(false);
const diagramData = ref<DiagramVo | null>(null);
const statsData = computed(() => diagramData.value);
const isInitializing = ref(false); // 标记是否正在初始化

// 筛选条件
const selectedParcel = ref('');
const selectedBuilding = ref('');
const selectedBuildingName = ref('');           
const selectedFloor = ref('');

// 下拉选项
const parcelOptions = ref<Array<{ text: string, value: string }>>([]);
const buildingOptions = ref<Array<{ text: string, value: string }>>([]);
const floorOptions = ref<Array<{ text: string, value: string }>>([]);
const propertyTypeOptions = ref<Array<{ text: string, value: string }>>([]);

// 更多筛选相关
const showFilterPopup = ref(false);
const filterForm = reactive({
    viewDate: '',        // 查看日期
    propertyTypes: [],   // 用途（物业类型）- 多选
    roomStatus: null,    // 房态 - 单选
    specialStatuses: []  // 特殊状态
});

// 日期选择器相关
const showViewDatePicker = ref(false);

const viewDate = ref(['2024', '01', '01']); // 日期选择器需要字符串数组格式
let minDate = new Date(2020, 0, 1);
const maxDate = new Date(2030, 11, 31);
const openViewDatePicker = () => {
    const today = new Date()
    const year = today.getFullYear()
    const month = String(today.getMonth() + 1).padStart(2, '0')
    const month0 = today.getMonth()
    const day0 = today.getDate()
    const day = String(today.getDate()).padStart(2, '0')
    minDate = new Date(year, month0, day0)
    viewDate.value = [String(year), String(month), String(day)]
    showViewDatePicker.value = true
}

// 房间详情
const showRoomDetail = ref(false);
const selectedRoom = ref<RoomDiagramVo | null>(null);

// 当前项目ID（从路由参数或用户默认项目获取）
const currentProjectId = ref(route.query.projectId as string || '');

// 房态图例配置
const legendItems = [
    { text: '空置', class: 'vacant' },
    { text: '在租', class: 'rented' },
    { text: '待生效/签约中/已预定', class: 'pending' },
    { text: '不可招商', class: 'unavailable' },
    { text: '自用', class: 'self-use', isHalf: true },
    { text: '未进场', class: 'not-entered', isHalf: true },
    { text: '未出场', class: 'not-exited', isHalf: true },
];

// 筛选选项
const roomStatusOptions = [
    { text: '空置', value: 1 },
    { text: '在租', value: 2 },
    { text: '待生效/签约中/已预定', value: 3 },
    { text: '不可招商', value: 4 }
];

// 生命周期
onMounted(async () => {
    await initPage();
});

// 监听筛选条件变化
watch([selectedParcel, selectedBuilding, selectedFloor], () => {
    // 如果正在初始化，跳过调用，避免重复请求
    if (!isInitializing.value) {
        console.log('Watch triggered, calling loadRoomDiagram');
        loadRoomDiagram();
    }
});

// 方法定义
const initPage = async () => {
    try {
        loading.value = true;
        isInitializing.value = true; // 设置初始化标志

        // 如果没有项目ID，获取用户默认项目
        // if (!currentProjectId.value) {
        //     const projects = await getUserProjects();
        //     if (projects.data && projects.data.length > 0) {
        //         currentProjectId.value = projects.data[0].id;
        //     }
        // }
        let currentProject = localStorage.getItem('currentProject')
        if (currentProject) {
            currentProject = JSON.parse(currentProject)
        } else {

        }
        // @ts-ignore
        currentProjectId.value = currentProject?.id
        if (currentProjectId.value) {
            // 先加载房间树数据，构建筛选选项
            await loadRoomTree();
            // 筛选选项构建完成后，再加载房态图数据
            // 此时筛选条件已经初始化，避免重复调用
            await loadRoomDiagram();
        }
    } catch (error) {
        console.error('初始化页面失败:', error);
        showToast('加载数据失败');
    } finally {
        loading.value = false;
        isInitializing.value = false; // 清除初始化标志
    }
};

// 加载房间树数据
const loadRoomTree = async () => {
    try {
        // 加载地块列表
        await loadParcelList();
    } catch (error) {
        console.error('加载房间树失败:', error);
    }
};

// 加载地块列表
const loadParcelList = async () => {
    if (!currentProjectId.value) return;
    
    try {
        const response = await getParcelList(currentProjectId.value);
        const parcels = response.data || [];
        
        // 构建地块选项
        parcelOptions.value = parcels.map(parcel => ({
            text: parcel.parcelName,
            value: parcel.id
        }));

        // 自动选择第一个地块
        if (parcelOptions.value.length > 0 && !selectedParcel.value) {
            selectedParcel.value = parcelOptions.value[0].value;
            await loadBuildingList();
        }
    } catch (error) {
        console.error('加载地块列表失败:', error);
    }
};

// 加载楼栋列表
const loadBuildingList = async () => {
    if (!selectedParcel.value) return;
    
    try {
        const response = await getBuildingSelectList(selectedParcel.value);
        const buildings = response.data || [];
        
        // 构建楼栋选项
        buildingOptions.value = buildings.map(building => ({
            text: building.buildingName,
            value: building.id
        }));

        // 自动选择第一个楼栋
        if (buildingOptions.value.length > 0 && !selectedBuilding.value) {
            selectedBuilding.value = buildingOptions.value[0].value;
            selectedBuildingName.value = buildingOptions.value[0].text;
            await loadFloorList();
        }
    } catch (error) {
        console.error('加载楼栋列表失败:', error);
    }
};

// 加载楼层列表
const loadFloorList = async () => {
    if (!selectedBuilding.value) return;
    
    try {
        const response = await getFloorList(selectedBuilding.value);
        const floors = response.data || [];
        
        // 构建楼层选项
        floorOptions.value = [
            { text: '全部楼层', value: '' },
            ...floors.map(floor => ({
                text: floor.floorName,
                value: floor.id
            }))
        ];

        // 楼层默认选择"全部楼层"
        if (!selectedFloor.value) {
            selectedFloor.value = '';
        }
        
        // 加载房态图数据
        await loadRoomDiagram();
    } catch (error) {
        console.error('加载楼层列表失败:', error);
    }
};



// 根据接口返回的propertyList构建物业类型选项
const buildPropertyTypeOptions = (propertyList: string[]) => {
    if (!propertyList || propertyList.length === 0) {
        propertyTypeOptions.value = [];
        return;
    }

    // 根据propertyList中的值匹配字典数据
    const matchedOptions = propertyList.map(propertyValue => {
        // 在字典中查找匹配的项
        const dictItem = PROPERTY_TYPE_DICT.find(item => item.value === propertyValue);
        if (dictItem) {
            return {
                text: dictItem.label,
                value: dictItem.value
            };
        } else {
            // 如果字典中没有找到，使用原值
            return {
                text: propertyValue,
                value: propertyValue
            };
        }
    });

    propertyTypeOptions.value = matchedOptions;
    console.log('Property type options built:', matchedOptions);
};

// 加载房态图数据
const loadRoomDiagram = async () => {
    try {
        console.log('loadRoomDiagram called with filters:', {
            parcel: selectedParcel.value,
            building: selectedBuilding.value,
            floor: selectedFloor.value
        });

        const params: RoomDiagramQueryDTO = {
            projectId: currentProjectId.value
        };

        // 添加筛选条件
        if (selectedParcel.value) {
            params.parcelId = selectedParcel.value;
        }
        if (selectedBuilding.value) {
            params.buildingId = selectedBuilding.value;
        }
        if (selectedFloor.value) {
            params.floorId = selectedFloor.value;
        }

        // 添加更多筛选条件
        if (filterForm.viewDate) {
            params.diagramDate = filterForm.viewDate;
        }
        if (filterForm.propertyTypes.length > 0) {
            // 物业类型支持多选，使用逗号分割
            params.propertyType = (filterForm.propertyTypes as string[]).join(',');
        }
        if (filterForm.roomStatus !== null && filterForm.roomStatus !== undefined) {
            // 房态为单选
            params.roomStatus = filterForm.roomStatus as number;
        }

        // 特殊状态筛选
        (filterForm.specialStatuses as string[]).forEach((status: string) => {
            switch (status) {
                case 'dueSoon':
                    params.dueSoon = true;
                    break;
                case 'isSelfUse':
                    params.isSelfUse = true;
                    break;
                case 'isLock':
                    params.isLock = true;
                    break;
                case 'isDirty':
                    params.isDirty = true;
                    break;
                case 'isMaintain':
                    params.isMaintain = true;
                    break;
            }
        });

        const response = await getRoomDiagram(params);
        diagramData.value = response.data;

        // 更新物业类型选项（基于实际返回的数据和字典匹配）
        if (response.data?.propertyList) {
            buildPropertyTypeOptions(response.data.propertyList);
        }
    } catch (error) {
        console.error('加载房态图失败:', error);
        showToast('加载房态图失败');
    }
};

// 由于现在直接使用ID，不再需要名称到ID的映射方法
// selectedParcel.value、selectedBuilding.value、selectedFloor.value 已经是ID值

// 筛选条件变化处理
const onParcelChange = async () => {
    // 地块变化时重置楼栋和楼层
    selectedBuilding.value = '';
    selectedFloor.value = '';
    buildingOptions.value = [];
    floorOptions.value = [{ text: '全部楼层', value: '' }];
    
    // 加载新的楼栋列表
    if (selectedParcel.value) {
        await loadBuildingList();
    }
};

const onBuildingChange = async () => {
    // 楼栋变化时重置楼层
    selectedFloor.value = '';
    floorOptions.value = [{ text: '全部楼层', value: '' }];
    
    // 加载新的楼层列表
    if (selectedBuilding.value) {
        selectedBuildingName.value = buildingOptions.value.find(item => item.value === selectedBuilding.value)?.text || '';
        await loadFloorList();
    }
};

const onFloorChange = async () => {
    // 楼层变化时重新加载房态图数据
    if (selectedParcel.value && selectedBuilding.value) {
        await loadRoomDiagram();
    }
};



// 获取房间状态样式类
const getRoomStatusClass = (room: RoomDiagramVo): string => {
    switch (room.roomStatus) {
        case 1: return 'vacant';      // 空置
        case 2: return 'rented';      // 在租
        case 3: return 'pending';     // 待生效/签约中/已预定
        case 4: return 'unavailable'; // 不可招商
        default: return 'vacant';
    }
};

// 检查是否有特殊状态
const hasSpecialStatus = (room: RoomDiagramVo): boolean => {
    return room.isSelfUse || room.needCheckIn || room.needCheckOut;
};

// 获取特殊状态样式类
const getSpecialStatusClass = (room: RoomDiagramVo): string => {
    if (room.isSelfUse) return 'self-use';
    if (room.needCheckIn) return 'not-entered';
    if (room.needCheckOut) return 'not-exited';
    return '';
};

// 房间点击事件
const onRoomClick = (room: RoomDiagramVo) => {
    selectedRoom.value = room;
    console.log('selectedRoom', selectedRoom.value)
    showRoomDetail.value = true;
};

const onBookingClick = () => {
    console.log('onBookingClick')
    router.push({
        path: '/booking-create',
        query: {
            roomId: selectedRoom.value?.roomId,
            propertyType: selectedRoom.value?.propertyType
        }
    })
}

const handleEnter = async () => {
    console.log('handleEnter')
            // 调用进场单初始化API
            const res = await initEntry({
            contractId: selectedRoom.value?.contractVo?.contractId || '',
            roomIds: [selectedRoom.value?.roomId || '']
        })
    // entry-process?contractId=0e523a481814de4d2fecba4ccc5ac991&roomIds=b797bc48a65ca562caf2979e0ec4f12d&contractNo=WYSF-XN-ZCC-BG-2025-0004&tenantName=刘伟
        router.push({
            name: 'EntryProcess',
            query: {
                contractId: selectedRoom.value?.contractVo?.contractId,
                roomIds: selectedRoom.value?.roomId,
                contractNo: selectedRoom.value?.contractVo?.contractNo,
                tenantName: selectedRoom.value?.contractVo?.tenantName
            },
            state: {
                    entryData: res.data // 通过路由状态传递详情数据
                }
        })
    
}

const handleBillCode = async() => {
    console.log('handleBillCode')
    // 修复类型错误：使用 rentPrice 替代 totalPrice
    const paymentLink = `${import.meta.env.VITE_APP_BASE_URL}/bill-payment?id=${selectedRoom.value?.contractVo?.contractId}&amount=${selectedRoom.value?.contractVo?.rentPrice}&customer=${selectedRoom.value?.contractVo?.tenantName}&type=bond`

    qrCodeData.value = paymentLink
    showQRCodePopup.value = true
    // 打开收款码弹窗
// const handleCollect = async (record: any) => {
    // try {
    //     // 生成支付链接
    //     const baseUrl = import.meta.env.VITE_APP_BASE_URL
    //     const contractId = record.id
    //     const amount = record.totalPrice
    //     const customerName = encodeURIComponent(record.customerName || '')

    //     const paymentLink = `${baseUrl}/bill-payment?id=${contractId}&amount=${amount}&customer=${customerName}&type=bond`

    //     currentRecord.value = {
    //         ...record,
    //         paymentUrl: paymentLink
    //     }
    //     collectCodeVisible.value = true
    // } catch (error) {
    //     console.error('生成收款码失败:', error)
    //     Message.error('生成收款码失败')
    // }
// }
}
// 关闭房间详情
const closeRoomDetail = () => {
    showRoomDetail.value = false;
    selectedRoom.value = null;
};

// 显示更多筛选
const showMoreFilter = () => {
    showFilterPopup.value = true;
};

// 关闭筛选弹框
const closeFilterPopup = () => {
    showFilterPopup.value = false;
};

// 查看日期确认
const onViewDateConfirm = (value: any) => {
    console.log('onViewDateConfirm', value)
    let arr = value.selectedValues
    filterForm.viewDate = `${arr[0]}-${arr[1]}-${arr[2]}`;
    showViewDatePicker.value = false;
};

// 查看日期取消
const onViewDateCancel = () => {
    showViewDatePicker.value = false;
};

// 格式化日期
const formatDate = (date: Date): string => {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
};

// 格式化日期范围
const formatDateRange = (startDate: string | null, endDate: string | null): string => {
    if (!startDate || !endDate) return '-';
    return `${startDate} 至 ${endDate}`;
};

// 应用筛选
const applyFilter = () => {
    loadRoomDiagram();
    showFilterPopup.value = false;
};

// 重置筛选
const resetFilter = () => {
    filterForm.viewDate = '';
    filterForm.propertyTypes = [];
    filterForm.roomStatus = null;
    filterForm.specialStatuses = [];
    const today = new Date();
    viewDate.value = [
        today.getFullYear().toString(),
        (today.getMonth() + 1).toString().padStart(2, '0'),
        today.getDate().toString().padStart(2, '0')
    ];

    // 重新加载数据
    loadRoomDiagram();
};
</script>

<style scoped lang="less">
.page {
    background-color: #f7f8fa;
    min-height: 100vh;
}

.nav-bar {
    background-color: #fff;

    :deep(.van-nav-bar__title) {
        font-size: 36px;
        font-weight: 500;
        color: #323233;
    }
}

// 统计信息栏
.stats-bar {
    display: flex;
    justify-content: space-around;
    padding: 32px 0;
    background-color: #fff;
    margin-bottom: 16px;

    .stats-item {
        display: flex;
        flex-direction: column;
        align-items: center;

        .stats-count {
            font-size: 48px;
            font-weight: bold;
            color: #1989fa;
            line-height: 1;
        }

        .stats-label {
            font-size: 24px;
            color: #646566;
            margin-top: 8px;
        }
    }
}

.filter-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    background-color: #fff;

    .filter-right {
        display: flex;
        align-items: center;
        justify-content: center;
        // margin-right: 32px;
        padding-left: 32px;
        .more-filter {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 32px;
            .more-filter-text {
                font-size: 26px;
                color: #000;
                font-weight: 500;
                // color: #1989fa;
            }
            .more-filter-img {
                width: 26px;
            }
        }
    }

    :deep(.van-dropdown-menu__bar) {
        box-shadow: none !important;
    }

    .dropdown-menu {
        flex: 1;

        :deep(.van-dropdown-menu__item) {
            color: #1989fa;
            font-size: 28px;
        }
    }

    .more-btn {
        margin-left: 32px;
        padding: 12px 24px;
        font-size: 28px;
        border-radius: 8px;
    }
}

.legend {
    display: flex;
    flex-wrap: wrap;
    padding: 24px 32px;
    background-color: #fff;
    margin-bottom: 16px;
    gap: 24px;

    .legend-item {
        display: flex;
        align-items: center;

        .legend-color {
            width: 24px;
            height: 24px;
            border-radius: 4px;
            margin-right: 12px;

            &.vacant {
                background-color: #52b93c;
            }

            &.rented {
                background-color: #ea5e5e;
            }

            &.pending {
                background-color: #f89538;
            }

            &.unavailable {
                background-color: #8b929c;
            }

            &.self-use {
                background-color: #3583ff;
            }

            &.not-entered {
                background-color: #60d1ff;
            }

            &.not-exited {
                background-color: #d73eff;
            }
        }

        .legend-color-half {
            width: 24px;
            height: 24px;
            background: #fff;
            position: relative;
            overflow: hidden;
            border-radius: 2px;
            margin-right: 12px;

            .half {
                color: white;
                width: 24px;
                height: 24px;
                display: flex;
                align-items: flex-end;
                justify-content: center;
                line-height: 1.1;
                text-align: center;
                position: absolute;
                top: -12px;
                right: -12px;
                transform: rotate(45deg);

                &.self-use {
                    background-color: #3583ff;
                }

                &.not-entered {
                    background-color: #60d1ff;
                }

                &.not-exited {
                    background-color: #d73eff;
                }
            }
        }

        .legend-text {
            font-size: 24px;
            color: #323233;
        }
    }
}

.loading-center {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 400px;
}

.rooms-container {
    padding: 0 16px;
    height: calc(100vh - 370px);
    overflow-y: auto;
}

.floor {
    display: flex;
    align-items: flex-start;
    margin-bottom: 48px;

    .floor-label {
        width: 120px;
        box-sizing: border-box;
        overflow: hidden;
        font-size: 26px;
        font-weight: 500;
        color: #323233;
        // padding-right: 32px;
        // min-width: 100px;
        text-align: right;
        padding: 16px 16px 0 0;
    }

    .room-grid {
        width: 0;
        flex: 1;
        display: grid;
        grid-template-columns: repeat(5, 1fr);
        gap: 16px;

        .room {
            aspect-ratio: 1;
            display: flex;
            justify-content: center;
            align-items: center;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.2s ease;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            position: relative;
            text-align: center;

            .room-number {
                font-size: 22px;
                // font-weight: 500;
                color: #fff;
            }

            .room-status-half {
                width: 32px;
                height: 32px;
                background: transparent;
                position: absolute;
                top: 0;
                right: 0;
                overflow: hidden;
                border-radius: 8px;

                .room-half {
                    background-color: #3583ff;
                    color: white;
                    width: 32px;
                    height: 32px;
                    display: flex;
                    align-items: flex-end;
                    justify-content: center;
                    line-height: 1.1;
                    text-align: center;
                    position: absolute;
                    top: -16px;
                    right: -16px;
                    transform: rotate(45deg);
                    border: 2px solid #f7f8fa;

                    &.self-use {
                        background-color: #3583ff;
                    }

                    &.not-entered {
                        background-color: #60d1ff;
                    }

                    &.not-exited {
                        background-color: #d73eff;
                    }
                }
            }

            &.vacant {
                background-color: #52b93c;
            }

            &.rented {
                background-color: #ea5e5e;
            }

            &.pending {
                background-color: #f89538;
            }

            &.unavailable {
                background-color: #8b929c;
            }

            &.self-use {
                background-color: #3583ff;
            }

            &.not-entered {
                background-color: #60d1ff;
            }

            &.not-exited {
                background-color: #d73eff;
            }

            &:hover {
                transform: scale(1.05);
                box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
            }

            &:active {
                transform: scale(0.98);
            }
        }
    }
}

/* 筛选弹框样式 */
.filter-popup {
    .filter-content {
        padding: 0;
        background-color: #fff;
    }

    .filter-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 16px 32px;
        border-bottom: 1px solid #f0f0f0;

        .filter-title {
            font-size: 38px;
            font-weight: bold;
            color: #333;
            margin: 0;
        }

        .close-icon {
            font-size: 40px;
            color: #999;
            cursor: pointer;
        }
    }

    .filter-form {
        padding: 0 16px;
        max-height: 50vh;
        overflow-y: auto;

        :deep(.van-cell-group) {
            margin-bottom: 16px;
        }

        :deep(.van-field__label) {
            font-size: 28px;
            color: #333;
            width: 120px;
        }

        :deep(.van-field__value) {
            font-size: 28px;
        }

        :deep(.van-field__control) {
            font-size: 28px;
        }

        // 复选框样式
        .property-type-checkboxes,
        .special-status-checkboxes {
            width: 100%;

            :deep(.van-checkbox-group) {
                display: flex;
                flex-wrap: wrap;
                gap: 24px;
            }

            :deep(.van-checkbox) {
                margin-right: 0;
                margin-bottom: 24px;

                .van-checkbox__label {
                    font-size: 28px;
                    color: #333;
                    margin-left: 16px;
                }

                .van-checkbox__icon {
                    font-size: 28px;
                }
            }
        }

        // 单选按钮样式
        .room-status-radios {
            width: 100%;

            :deep(.van-radio-group) {
                display: flex;
                flex-wrap: wrap;
                gap: 24px;
            }

            :deep(.van-radio) {
                margin-right: 0;
                margin-bottom: 24px;

                .van-radio__label {
                    font-size: 28px;
                    color: #333;
                    margin-left: 16px;
                }

                .van-radio__icon {
                    font-size: 28px;
                }
            }
        }
    }

    .filter-actions {
        display: flex;
        gap: 16px;
        padding: 16px 32px;
        border-top: 1px solid #f0f0f0;
        background-color: #fff;

        .reset-btn,
        .confirm-btn {
            flex: 1;
            height: 80px;
            font-size: 28px;
            border-radius: 8px;
        }

        .reset-btn {
            background-color: #f8f9fa;
            border-color: #e9ecef;
            color: #6c757d;
        }

        .confirm-btn {
            background-color: #1890ff;
            border-color: #1890ff;
        }
    }
}

/* 房间详情弹框样式 */
.room-detail-popup {
    .room-detail-content {
        padding: 0;
        background-color: #fff;
    }

    .room-detail-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 32px 32px;
        // border-bottom: 1px solid #f0f0f0;

        .room-detail-title {
            width: 0;
            flex: 1;
            text-align: center;
            font-size: 36px;
            font-weight: bold;
            color: #333;
            margin: 0;
        }

        .close-icon {
            font-size: 40px;
            color: #999;
            cursor: pointer;
        }
    }

    .room-detail-body {
        padding: 40px 80px;
        max-height: 50vh;
        overflow-y: auto;

        .room-detail-btn-box {
            display: flex;
            justify-content: center;
            margin-top: 40px;
            .room-detail-btn {
                width: 260px;
                height: 80px;
                font-size: 28px;
                border-radius: 40px;
            }
        }

        .room-detail-btn-box-bottom {
            justify-content: center;
            gap: 20px;
        }

        .section-title {
            font-size: 56px;
            font-weight: bold;
            color: #333;
            margin: 40px 0 20px 0;

            &:first-child {
                margin-top: 0;
            }
        }

        .room-list {
            display: flex;
            flex-direction: column;
            gap: 16px;

            .room-item {
                display: flex;
                align-items: center;
                gap: 16px;
                background: linear-gradient(270deg, #FFFFFF 0%, #D1DEEE 100%);
                border-radius: 16px;

                margin-bottom: 40px;

                .room-item-img-box {
                    width: 80px;
                    height: 80px;
                    background: linear-gradient(180deg, #AAC3DE 0%, #486A9A 100%);
                    border-radius: 50%;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    margin-left: -16px;

                    .room-item-img {
                        width: 50px;
                        height: 50px;
                    }
                }

                .room-item-name {
                    width: 0;
                    flex: 1;
                    font-size: 30px;
                    color: #000;
                    font-weight: bold;
                }

                .room-item-status {

                    font-size: 28px;
                    color: #fff;
                    padding: 8px 20px;

                    &.vacant {
                        background-color: #52b93c;
                    }

                    &.rented {
                        background-color: #ea5e5e;
                    }

                    &.pending {
                        background-color: #f89538;
                    }

                    &.unavailable {
                        background-color: #8b929c;
                    }

                    &.self-use {
                        background-color: #3583ff;
                    }

                    &.not-entered {
                        background-color: #60d1ff;
                    }

                    &.not-exited {
                        background-color: #d73eff;
                    }

                }

            }
        }

        .info-row {
            display: flex;
            padding: 16px 0;
            border-bottom: 1px solid #f5f5f5;

            .info-label {
                font-size: 48px;
                color: #666;
                min-width: 200px;
            }

            .info-value {
                font-size: 48px;
                color: #333;
                flex: 1;
            }
        }

        .tags-container {
            display: flex;
            flex-wrap: wrap;
            gap: 16px;
            margin-top: 20px;

            :deep(.van-tag) {
                font-size: 32px;
                padding: 8px 16px;
            }
        }
    }
}
/* 收款码弹框样式 */
.qr-code-popup {
    :deep(.van-popup) {
        border-radius: 20px;
        overflow: hidden;
    }

    .qr-code-content {
        padding: 40px;
        width: 600px;
        max-width: 90vw;
        background-color: #fff;
    }

    .qr-code-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 30px;

        .qr-code-title {
            font-size: 36px;
            font-weight: bold;
            color: #333;
            margin: 0;
        }

        .close-icon {
            font-size: 40px;
            color: #999;
            cursor: pointer;
        }
    }

    .booking-info {
        margin-bottom: 30px;
        padding: 20px;
        background-color: #f8f9fa;
        border-radius: 12px;

        .info-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
            font-size: 28px;

            &:last-child {
                margin-bottom: 0;
            }

            .label {
                color: #666;
            }

            .value {
                color: #333;
                font-weight: 500;

                &.amount {
                    color: #ff4444;
                    font-size: 32px;
                    font-weight: bold;
                }
            }
        }
    }

    .qr-code-section {
        display: flex;
        justify-content: center;
        align-items: center;
        margin-bottom: 30px;
        min-height: 200px;

        .loading-section {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 16px;
            color: #666;
            font-size: 28px;
        }

        .qr-code-wrapper {
            display: flex;
            justify-content: center;
            padding: 20px;
            background-color: #fff;
            border-radius: 12px;
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
        }

        .error-section {
            color: #ff4444;
            font-size: 28px;
        }
    }

    .qr-code-actions {
        display: flex;
        justify-content: center;

        .action-btn {
            min-width: 200px;
            height: 80px;
            font-size: 30px;
        }
    }
}
</style>
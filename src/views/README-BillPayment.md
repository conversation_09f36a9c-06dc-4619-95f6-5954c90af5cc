# 账单缴费页面使用说明

## 概述

账单缴费页面是万洋资管平台的核心功能之一，支持客户在线查看合同账单详情并完成批量缴费操作。

## 功能特性

### ✅ 已实现功能

1. **合同信息查看**
   - 显示合同基本信息（合同号、承租方、合同周期等）
   - 显示租赁单元信息（支持多个单元）
   - 显示客户信息和项目信息

2. **账单列表管理**
   - 显示未收齐的账单列表
   - 账单类型显示（保证金、租金、其他费用）
   - 账单状态标识（逾期、已到期、未到期）
   - 账单期间和应缴纳日期显示
   - 实际应收金额显示

3. **批量选择功能**
   - 支持单个账单选择
   - 支持全选/取消全选
   - 实时计算选中账单总金额

4. **支付功能**
   - 支付金额确认
   - 批量支付处理
   - 支付状态反馈
   - 支付链接跳转或直接完成

5. **业务逻辑校验**
   - 账单状态校验（只允许待收状态且可支付的账单）
   - 账单权限校验（检查 `canPay` 字段）
   - 支付金额验证

6. **用户体验优化**
   - 加载状态提示
   - 错误处理和友好提示
   - 响应式设计适配移动端
   - 扫码进入和正常进入的区别处理

## 页面路由

```
/bill-payment?id={contractId}
```

### 参数说明
- `id`: 合同ID（必需）

## API 接口

### 1. 获取合同详情
```typescript
GET /contract/detail?id={contractId}
```

### 2. 合同支付
```typescript
POST /contract/pay
{
  "contractId": "string",
  "amount": number,
  "costList": [
    {
      "costId": "string",
      "payAmount": number
    }
  ]
}
```

## 数据结构

### 合同详情响应 (ContractDetailVo)
```typescript
interface ContractDetailVo {
  contract: ContractVo;        // 合同基本信息
  unpaidBills: CostVo[];       // 未收齐账单列表
}
```

### 账单支付请求 (ContractPaymentDto)
```typescript
interface ContractPaymentDto {
  contractId: string;          // 合同ID
  amount: number;              // 支付总金额
  costList: ContractPaymentItemDto[];  // 账单支付明细
}
```

### 关键字段说明
- `contract.customerName`: 承租方名称
- `contract.roomName`: 租赁单元（主要）
- `contract.rooms`: 租赁单元列表（详细）
- `unpaidBills[].canPay`: 是否可缴费
- `unpaidBills[].status`: 账单状态（0-待收、1-待付、2-已收、3-已付）
- `unpaidBills[].actualReceivable`: 实际应收金额
- `unpaidBills[].costType`: 账单类型（1-保证金，2-租金，3-其他费用）

## 账单状态说明

### 状态计算逻辑
- **逾期**: 应收日期 < 当前日期
- **已到期**: 应收日期 = 当前日期
- **未到期**: 应收日期 > 当前日期

### 状态颜色
- 逾期: #FF9428 (橙色)
- 已到期: #FF4141 (红色)
- 未到期: #3AC8D4 (蓝绿色)

## 支付流程

1. **选择账单**: 用户勾选需要支付的账单
2. **确认金额**: 系统自动计算选中账单的总金额
3. **发起支付**: 点击"去支付"按钮
4. **支付处理**: 
   - 如果返回支付链接，跳转到支付页面
   - 如果直接成功，显示成功提示
   - 如果失败，显示错误信息
5. **完成处理**:
   - 扫码进入：支付成功后关闭浏览器
   - 正常进入：支付成功后返回上一页

## 技术细节

### 状态管理
- 使用 `ref` 管理页面状态
- 使用 `computed` 计算衍生状态
- 响应式数据更新

### 错误处理
- API 请求错误捕获
- 用户操作验证
- 友好的错误提示

### 类型安全
- 完整的 TypeScript 类型定义
- API 接口类型约束
- 组件属性类型检查

## 使用场景

1. **扫码缴费**: 通过二维码扫描进入，完成缴费后自动关闭
2. **系统内缴费**: 从系统内部跳转，完成后返回上一页
3. **批量缴费**: 选择多个账单一次性完成支付
4. **单笔缴费**: 选择单个账单进行支付

## 注意事项

1. **账单筛选**: 只显示状态为"待收"且可支付的账单
2. **金额精度**: 所有金额计算保持精度，使用 `formatMoney` 格式化显示
3. **状态同步**: 支付完成后需要重新获取账单状态
4. **网络处理**: 网络请求失败时提供重试机制
5. **安全考虑**: 支付金额和账单ID在服务端再次验证 
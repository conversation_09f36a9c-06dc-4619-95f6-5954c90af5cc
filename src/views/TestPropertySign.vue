<template>
  <div class="test-property-sign">
    <van-nav-bar title="物业签字流程测试" left-arrow @click-left="$router.back()" />
    
    <div class="content">
      <!-- 测试说明 -->
      <van-cell-group>
        <van-cell title="测试说明" value="模拟完整的物业签字流程" />
        <van-cell title="当前状态" :value="currentStep" />
      </van-cell-group>

      <!-- 步骤指示器 -->
      <div class="step-indicator">
        <van-steps :active="activeStep" active-color="#3583FF">
          <van-step>填写数据</van-step>
          <van-step>签字上传</van-step>
          <van-step>保存确认</van-step>
          <van-step>完成</van-step>
        </van-steps>
      </div>

      <!-- 模拟数据填写 -->
      <div v-if="activeStep === 0" class="form-section">
        <h3>1. 填写测试数据</h3>
        <van-form>
          <van-field 
            label="出场单ID" 
            placeholder="请输入出场单ID" 
            v-model="testData.exitId"
            required />
          
          <van-field 
            label="房间名称" 
            placeholder="请输入房间名称" 
            v-model="testData.roomName"
            required />
            
          <van-field 
            label="电费欠费" 
            placeholder="请输入电费欠费" 
            type="number"
            v-model="testData.elecFee" />
            
          <van-field 
            label="水费欠费" 
            placeholder="请输入水费欠费" 
            type="number"
            v-model="testData.waterFee" />
            
          <van-field 
            label="物业欠费" 
            placeholder="请输入物业欠费" 
            type="number"
            v-model="testData.pmFee" />
        </van-form>
        
        <van-button 
          type="primary" 
          block 
          @click="nextStep"
          :disabled="!testData.exitId || !testData.roomName"
          style="margin-top: 20px;">
          下一步：签字上传
        </van-button>
      </div>

      <!-- 签字上传 -->
      <div v-if="activeStep === 1" class="signature-section">
        <h3>2. 手写签字</h3>
        <SignatureCanvas 
          @confirm="onSignatureConfirm"
          @cancel="prevStep"
        />
      </div>

      <!-- 保存确认 -->
      <div v-if="activeStep === 2" class="confirm-section">
        <h3>3. 确认保存</h3>
        
        <van-cell-group>
          <van-cell title="签字图片" :value="signatureUrl ? '已上传' : '未上传'" />
          <van-cell title="出场单ID" :value="testData.exitId" />
          <van-cell title="房间名称" :value="testData.roomName" />
          <van-cell title="总欠费" :value="`¥${totalFees}`" />
        </van-cell-group>

        <div class="actions" style="margin-top: 20px;">
          <van-button 
            type="default" 
            @click="prevStep"
            style="margin-right: 10px;">
            返回修改
          </van-button>
          <van-button 
            type="primary" 
            @click="savePropertySign"
            :loading="saving">
            {{ saving ? '保存中...' : '确认保存' }}
          </van-button>
        </div>
      </div>

      <!-- 完成 -->
      <div v-if="activeStep === 3" class="result-section">
        <h3>4. 保存完成</h3>
        
        <van-cell-group>
          <van-cell title="保存状态" value="成功" />
          <van-cell title="响应信息" :value="saveResult?.msg || '操作成功'" />
          <van-cell v-if="saveResult?.data" title="返回数据" :value="JSON.stringify(saveResult.data)" />
        </van-cell-group>

        <van-button 
          type="primary" 
          block 
          @click="resetTest"
          style="margin-top: 20px;">
          重新测试
        </van-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { showToast } from 'vant'
import SignatureCanvas from '../components/SignatureCanvas.vue'
import { savePropertySign as savePropertySignAPI, type ExitPropertySignDTO } from '../api/exit'
import { getToken } from '../utils/auth'

// 当前步骤
const activeStep = ref(0)
const saving = ref(false)

// 测试数据
const testData = ref({
  exitId: '',
  roomName: '',
  elecFee: 0,
  waterFee: 0,
  pmFee: 0
})

// 签字URL
const signatureUrl = ref('')

// 保存结果
const saveResult = ref<any>(null)

// 当前状态描述
const currentStep = computed(() => {
  const steps = [
    '填写测试数据',
    '手写签字上传',
    '确认保存数据', 
    '流程完成'
  ]
  return steps[activeStep.value] || '未知状态'
})

// 总费用
const totalFees = computed(() => {
  return (testData.value.elecFee || 0) + 
         (testData.value.waterFee || 0) + 
         (testData.value.pmFee || 0)
})

// 下一步
const nextStep = () => {
  if (activeStep.value < 3) {
    activeStep.value++
  }
}

// 上一步
const prevStep = () => {
  if (activeStep.value > 0) {
    activeStep.value--
  }
}

// 签字确认
const onSignatureConfirm = (imageUrl: string) => {
  signatureUrl.value = imageUrl
  console.log('🖊️ 签字上传成功:', imageUrl)
  
  showToast({
    type: 'success',
    message: '签字上传成功'
  })
  
  nextStep()
}

// 保存物业签字
const savePropertySign = async () => {
  try {
    saving.value = true
    
    // 构造测试数据
    const signData: ExitPropertySignDTO = {
      exitId: testData.value.exitId,
      exitRoomList: [{
        id: 'test-room-1',
        exitId: testData.value.exitId,
        roomId: 'room-1',
        roomName: testData.value.roomName,
        exitDate: new Date().toISOString(),
        // 基础必需字段
        doorWindowStatus: 1,
        doorWindowPenalty: 0,
        keyHandoverStatus: 1,
        keyPenalty: 0,
        cleaningStatus: 1,
        cleaningPenalty: 0,
        // 财务部数据
        elecMeterReading: 100,
        coldWaterReading: 50,
        hotWaterReading: 30,
        elecFee: parseFloat(testData.value.elecFee.toString()) || 0,
        waterFee: parseFloat(testData.value.waterFee.toString()) || 0,
        pmFee: parseFloat(testData.value.pmFee.toString()) || 0,
        isFinanceConfirmed: true,
        financeConfirmBy: getToken() || 'test-user',
        financeConfirmByName: '测试用户',
        financeConfirmTime: new Date().toISOString(),
        financeConfirmSignature: signatureUrl.value,
        exitRoomAssetsList: [
          {
            id: 'asset-1',
            exitId: testData.value.exitId,
            exitRoomId: 'test-room-1',
            category: 1,
            name: '测试资产',
            count: 1,
            status: 1, // 完好
            penalty: 0
          }
        ],
        isSubmit: true
      }],
      signatureUrl: signatureUrl.value,
      signType: 1, // 财务确认
      userId: getToken() || 'test-user',
      userName: '测试用户'
    }

    console.log('📋 测试提交数据:', signData)

    const response = await savePropertySignAPI(signData)
    console.log('✅ 保存响应:', response)
    
    saveResult.value = response
    
    showToast({
      type: 'success',
      message: '保存成功'
    })
    
    nextStep()
  } catch (error) {
    console.error('❌ 保存失败:', error)
    
    let errorMessage = '保存失败'
    if (error && typeof error === 'object' && 'message' in error) {
      errorMessage = (error as Error).message
    }
    
    showToast({
      type: 'fail',
      message: errorMessage
    })
  } finally {
    saving.value = false
  }
}

// 重置测试
const resetTest = () => {
  activeStep.value = 0
  testData.value = {
    exitId: '',
    roomName: '',
    elecFee: 0,
    waterFee: 0,
    pmFee: 0
  }
  signatureUrl.value = ''
  saveResult.value = null
  saving.value = false
}
</script>

<style scoped>
.test-property-sign {
  min-height: 100vh;
  background: #f5f5f5;
}

.content {
  padding: 20px;
}

.step-indicator {
  margin: 30px 0;
  background: white;
  padding: 20px;
  border-radius: 12px;
}

.form-section,
.signature-section,
.confirm-section,
.result-section {
  background: white;
  padding: 20px;
  border-radius: 12px;
  margin: 20px 0;
}

.form-section h3,
.signature-section h3,
.confirm-section h3,
.result-section h3 {
  margin: 0 0 20px 0;
  font-size: 18px;
  color: #333;
}

.actions {
  display: flex;
  gap: 12px;
}

.actions .van-button {
  flex: 1;
}
</style> 
# 定单缴费页面使用说明

## 概述

定单缴费页面是万洋资管平台的核心功能之一，支持客户在线查看定单详情并完成缴费操作。

## 功能特性

### ✅ 已实现功能

1. **定单详情查看**
   - 显示定单基本信息（定单号、预定人、应收日期等）
   - 显示房源信息（租赁单元名称、物业类型等）
   - 显示费用明细（账单总额、优惠金额、实际应收金额）
   - 显示定单状态（草稿、待收费、已生效、已转签、已作废）

2. **支付功能**
   - 支付金额确认
   - 租赁定价协议展示
   - 协议同意确认
   - 支付处理（生成支付链接或直接处理）

3. **业务逻辑校验**
   - 定单状态校验（只允许待收费状态的定单进行缴费）
   - 支付权限校验（检查 `canPay` 字段）
   - 协议同意验证

4. **套打功能**
   - 支持生成定单套打文件
   - 异步处理和进度提示

5. **用户体验优化**
   - 加载状态提示
   - 错误处理和友好提示
   - 响应式设计适配移动端

## 页面路由

```
/order-payment?id={bookingId}
```

### 参数说明
- `id`: 定单ID（必需）

## API 接口

### 1. 获取定单详情
```typescript
GET /booking/detail?id={bookingId}
```

### 2. 定单支付
```typescript
POST /booking/pay
{
  "bookingId": "string",
  "amount": number
}
```

### 3. 定单套打
```typescript
GET /booking/print?id={bookingId}
```

## 数据结构

### 定单详情响应 (BookingDetailVo)
```typescript
interface BookingDetailVo {
  booking: BookingVo;        // 定单基本信息
  cost: CostVo;              // 账单信息
  flowRelList: CostFlowRelVo[];  // 流水关系列表
  flowLogList: CostFlowLogVo[];  // 流水记录列表
}
```

### 关键字段说明
- `booking.status`: 定单状态（0-草稿，1-待收费，2-已生效，3-已转签，4-已作废）
- `cost.canPay`: 是否可缴费
- `cost.actualReceivable`: 实际应收金额
- `cost.totalAmount`: 账单总额
- `cost.discountAmount`: 优惠金额

## 状态管理

### 定单状态
- **草稿 (0)**: 定单创建但未提交
- **待收费 (1)**: 可以进行缴费操作
- **已生效 (2)**: 已完成缴费
- **已转签 (3)**: 已转为正式合同
- **已作废 (4)**: 定单已取消

### 账单状态
- **待收 (0)**: 等待收款
- **待付 (1)**: 等待付款  
- **已收 (2)**: 已收款完成
- **已付 (3)**: 已付款完成

## 开发测试

### 模拟数据
开发环境自动启用模拟数据，测试定单ID: `test-booking-123`

### 测试流程
1. 从首页点击"定单缴费"进入页面
2. 查看定单详情是否正确显示
3. 测试协议同意功能
4. 测试支付流程
5. 测试套打功能

## 错误处理

### 常见错误场景
1. **缺少定单ID**: 显示提示并返回上一页
2. **定单不存在**: 显示错误提示并返回
3. **定单状态不允许缴费**: 显示状态提示并返回
4. **网络请求失败**: 显示错误提示
5. **支付处理失败**: 显示具体错误信息

### 错误提示类型
- Toast 提示：用于简单的成功/失败消息
- Loading 提示：用于异步操作进度
- Dialog 确认：用于重要操作确认

## 样式设计

### 设计特点
- 渐变背景设计
- 卡片式布局
- 状态徽章显示
- 底部固定支付栏
- 响应式适配

### 主要颜色
- 主色调：`#3583FF` 
- 强调色：`#FF6900`（金额显示）
- 成功色：`#07c160`（优惠金额）
- 文本色：`#6e6e77`、`#919199`

## 技术实现

### 核心技术栈
- Vue 3 + TypeScript
- Vant UI 组件库
- Vue Router 路由管理
- Axios HTTP 请求

### 关键实现
- Composition API 状态管理
- 计算属性动态数据绑定
- 异步数据加载和错误处理
- 模拟数据支持开发测试

## 部署说明

### 环境变量配置
```bash
# API 基础地址
VITE_API_BASE_URL=https://api.example.com

# 是否启用模拟数据
VITE_USE_MOCK=false
```

### 构建命令
```bash
# 开发环境
npm run dev

# 生产构建
npm run build
```

## 维护指南

### 新增字段
1. 更新 TypeScript 接口定义
2. 修改页面显示逻辑
3. 更新模拟数据
4. 测试功能正常

### 状态扩展
1. 更新状态枚举定义
2. 添加状态文本映射
3. 更新样式类定义
4. 测试状态显示

### 接口变更
1. 更新 API 函数签名
2. 修改请求/响应处理逻辑
3. 更新错误处理
4. 回归测试 
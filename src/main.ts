import { createApp } from 'vue'
import './style.css'
import App from './App.vue'

// 引入 flexible 适配
import 'amfe-flexible'

// 引入路由
import router from './router'

// ✅ 新增：引入 Vant 及样式
import Vant from 'vant'
import 'vant/lib/index.css'

import VConsole from 'vconsole'
new VConsole()

// 设置Vant组件库的rem适配 (设计稿为750px)
import { ConfigProvider } from 'vant'
ConfigProvider.props.themeVars.default = () => ({
  // 设置Vant主题样式变量
  // 按钮相关
  buttonNormalFontSize: '0.43rem',
  buttonLargeFontSize: '0.45rem',
  buttonSmallFontSize: '0.37rem',
  buttonMiniPadding: '0 0.16rem',
  buttonDefaultPadding: '0 0.43rem',
  buttonDefaultHeight: '1.17rem',
  buttonDefaultFontSize: '0.43rem',
  
  // 导航栏相关
  navBarHeight: '1.2rem',
  navBarTitleFontSize: '0.48rem',
  navBarArrowSize: '0.48rem',
  
  // 复选框相关
  checkboxFontSize: '0.35rem',
  checkboxSize: '0.53rem',
})

const app = createApp(App)

app.use(Vant)        // 注册 Vant 插件
app.use(router)      // 注册路由
app.mount('#app')    // 挂载应用

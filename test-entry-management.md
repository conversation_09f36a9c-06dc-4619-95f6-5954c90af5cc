# 进场管理页面测试指南

## 测试地址
- 开发环境：`http://localhost:5173/entry-management`
- 路由名称：`EntryManagement`

## 测试功能点

### 1. 页面基础功能
- [x] 页面正常加载
- [x] 导航栏显示"进场管理"
- [x] 搜索框正常显示
- [x] 状态标签栏（待办理/已办理）
- [x] 时间筛选下拉框（近3天/近7天/近30天/全部）
- [x] 排序筛选下拉框
- [x] 更多筛选按钮

### 2. 列表显示功能
- [x] 列表项正常显示
- [x] 承租方名称显示
- [x] 状态标识显示
- [x] 合同编号显示
- [x] 租期显示
- [x] 未进场房源数显示
- [x] "办理进场"按钮显示（仅待办理状态）

### 3. 房源选择弹框功能
- [x] 点击"办理进场"按钮弹出选择框
- [x] 弹框标题"选择进场房源"
- [x] 关闭按钮功能
- [x] 搜索框功能
- [x] 房源列表显示
- [x] 全选功能
- [x] 单个房源选择功能
- [x] 确定按钮状态变化
- [x] 选择数量显示

### 4. 交互功能测试

#### 4.1 搜索功能
```
1. 在搜索框输入"101"
2. 验证：列表只显示包含"101"的房源
3. 清空搜索框
4. 验证：恢复完整房源列表
```

#### 4.2 全选功能
```
1. 点击"全选"按钮
2. 验证：所有房源被选中
3. 再次点击"全选"按钮
4. 验证：所有房源取消选中
```

#### 4.3 单选功能
```
1. 点击任意房源
2. 验证：该房源被选中
3. 点击已选中房源
4. 验证：该房源取消选中
```

#### 4.4 确认流程
```
1. 选择一个或多个房源
2. 点击"确定"按钮
3. 验证：弹出确认对话框
4. 点击"确定"
5. 验证：调用API，显示成功提示，关闭弹框
```

### 5. 错误处理测试

#### 5.1 未选择房源
```
1. 不选择任何房源
2. 点击"确定"按钮
3. 验证：显示"请选择要办理进场的房源"提示
```

#### 5.2 API异常处理
```
1. 模拟网络异常
2. 验证：显示错误提示，使用模拟数据降级
```

## 预期结果

### 正常流程
1. 页面加载正常，显示进场管理列表
2. 点击"办理进场"按钮打开房源选择弹框
3. 搜索、选择房源功能正常
4. 确认办理进场成功，页面更新

### 异常处理
1. API异常时使用模拟数据
2. 用户操作错误时给出明确提示
3. 网络问题时有友好的错误提示

## 模拟数据
当API不可用时，系统会使用以下模拟数据：
- 北区-56幢-101
- 北区-56幢-102  
- 北区-56幢-103
- 北区-57幢-101
- 北区-57幢-102

## 注意事项
1. 确保已启动开发服务器
2. 检查浏览器控制台是否有错误信息
3. 验证API接口返回数据格式是否正确
4. 测试不同屏幕尺寸下的响应式效果 